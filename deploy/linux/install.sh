#!/bin/bash

# SEO Platform 安装脚本
# Installation script for SEO Platform

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
APP_NAME="seo-platform"
APP_USER="seo-platform"
APP_DIR="/opt/seo-platform"
SERVICE_FILE="/etc/systemd/system/seo-platform.service"
BINARY_NAME="seo-platform-linux-amd64"

echo -e "${BLUE}🚀 SEO Platform 安装程序${NC}"
echo -e "${BLUE}================================${NC}"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ 请以root权限运行此脚本${NC}"
    echo -e "${YELLOW}💡 使用: sudo ./install.sh${NC}"
    exit 1
fi

# 检查可执行文件是否存在
if [ ! -f "$BINARY_NAME" ]; then
    echo -e "${RED}❌ 找不到可执行文件: $BINARY_NAME${NC}"
    exit 1
fi

echo -e "${YELLOW}🔄 开始安装...${NC}"

# 1. 创建应用用户
if ! id "$APP_USER" &>/dev/null; then
    echo -e "${YELLOW}👤 创建应用用户: $APP_USER${NC}"
    useradd --system --shell /bin/false --home-dir "$APP_DIR" --create-home "$APP_USER"
else
    echo -e "${GREEN}✅ 用户 $APP_USER 已存在${NC}"
fi

# 2. 创建应用目录
echo -e "${YELLOW}📁 创建应用目录: $APP_DIR${NC}"
mkdir -p "$APP_DIR"

# 3. 复制文件
echo -e "${YELLOW}📋 复制应用文件...${NC}"
cp "$BINARY_NAME" "$APP_DIR/"
cp start.sh "$APP_DIR/" 2>/dev/null || true
cp stop.sh "$APP_DIR/" 2>/dev/null || true
cp README.md "$APP_DIR/" 2>/dev/null || true

# 4. 设置权限
echo -e "${YELLOW}🔐 设置文件权限...${NC}"
chown -R "$APP_USER:$APP_USER" "$APP_DIR"
chmod +x "$APP_DIR/$BINARY_NAME"
chmod +x "$APP_DIR/start.sh" 2>/dev/null || true
chmod +x "$APP_DIR/stop.sh" 2>/dev/null || true

# 5. 创建systemd服务文件
echo -e "${YELLOW}⚙️  创建系统服务...${NC}"
cat > "$SERVICE_FILE" << EOF
[Unit]
Description=SEO Platform Service
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/$BINARY_NAME
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=DB_HOST=localhost
Environment=DB_PORT=3306
Environment=DB_USER=root
Environment=DB_PASSWORD=${DB_PASSWORD:-your_mysql_password}
Environment=DB_NAME=seo_platform_auto
Environment=SERVER_PORT=8081
Environment=SERVER_HOST=0.0.0.0

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR

[Install]
WantedBy=multi-user.target
EOF

# 6. 重载systemd并启用服务
echo -e "${YELLOW}🔄 重载systemd配置...${NC}"
systemctl daemon-reload

echo -e "${YELLOW}✅ 启用开机自启动...${NC}"
systemctl enable seo-platform

# 7. 创建日志目录
echo -e "${YELLOW}📝 创建日志目录...${NC}"
mkdir -p /var/log/seo-platform
chown "$APP_USER:$APP_USER" /var/log/seo-platform

# 8. 检查防火墙设置
echo -e "${YELLOW}🔥 检查防火墙设置...${NC}"
if command -v ufw &> /dev/null; then
    if ufw status | grep -q "Status: active"; then
        echo -e "${BLUE}🔓 配置UFW防火墙规则...${NC}"
        ufw allow 8081/tcp
    fi
elif command -v firewall-cmd &> /dev/null; then
    if systemctl is-active --quiet firewalld; then
        echo -e "${BLUE}🔓 配置firewalld防火墙规则...${NC}"
        firewall-cmd --permanent --add-port=8081/tcp
        firewall-cmd --reload
    fi
fi

echo -e "${GREEN}✅ 安装完成!${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "${GREEN}🎉 SEO Platform 已成功安装为系统服务${NC}"
echo ""
echo -e "${BLUE}📋 管理命令:${NC}"
echo -e "   启动服务: ${GREEN}sudo systemctl start seo-platform${NC}"
echo -e "   停止服务: ${GREEN}sudo systemctl stop seo-platform${NC}"
echo -e "   重启服务: ${GREEN}sudo systemctl restart seo-platform${NC}"
echo -e "   查看状态: ${GREEN}sudo systemctl status seo-platform${NC}"
echo -e "   查看日志: ${GREEN}sudo journalctl -u seo-platform -f${NC}"
echo ""
echo -e "${BLUE}🌐 访问信息:${NC}"
echo -e "   地址: ${GREEN}http://your-server-ip:8081${NC}"
echo -e "   用户名: ${GREEN}admin${NC}"
echo -e "   密码: ${GREEN}请手动创建管理员用户${NC}"
echo ""
echo -e "${YELLOW}⚠️  重要提醒:${NC}"
echo -e "1. 请修改 $SERVICE_FILE 中的数据库配置"
echo -e "2. 首次登录后请立即修改默认密码"
echo -e "3. 建议配置HTTPS和反向代理"
echo ""
echo -e "${BLUE}🚀 现在可以启动服务:${NC}"
echo -e "${GREEN}sudo systemctl start seo-platform${NC}"
