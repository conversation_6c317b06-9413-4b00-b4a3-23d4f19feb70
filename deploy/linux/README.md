# SEO Platform - Linux AMD64 部署指南

## 📦 部署包内容

```
seo-platform-linux-amd64    # 主程序可执行文件
README.md                    # 本说明文件
start.sh                     # 启动脚本
stop.sh                      # 停止脚本
install.sh                   # 安装脚本
```

## 🚀 快速部署

### 1. 系统要求
- **操作系统**: Linux (AMD64架构)
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+
- **内存**: 最少 512MB RAM
- **磁盘**: 最少 100MB 可用空间

### 2. 安装MySQL (如果未安装)

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

**CentOS/RHEL:**
```bash
sudo yum install mysql-server
# 或者对于较新版本
sudo dnf install mysql-server
sudo systemctl start mysqld
sudo mysql_secure_installation
```

### 3. 创建数据库用户

```sql
# 登录MySQL
mysql -u root -p

# 创建数据库用户
CREATE USER 'seo_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON *.* TO 'seo_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. 部署应用

```bash
# 1. 解压部署包
tar -xzf seo-platform-linux-amd64.tar.gz
cd seo-platform-linux

# 2. 运行安装脚本
chmod +x install.sh
sudo ./install.sh

# 3. 启动应用
./start.sh
```

## ⚙️ 配置说明

### 环境变量配置

应用支持以下环境变量：

```bash
# 数据库配置
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=seo_user
export DB_PASSWORD=your_password
export DB_NAME=seo_platform_auto

# 服务器配置
export SERVER_PORT=8081
export SERVER_HOST=0.0.0.0

# JWT配置
export JWT_SECRET=your_jwt_secret_key
```

### 配置文件方式

创建 `config.yaml` 文件：

```yaml
database:
  host: localhost
  port: 3306
  user: seo_user
  password: your_password
  name: seo_platform_auto

server:
  port: 8081
  host: 0.0.0.0

jwt:
  secret: your_jwt_secret_key
```

## 🔧 运行方式

### 方式1: 直接运行
```bash
./seo-platform-linux-amd64
```

### 方式2: 使用启动脚本
```bash
./start.sh
```

### 方式3: 系统服务 (推荐生产环境)
```bash
sudo ./install.sh  # 安装为系统服务
sudo systemctl start seo-platform
sudo systemctl enable seo-platform  # 开机自启
```

## 📋 默认登录信息

- **访问地址**: `http://your-server-ip:8081`
- **用户名**: `admin`
- **密码**: 请手动创建管理员用户

⚠️ **重要**: 首次登录后请立即修改默认密码！

## 🛠️ 管理命令

```bash
# 启动服务
./start.sh
sudo systemctl start seo-platform

# 停止服务
./stop.sh
sudo systemctl stop seo-platform

# 重启服务
sudo systemctl restart seo-platform

# 查看状态
sudo systemctl status seo-platform

# 查看日志
sudo journalctl -u seo-platform -f
```

## 🔍 故障排除

### 1. 端口被占用
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8081
# 或者
sudo ss -tlnp | grep :8081

# 修改端口
export SERVER_PORT=8082
```

### 2. 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -h localhost -u seo_user -p
```

### 3. 权限问题
```bash
# 给予执行权限
chmod +x seo-platform-linux-amd64

# 检查防火墙
sudo ufw status
sudo firewall-cmd --list-ports
```

## 📊 性能优化

### 1. 系统级优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf
```

### 2. MySQL优化
```sql
# 在 /etc/mysql/mysql.conf.d/mysqld.cnf 中添加
[mysqld]
innodb_buffer_pool_size = 256M
max_connections = 200
query_cache_size = 64M
```

## 🔒 安全建议

1. **修改默认密码**
2. **配置防火墙**
3. **使用HTTPS** (配置反向代理)
4. **定期备份数据库**
5. **监控系统资源**

## 📞 技术支持

如遇问题，请检查：
1. 系统日志: `sudo journalctl -u seo-platform`
2. 应用日志: 查看控制台输出
3. 数据库连接: 确认MySQL服务正常
4. 网络连接: 确认端口未被防火墙阻止
