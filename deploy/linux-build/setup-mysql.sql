-- SEO Platform MySQL 数据库设置脚本
-- 请在MySQL中执行此脚本来创建数据库和用户

-- 如果需要重新创建数据库，请取消注释以下行
-- DROP DATABASE IF EXISTS seo_platform;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS seo_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户（可选，推荐用于生产环境）
-- CREATE USER 'seo_user'@'localhost' IDENTIFIED BY 'your_secure_password';
-- GRANT ALL PRIVILEGES ON seo_platform.* TO 'seo_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 使用数据库
USE seo_platform;

-- 显示数据库信息
SELECT 'Database seo_platform created successfully!' as message;
SHOW DATABASES LIKE 'seo_platform';
