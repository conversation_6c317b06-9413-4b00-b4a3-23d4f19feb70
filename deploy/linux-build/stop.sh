#!/bin/bash

# SEO Platform 停止脚本
# Stop script for SEO Platform

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 应用配置
APP_NAME="seo-platform"
PID_FILE="/tmp/seo-platform.pid"
LOG_FILE="/tmp/seo-platform.log"

echo -e "${BLUE}🛑 停止 SEO Platform...${NC}"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo -e "${YELLOW}⚠️  PID文件不存在，应用可能未运行${NC}"
    
    # 尝试通过进程名查找
    PIDS=$(pgrep -f "seo-platform-linux-amd64" || true)
    if [ -n "$PIDS" ]; then
        echo -e "${YELLOW}🔍 发现运行中的进程，尝试停止...${NC}"
        for pid in $PIDS; do
            echo -e "${YELLOW}🔄 停止进程 $pid${NC}"
            kill "$pid" 2>/dev/null || true
            sleep 2
            
            # 如果进程仍在运行，强制杀死
            if ps -p "$pid" > /dev/null 2>&1; then
                echo -e "${RED}⚡ 强制停止进程 $pid${NC}"
                kill -9 "$pid" 2>/dev/null || true
            fi
        done
        echo -e "${GREEN}✅ 所有相关进程已停止${NC}"
    else
        echo -e "${GREEN}✅ 没有发现运行中的 SEO Platform 进程${NC}"
    fi
    exit 0
fi

# 读取PID
PID=$(cat "$PID_FILE")

# 检查进程是否存在
if ! ps -p "$PID" > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  进程 $PID 不存在，清理PID文件${NC}"
    rm -f "$PID_FILE"
    echo -e "${GREEN}✅ SEO Platform 已停止${NC}"
    exit 0
fi

echo -e "${YELLOW}🔄 正在停止进程 $PID...${NC}"

# 优雅停止
kill "$PID" 2>/dev/null || true

# 等待进程停止
WAIT_TIME=0
MAX_WAIT=10

while [ $WAIT_TIME -lt $MAX_WAIT ]; do
    if ! ps -p "$PID" > /dev/null 2>&1; then
        break
    fi
    sleep 1
    WAIT_TIME=$((WAIT_TIME + 1))
    echo -e "${YELLOW}⏳ 等待进程停止... ($WAIT_TIME/$MAX_WAIT)${NC}"
done

# 检查进程是否已停止
if ps -p "$PID" > /dev/null 2>&1; then
    echo -e "${RED}⚡ 进程未响应，强制停止...${NC}"
    kill -9 "$PID" 2>/dev/null || true
    sleep 2
    
    if ps -p "$PID" > /dev/null 2>&1; then
        echo -e "${RED}❌ 无法停止进程 $PID${NC}"
        exit 1
    fi
fi

# 清理PID文件
rm -f "$PID_FILE"

echo -e "${GREEN}✅ SEO Platform 已成功停止${NC}"

# 显示日志文件位置
if [ -f "$LOG_FILE" ]; then
    echo -e "${BLUE}📝 日志文件: $LOG_FILE${NC}"
    echo -e "${BLUE}💡 查看最后几行日志: tail $LOG_FILE${NC}"
fi
