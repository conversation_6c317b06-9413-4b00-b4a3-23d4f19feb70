#!/bin/bash

# SEO Platform 系统要求检查脚本
# System requirements check script for SEO Platform

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 SEO Platform 系统要求检查${NC}"
echo -e "${BLUE}================================${NC}"

# 检查操作系统
echo -e "${YELLOW}📋 检查操作系统...${NC}"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS_NAME=$(lsb_release -d 2>/dev/null | cut -f2 || echo "Linux")
    ARCH=$(uname -m)
    echo -e "${GREEN}✅ 操作系统: $OS_NAME ($ARCH)${NC}"
    
    if [[ "$ARCH" != "x86_64" ]]; then
        echo -e "${RED}❌ 警告: 当前架构 $ARCH，推荐使用 x86_64${NC}"
    fi
else
    echo -e "${RED}❌ 不支持的操作系统: $OSTYPE${NC}"
    exit 1
fi

# 检查内存
echo -e "${YELLOW}💾 检查内存...${NC}"
MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
MEMORY_MB=$((MEMORY_KB / 1024))
echo -e "${BLUE}📊 总内存: ${MEMORY_MB}MB${NC}"

if [ $MEMORY_MB -lt 512 ]; then
    echo -e "${RED}❌ 内存不足: ${MEMORY_MB}MB < 512MB (最低要求)${NC}"
    exit 1
elif [ $MEMORY_MB -lt 1024 ]; then
    echo -e "${YELLOW}⚠️  内存较低: ${MEMORY_MB}MB，推荐1GB以上${NC}"
else
    echo -e "${GREEN}✅ 内存充足: ${MEMORY_MB}MB${NC}"
fi

# 检查磁盘空间
echo -e "${YELLOW}💽 检查磁盘空间...${NC}"
DISK_AVAILABLE=$(df . | tail -1 | awk '{print $4}')
DISK_AVAILABLE_MB=$((DISK_AVAILABLE / 1024))
echo -e "${BLUE}📊 可用空间: ${DISK_AVAILABLE_MB}MB${NC}"

if [ $DISK_AVAILABLE_MB -lt 100 ]; then
    echo -e "${RED}❌ 磁盘空间不足: ${DISK_AVAILABLE_MB}MB < 100MB (最低要求)${NC}"
    exit 1
else
    echo -e "${GREEN}✅ 磁盘空间充足: ${DISK_AVAILABLE_MB}MB${NC}"
fi

# 检查MySQL/MariaDB
echo -e "${YELLOW}🗄️  检查数据库...${NC}"
if command -v mysql &> /dev/null; then
    MYSQL_VERSION=$(mysql --version | head -1)
    echo -e "${GREEN}✅ MySQL已安装: $MYSQL_VERSION${NC}"
    
    # 测试连接
    if mysql -u root -p"${DB_PASSWORD:-your_mysql_password}" -e "SELECT 1;" &>/dev/null; then
        echo -e "${GREEN}✅ MySQL连接测试成功${NC}"
    else
        echo -e "${YELLOW}⚠️  MySQL连接测试失败，请检查用户名密码${NC}"
    fi
elif command -v mariadb &> /dev/null; then
    MARIADB_VERSION=$(mariadb --version | head -1)
    echo -e "${GREEN}✅ MariaDB已安装: $MARIADB_VERSION${NC}"
else
    echo -e "${RED}❌ 未找到MySQL或MariaDB${NC}"
    echo -e "${YELLOW}💡 安装命令:${NC}"
    echo -e "   Ubuntu/Debian: sudo apt install mysql-server"
    echo -e "   CentOS/RHEL: sudo yum install mysql-server"
    exit 1
fi

# 检查端口占用
echo -e "${YELLOW}🌐 检查端口占用...${NC}"
if netstat -tlnp 2>/dev/null | grep -q ":8081 "; then
    echo -e "${YELLOW}⚠️  端口8081已被占用${NC}"
    echo -e "${BLUE}💡 可以通过环境变量修改端口: export SERVER_PORT=8082${NC}"
else
    echo -e "${GREEN}✅ 端口8081可用${NC}"
fi

# 检查防火墙
echo -e "${YELLOW}🔥 检查防火墙...${NC}"
if command -v ufw &> /dev/null; then
    if ufw status | grep -q "Status: active"; then
        echo -e "${YELLOW}⚠️  UFW防火墙已启用${NC}"
        echo -e "${BLUE}💡 需要开放端口: sudo ufw allow 8081${NC}"
    else
        echo -e "${GREEN}✅ UFW防火墙未启用${NC}"
    fi
elif command -v firewall-cmd &> /dev/null; then
    if systemctl is-active --quiet firewalld; then
        echo -e "${YELLOW}⚠️  firewalld防火墙已启用${NC}"
        echo -e "${BLUE}💡 需要开放端口: sudo firewall-cmd --permanent --add-port=8081/tcp${NC}"
    else
        echo -e "${GREEN}✅ firewalld防火墙未启用${NC}"
    fi
else
    echo -e "${GREEN}✅ 未检测到常见防火墙${NC}"
fi

# 检查systemd
echo -e "${YELLOW}⚙️  检查systemd...${NC}"
if command -v systemctl &> /dev/null; then
    echo -e "${GREEN}✅ systemd可用${NC}"
else
    echo -e "${YELLOW}⚠️  systemd不可用，无法安装为系统服务${NC}"
fi

# 检查权限
echo -e "${YELLOW}🔐 检查权限...${NC}"
if [ "$EUID" -eq 0 ]; then
    echo -e "${GREEN}✅ 当前为root权限${NC}"
else
    echo -e "${YELLOW}⚠️  当前非root权限${NC}"
    echo -e "${BLUE}💡 安装系统服务需要root权限: sudo ./install.sh${NC}"
fi

echo -e "${BLUE}================================${NC}"
echo -e "${GREEN}🎉 系统要求检查完成!${NC}"
echo ""
echo -e "${BLUE}📋 下一步操作:${NC}"
echo -e "1. ${GREEN}直接运行${NC}: ./start.sh"
echo -e "2. ${GREEN}安装为系统服务${NC}: sudo ./install.sh"
echo -e "3. ${GREEN}查看详细说明${NC}: cat README.md"
