#!/bin/bash

# SEO Platform 启动脚本
# Start script for SEO Platform

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 应用配置
APP_NAME="seo-platform"
APP_BINARY="./seo-platform-linux-amd64"
PID_FILE="/tmp/seo-platform.pid"
LOG_FILE="/tmp/seo-platform.log"

# 检查可执行文件是否存在
if [ ! -f "$APP_BINARY" ]; then
    echo -e "${RED}❌ 错误: 找不到可执行文件 $APP_BINARY${NC}"
    echo -e "${YELLOW}💡 请确保在正确的目录中运行此脚本${NC}"
    exit 1
fi

# 检查是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  SEO Platform 已经在运行 (PID: $PID)${NC}"
        echo -e "${BLUE}🌐 访问地址: http://localhost:8081${NC}"
        exit 0
    else
        echo -e "${YELLOW}🧹 清理过期的PID文件${NC}"
        rm -f "$PID_FILE"
    fi
fi

# 设置默认环境变量
export DB_HOST=${DB_HOST:-localhost}
export DB_PORT=${DB_PORT:-3306}
export DB_USER=${DB_USER:-root}
export DB_PASSWORD=${DB_PASSWORD:-your_mysql_password}
export DB_NAME=${DB_NAME:-seo_platform_auto}
export SERVER_PORT=${SERVER_PORT:-8081}
export SERVER_HOST=${SERVER_HOST:-0.0.0.0}

echo -e "${BLUE}🚀 启动 SEO Platform...${NC}"
echo -e "${BLUE}📊 配置信息:${NC}"
echo -e "   数据库: ${DB_USER}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
echo -e "   服务器: ${SERVER_HOST}:${SERVER_PORT}"

# 给予执行权限
chmod +x "$APP_BINARY"

# 启动应用
echo -e "${YELLOW}🔄 正在启动应用...${NC}"

# 后台运行并记录PID
nohup "$APP_BINARY" > "$LOG_FILE" 2>&1 &
APP_PID=$!

# 保存PID
echo "$APP_PID" > "$PID_FILE"

# 等待应用启动
sleep 3

# 检查应用是否成功启动
if ps -p "$APP_PID" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ SEO Platform 启动成功!${NC}"
    echo -e "${GREEN}🆔 进程ID: $APP_PID${NC}"
    echo -e "${GREEN}🌐 访问地址: http://localhost:${SERVER_PORT}${NC}"
    echo -e "${BLUE}📋 默认登录信息:${NC}"
    echo -e "   用户名: admin"
    echo "   密码: 请手动创建管理员用户"
    echo -e "${YELLOW}⚠️  请在首次登录后修改默认密码!${NC}"
    echo ""
    echo -e "${BLUE}📝 管理命令:${NC}"
    echo -e "   查看日志: tail -f $LOG_FILE"
    echo -e "   停止服务: ./stop.sh"
    echo -e "   重启服务: ./stop.sh && ./start.sh"
else
    echo -e "${RED}❌ 应用启动失败${NC}"
    echo -e "${YELLOW}📝 查看日志获取详细信息: cat $LOG_FILE${NC}"
    rm -f "$PID_FILE"
    exit 1
fi
