package test

import (
	"seo-platform/internal/utils"
	"testing"
)

func TestUserAgentManager(t *testing.T) {
	manager := utils.GetUserAgentManager()

	// 测试加载User-Agent文件
	err := manager.LoadFromFile("../static/data/user-agents.txt")
	if err != nil {
		t.Fatalf("Failed to load user-agents: %v", err)
	}

	// 测试获取数量
	count := manager.GetCount()
	if count == 0 {
		t.<PERSON>rror("User-Agent count should be greater than 0")
	}
	t.Logf("Loaded %d user-agents", count)

	// 测试获取随机User-Agent
	randomUA := manager.GetRandomUserAgent()
	if randomUA == "" {
		t.<PERSON>rror("Random user-agent should not be empty")
	}
	t.Logf("Random user-agent: %s", randomUA)

	// 测试根据关键词过滤
	chromeUAs := manager.FilterByKeyword("Chrome")
	t.Logf("Found %d Chrome user-agents", len(chromeUAs))

	firefoxUAs := manager.FilterByKeyword("Firefox")
	t.Logf("Found %d Firefox user-agents", len(firefoxUAs))

	// 测试根据关键词获取随机User-Agent
	chromeUA := manager.GetRandomUserAgentByKeyword("Chrome")
	if chromeUA == "" {
		t.Error("Chrome user-agent should not be empty")
	}
	t.Logf("Random Chrome user-agent: %s", chromeUA)

	// 测试根据索引获取User-Agent
	firstUA := manager.GetUserAgentByIndex(0)
	if firstUA == "" {
		t.Error("First user-agent should not be empty")
	}
	t.Logf("First user-agent: %s", firstUA)

	// 测试是否已加载
	if !manager.IsLoaded() {
		t.Error("Manager should be loaded")
	}
}

func TestGlobalUserAgentFunctions(t *testing.T) {
	// 测试全局函数
	randomUA := utils.GetRandomUserAgent()
	if randomUA == "" {
		t.Error("Global random user-agent should not be empty")
	}
	t.Logf("Global random user-agent: %s", randomUA)

	chromeUA := utils.GetRandomUserAgentByKeyword("Chrome")
	if chromeUA == "" {
		t.Error("Global Chrome user-agent should not be empty")
	}
	t.Logf("Global Chrome user-agent: %s", chromeUA)

	count := utils.GetUserAgentCount()
	if count == 0 {
		t.Error("Global user-agent count should be greater than 0")
	}
	t.Logf("Global user-agent count: %d", count)

	if !utils.IsUserAgentLoaded() {
		t.Error("Global user-agent should be loaded")
	}
}

func BenchmarkGetRandomUserAgent(b *testing.B) {
	manager := utils.GetUserAgentManager()
	manager.LoadFromFile("../static/data/user-agents.txt")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.GetRandomUserAgent()
	}
}

func BenchmarkGetRandomUserAgentByKeyword(b *testing.B) {
	manager := utils.GetUserAgentManager()
	manager.LoadFromFile("../static/data/user-agents.txt")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.GetRandomUserAgentByKeyword("Chrome")
	}
}
