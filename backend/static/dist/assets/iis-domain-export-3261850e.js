import{_ as a}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                */import{x as r}from"./elementPlus-ef333120.js";import{y as _,z as n,Q as i,I as t,A as o}from"./vendor-ad470fc0.js";const p={class:"iis-domain-export"},d={__name:"iis-domain-export",setup(l){return(c,e)=>{const s=r;return _(),n("div",p,[i(s,null,{header:t(()=>e[0]||(e[0]=[o("div",{class:"card-header"},[o("span",null,"IIS 域名导出")],-1)])),default:t(()=>[e[1]||(e[1]=o("div",{class:"content"},[o("p",null,"IIS 域名导出功能正在开发中...")],-1))]),_:1,__:[1]})])}}},v=a(d,[["__scopeId","data-v-17531488"]]);export{v as default};
