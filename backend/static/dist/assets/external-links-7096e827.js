import{_ as ne}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  */import{r as f,X as T,k as se,y as R,z as re,A as i,Q as e,I as o,a5 as ie,J as ue,H as de,u as x,M as r,O as y,C as pe}from"./vendor-ad470fc0.js";import{a as $,E as g,c as _e,h as me,s as ce,J as fe,C as ge,L as ve,a8 as be,M as ye,w as ke,I as he,B as we,A as xe,u as Ve,v as Ce,N as Ee,O as ze,P as Se,T as Te,S as Ue}from"./elementPlus-05e8f3ef.js";const Be={class:"page-container"},De={class:"page-header"},Le={class:"breadcrumb"},Re={class:"page-content"},$e={class:"table-toolbar"},Me={class:"toolbar-left"},Ie={class:"toolbar-right"},Ne={class:"pagination-container"},Fe={class:"dialog-footer"},Oe={__name:"external-links",setup(Pe){const C=f(!1),U=f([]),m=f([]),v=f(!1),k=f(!1),E=f(!1),z=f(),b=T({keyword:"",status:""}),u=T({page:1,pageSize:10,total:0}),n=T({title:"",url:"",anchor_text:"",link_type:1,target:"_blank",rel:[],description:""}),M={title:[{required:!0,message:"请输入链接标题",trigger:"blur"}],url:[{required:!0,message:"请输入链接地址",trigger:"blur"},{type:"url",message:"请输入有效的URL地址",trigger:"blur"}],anchor_text:[{required:!0,message:"请输入锚文本",trigger:"blur"}],link_type:[{required:!0,message:"请选择链接类型",trigger:"change"}]},I=a=>({1:"友情链接",2:"内容链接",3:"导航链接",4:"广告链接"})[a]||"未知",N=a=>({1:"正常",0:"异常","-1":"未检测"})[a]||"未知",F=a=>({1:"success",0:"danger","-1":"info"})[a]||"info",O=a=>a?a>=200&&a<300?"success-code":a>=300&&a<400?"warning-code":a>=400?"error-code":"":"",B=a=>ke(a).format("YYYY-MM-DD HH:mm:ss"),p=async()=>{C.value=!0;try{await new Promise(a=>setTimeout(a,500)),U.value=[{id:1,title:"百度搜索",url:"https://www.baidu.com",anchor_text:"百度一下",link_type:1,target:"_blank",rel:["nofollow"],status:1,response_code:200,last_check:new Date,description:"百度搜索引擎",created_at:new Date}],u.total=1}catch(a){console.error("Failed to fetch external links:",a)}finally{C.value=!1}},D=()=>{u.page=1,p()},P=()=>{b.keyword="",b.status="",u.page=1,p()},j=a=>{u.pageSize=a,u.page=1,p()},q=a=>{u.page=a,p()},A=a=>{m.value=a},Y=()=>{k.value=!1,Z(),v.value=!0},H=a=>{k.value=!0,Object.assign(n,{...a,rel:Array.isArray(a.rel)?a.rel:a.rel?a.rel.split(","):[]}),v.value=!0},J=a=>{$.confirm(`确定要删除外链 "${a.title}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{g.success("删除成功"),p()})},K=()=>{m.value.length&&$.confirm(`确定要删除选中的 ${m.value.length} 个外链吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{g.success("批量删除成功"),p(),m.value=[]})},Q=a=>{g.info("正在检测链接状态..."),setTimeout(()=>{g.success("检测完成"),p()},1e3)},X=()=>{m.value.length&&(g.info(`正在检测 ${m.value.length} 个链接...`),setTimeout(()=>{g.success("批量检测完成"),p(),m.value=[]},2e3))},G=a=>{window.open(a.url,"_blank")},W=async()=>{if(z.value)try{await z.value.validate(),E.value=!0,await new Promise(a=>setTimeout(a,1e3)),g.success(k.value?"更新成功":"添加成功"),v.value=!1,p()}catch(a){console.error("Submit failed:",a)}finally{E.value=!1}},Z=()=>{Object.assign(n,{title:"",url:"",anchor_text:"",link_type:1,target:"_blank",rel:[],description:""})};return se(()=>{p()}),(a,l)=>{const h=_e,d=me,w=ce,s=he,V=fe,_=we,L=xe,ee=ge,le=ve,c=Ve,te=Ce,ae=be,oe=ye;return R(),re("div",Be,[i("div",De,[l[15]||(l[15]=i("h2",null,"外链管理",-1)),i("div",Le,[l[13]||(l[13]=i("span",null,"项目管理",-1)),e(h,null,{default:o(()=>[e(x(Ee))]),_:1}),l[14]||(l[14]=i("span",null,"外链管理",-1))])]),i("div",Re,[i("div",$e,[i("div",Me,[e(d,{type:"primary",onClick:Y},{default:o(()=>[e(h,null,{default:o(()=>[e(x(ze))]),_:1}),l[16]||(l[16]=r(" 添加外链 ",-1))]),_:1,__:[16]}),e(d,{onClick:K,disabled:!m.value.length},{default:o(()=>[e(h,null,{default:o(()=>[e(x(Se))]),_:1}),l[17]||(l[17]=r(" 批量删除 ",-1))]),_:1,__:[17]},8,["disabled"]),e(d,{onClick:X,disabled:!m.value.length},{default:o(()=>[e(h,null,{default:o(()=>[e(x(Te))]),_:1}),l[18]||(l[18]=r(" 批量检测 ",-1))]),_:1,__:[18]},8,["disabled"])]),i("div",Ie,[e(w,{modelValue:b.keyword,"onUpdate:modelValue":l[0]||(l[0]=t=>b.keyword=t),placeholder:"搜索链接标题或URL",style:{width:"300px","margin-right":"10px"},clearable:"",onKeyup:ie(D,["enter"])},{prefix:o(()=>[e(h,null,{default:o(()=>[e(x(Ue))]),_:1})]),_:1},8,["modelValue"]),e(V,{modelValue:b.status,"onUpdate:modelValue":l[1]||(l[1]=t=>b.status=t),placeholder:"链接状态",style:{width:"120px","margin-right":"10px"},clearable:""},{default:o(()=>[e(s,{label:"正常",value:1}),e(s,{label:"异常",value:0}),e(s,{label:"未检测",value:-1})]),_:1},8,["modelValue"]),e(d,{onClick:D},{default:o(()=>l[19]||(l[19]=[r("搜索",-1)])),_:1,__:[19]}),e(d,{onClick:P},{default:o(()=>l[20]||(l[20]=[r("重置",-1)])),_:1,__:[20]})])]),ue((R(),de(ee,{data:U.value,style:{width:"100%"},onSelectionChange:A},{default:o(()=>[e(_,{type:"selection",width:"55"}),e(_,{prop:"title",label:"链接标题","min-width":"150"}),e(_,{prop:"url",label:"链接地址","min-width":"250","show-overflow-tooltip":""}),e(_,{prop:"anchor_text",label:"锚文本","min-width":"120"}),e(_,{prop:"link_type",label:"链接类型",width:"100"},{default:o(({row:t})=>[e(L,null,{default:o(()=>[r(y(I(t.link_type)),1)]),_:2},1024)]),_:1}),e(_,{prop:"status",label:"状态",width:"100"},{default:o(({row:t})=>[e(L,{type:F(t.status)},{default:o(()=>[r(y(N(t.status)),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"response_code",label:"响应码",width:"80"},{default:o(({row:t})=>[i("span",{class:pe(O(t.response_code))},y(t.response_code||"-"),3)]),_:1}),e(_,{prop:"last_check",label:"最后检测",width:"160"},{default:o(({row:t})=>[r(y(t.last_check?B(t.last_check):"未检测"),1)]),_:1}),e(_,{prop:"created_at",label:"创建时间",width:"160"},{default:o(({row:t})=>[r(y(B(t.created_at)),1)]),_:1}),e(_,{label:"操作",width:"200",fixed:"right"},{default:o(({row:t})=>[e(d,{type:"text",size:"small",onClick:S=>H(t)},{default:o(()=>l[21]||(l[21]=[r(" 编辑 ",-1)])),_:2,__:[21]},1032,["onClick"]),e(d,{type:"text",size:"small",onClick:S=>Q(t)},{default:o(()=>l[22]||(l[22]=[r(" 检测 ",-1)])),_:2,__:[22]},1032,["onClick"]),e(d,{type:"text",size:"small",onClick:S=>G(t)},{default:o(()=>l[23]||(l[23]=[r(" 访问 ",-1)])),_:2,__:[23]},1032,["onClick"]),e(d,{type:"text",size:"small",onClick:S=>J(t),style:{color:"#f56c6c"}},{default:o(()=>l[24]||(l[24]=[r(" 删除 ",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[oe,C.value]]),i("div",Ne,[e(le,{"current-page":u.page,"onUpdate:currentPage":l[2]||(l[2]=t=>u.page=t),"page-size":u.pageSize,"onUpdate:pageSize":l[3]||(l[3]=t=>u.pageSize=t),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:q},null,8,["current-page","page-size","total"])])]),e(ae,{modelValue:v.value,"onUpdate:modelValue":l[12]||(l[12]=t=>v.value=t),title:k.value?"编辑外链":"添加外链",width:"600px"},{footer:o(()=>[i("span",Fe,[e(d,{onClick:l[11]||(l[11]=t=>v.value=!1)},{default:o(()=>l[25]||(l[25]=[r("取消",-1)])),_:1,__:[25]}),e(d,{type:"primary",onClick:W,loading:E.value},{default:o(()=>[r(y(k.value?"更新":"添加"),1)]),_:1},8,["loading"])])]),default:o(()=>[e(te,{ref_key:"formRef",ref:z,model:n,rules:M,"label-width":"120px"},{default:o(()=>[e(c,{label:"链接标题",prop:"title"},{default:o(()=>[e(w,{modelValue:n.title,"onUpdate:modelValue":l[4]||(l[4]=t=>n.title=t),placeholder:"请输入链接标题"},null,8,["modelValue"])]),_:1}),e(c,{label:"链接地址",prop:"url"},{default:o(()=>[e(w,{modelValue:n.url,"onUpdate:modelValue":l[5]||(l[5]=t=>n.url=t),placeholder:"请输入完整的URL地址"},null,8,["modelValue"])]),_:1}),e(c,{label:"锚文本",prop:"anchor_text"},{default:o(()=>[e(w,{modelValue:n.anchor_text,"onUpdate:modelValue":l[6]||(l[6]=t=>n.anchor_text=t),placeholder:"请输入锚文本"},null,8,["modelValue"])]),_:1}),e(c,{label:"链接类型",prop:"link_type"},{default:o(()=>[e(V,{modelValue:n.link_type,"onUpdate:modelValue":l[7]||(l[7]=t=>n.link_type=t),placeholder:"请选择链接类型"},{default:o(()=>[e(s,{label:"友情链接",value:1}),e(s,{label:"内容链接",value:2}),e(s,{label:"导航链接",value:3}),e(s,{label:"广告链接",value:4})]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"目标属性",prop:"target"},{default:o(()=>[e(V,{modelValue:n.target,"onUpdate:modelValue":l[8]||(l[8]=t=>n.target=t),placeholder:"请选择目标属性"},{default:o(()=>[e(s,{label:"_blank (新窗口)",value:"_blank"}),e(s,{label:"_self (当前窗口)",value:"_self"}),e(s,{label:"_parent (父窗口)",value:"_parent"}),e(s,{label:"_top (顶层窗口)",value:"_top"})]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"rel属性",prop:"rel"},{default:o(()=>[e(V,{modelValue:n.rel,"onUpdate:modelValue":l[9]||(l[9]=t=>n.rel=t),placeholder:"请选择rel属性",multiple:""},{default:o(()=>[e(s,{label:"nofollow",value:"nofollow"}),e(s,{label:"noopener",value:"noopener"}),e(s,{label:"noreferrer",value:"noreferrer"}),e(s,{label:"sponsored",value:"sponsored"}),e(s,{label:"ugc",value:"ugc"})]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"描述",prop:"description"},{default:o(()=>[e(w,{modelValue:n.description,"onUpdate:modelValue":l[10]||(l[10]=t=>n.description=t),type:"textarea",rows:3,placeholder:"请输入链接描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},al=ne(Oe,[["__scopeId","data-v-a7f8ae77"]]);export{al as default};
