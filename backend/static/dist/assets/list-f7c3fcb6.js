import{_ as je}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                         *//* empty css                *//* empty css                    */import{aC as Ce,aB as xe,r as U,X as Q,c as G,f as Se,k as ze,y as v,z as x,A as n,Q as e,I as a,u as _,O as f,M as o,a5 as X,J as q,H as B,L as Ee}from"./vendor-ad470fc0.js";import{p as k}from"./project-3f17a178.js";import{w as Ue,E as j,a as W,c as Ve,G as Te,H as De,x as Be,h as Me,s as Pe,I as Ie,J as Re,B as Le,A as $e,K as Fe,m as Ne,n as Ke,o as Ae,C as He,L as Oe,M as Ye,D as Z,N as Je,O as M,P as ee,Q as Qe,R as Ge,S as P,T as Xe,U as qe}from"./elementPlus-05e8f3ef.js";import We from"./form-f586b152.js";import"./index-24913185.js";/* empty css                        *//* empty css                     */const Ze={class:"page-container"},et={class:"page-header"},tt={class:"header-content"},at={class:"header-left"},lt={class:"header-icon"},st={class:"header-text"},ot={class:"breadcrumb"},nt={class:"header-stats"},dt={class:"stat-item"},rt={class:"stat-value"},it={class:"stat-item"},ct={class:"stat-value"},ut={class:"page-content"},pt={class:"mode-tabs"},_t={class:"tab-label"},ft={class:"tab-label"},mt={key:0,class:"standalone-mode"},gt={class:"table-toolbar"},ht={class:"toolbar-left"},yt={class:"toolbar-right"},bt={class:"search-group"},vt={key:1,class:"text-muted"},wt={style:{"font-size":"12px"}},kt={class:"pagination-container"},jt={key:1,class:"interface-mode"},Ct={class:"table-toolbar"},xt={class:"toolbar-left"},St={class:"toolbar-right"},zt={class:"pagination-container"},Et={key:2,class:"create-mode"},Ut={__name:"list",setup(Vt){const y=Ce(),te=xe(),S=U(!1),C=U([]),u=U([]),p=U("standalone"),c=Q({keyword:"",status:"",type:"",interface_type:""}),r=Q({page:1,pageSize:10,total:0}),ae=G(()=>p.value==="standalone"?"项目列表":"新建项目"),le=G(()=>C.value.filter(l=>l.project_status===1).length),se=l=>({1:"泛目录",2:"寄生虫",3:"URL劫持",4:"404劫持"})[l]||"未知",I=l=>Ue(l).format("YYYY-MM-DD HH:mm:ss"),g=async()=>{S.value=!0;try{const l={page:r.page,page_size:r.pageSize,keyword:c.keyword,status:c.status,mode:p.value};p.value==="standalone"?l.type=c.type:p.value==="interface"&&(l.interface_type=c.interface_type);const t=await k.getList(l);t.code===200&&(C.value=t.data.list||[],r.total=t.data.total||0)}catch(l){console.error("Failed to fetch projects:",l),p.value==="interface"&&(C.value=[{id:1,domain:"example1.com",interface_type:"fanmulu",description:"请选择",status:1,created_at:new Date},{id:2,domain:"example2.com",interface_type:"jishengchong",description:"普通类型",status:1,created_at:new Date}],r.total=2)}finally{S.value=!1}},oe=l=>{p.value=l,V()},R=l=>{u.value=l},ne=()=>{y.push("/project/create")},de=()=>{y.push("/project/interface/create")},re=l=>({fanmulu:"泛目录",jishengchong:"寄生虫",url_hijack:"URL劫持","404_hijack":"404劫持",other:"其他"})[l]||l,ie=l=>({fanmulu:"primary",jishengchong:"success",url_hijack:"warning","404_hijack":"danger",other:"info"})[l]||"info",ce=()=>{p.value="standalone",g(),j.success("项目创建成功")},z=()=>{r.page=1,g()},V=()=>{c.keyword="",c.status="",c.type="",c.interface_type="",r.page=1,g()},L=()=>{u.value.length&&W.confirm(`确定要删除选中的 ${u.value.length} 个项目吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const l=u.value.map(d=>d.id);(await k.batchDelete(l)).code===200&&(j.success("批量删除成功"),g(),u.value=[])}catch(l){console.error("Failed to batch delete projects:",l)}})},ue=async()=>{if(u.value.length)try{const l=u.value.map(d=>d.id);(await k.batchUpdateStatus(l,1)).code===200&&(j.success("批量启用成功"),g(),u.value=[])}catch(l){console.error("Failed to batch enable projects:",l)}},pe=async()=>{if(u.value.length)try{const l=u.value.map(d=>d.id);(await k.batchUpdateStatus(l,0)).code===200&&(j.success("批量禁用成功"),g(),u.value=[])}catch(l){console.error("Failed to batch disable projects:",l)}},$=l=>{r.pageSize=l,r.page=1,g()},F=l=>{r.page=l,g()},_e=(l,t)=>{switch(l){case"edit":N(t);break;case"sync":fe(t);break;case"stats":me(t);break;case"keywords":ge(t);break;case"templates":he(t);break;case"urls":ye(t);break;case"delete":K(t);break}},N=l=>{p.value==="interface"?y.push(`/project/interface/edit/${l.id}`):y.push(`/project/edit/${l.id}`)},fe=async l=>{try{(await k.sync(l.id)).code===200&&(j.success("同步成功"),g())}catch(t){console.error("Failed to sync project:",t)}},me=l=>{y.push(`/spider/stats?project_id=${l.id}`)},ge=l=>{y.push(`/project/${l.id}/keywords`)},he=l=>{y.push(`/project/${l.id}/templates`)},ye=l=>{y.push(`/project/${l.id}/urls`)},K=l=>{W.confirm(`确定要删除项目 "${l.project_name||l.project_url}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{(await k.delete(l.id)).code===200&&(j.success("删除成功"),g())}catch(t){console.error("Failed to delete project:",t)}})};return Se(()=>te.name,l=>{l==="ProjectStandalone"?(p.value="standalone",g()):l==="ProjectInterface"&&(p.value="interface",g())},{immediate:!0}),ze(()=>{g()}),(l,t)=>{const d=Ve,A=Te,be=De,T=Be,m=Me,H=Pe,h=Ie,D=Re,i=Le,b=$e,ve=Fe,w=Ne,we=Ke,ke=Ae,O=He,Y=Oe,J=Ye;return v(),x("div",Ze,[n("div",et,[n("div",tt,[n("div",at,[n("div",lt,[e(d,null,{default:a(()=>[e(_(Z))]),_:1})]),n("div",st,[t[11]||(t[11]=n("h2",null,"项目管理",-1)),n("div",ot,[t[10]||(t[10]=n("span",null,"项目管理",-1)),e(d,null,{default:a(()=>[e(_(Je))]),_:1}),n("span",null,f(ae.value),1)])])]),n("div",nt,[n("div",dt,[n("div",rt,f(r.total||0),1),t[12]||(t[12]=n("div",{class:"stat-label"},"总项目数",-1))]),n("div",it,[n("div",ct,f(le.value),1),t[13]||(t[13]=n("div",{class:"stat-label"},"活跃项目",-1))])])])]),n("div",ut,[n("div",pt,[e(T,{class:"tabs-card"},{default:a(()=>[e(be,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=s=>p.value=s),onTabChange:oe},{default:a(()=>[e(A,{label:"项目列表",name:"standalone"},{label:a(()=>[n("span",_t,[e(d,null,{default:a(()=>[e(_(Z))]),_:1}),t[14]||(t[14]=o(" 项目列表 ",-1))])]),_:1}),e(A,{label:"新建项目",name:"create"},{label:a(()=>[n("span",ft,[e(d,null,{default:a(()=>[e(_(M))]),_:1}),t[15]||(t[15]=o(" 新建项目 ",-1))])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),p.value==="standalone"?(v(),x("div",mt,[e(T,{class:"toolbar-card"},{default:a(()=>[n("div",gt,[n("div",ht,[e(m,{type:"primary",onClick:ne,class:"action-btn"},{default:a(()=>[e(d,null,{default:a(()=>[e(_(M))]),_:1}),t[16]||(t[16]=o(" 新建项目 ",-1))]),_:1,__:[16]}),e(m,{onClick:L,disabled:!u.value.length,class:"action-btn danger"},{default:a(()=>[e(d,null,{default:a(()=>[e(_(ee))]),_:1}),t[17]||(t[17]=o(" 批量删除 ",-1))]),_:1,__:[17]},8,["disabled"]),e(m,{onClick:ue,disabled:!u.value.length,class:"action-btn success"},{default:a(()=>[e(d,null,{default:a(()=>[e(_(Qe))]),_:1}),t[18]||(t[18]=o(" 批量启用 ",-1))]),_:1,__:[18]},8,["disabled"]),e(m,{onClick:pe,disabled:!u.value.length,class:"action-btn warning"},{default:a(()=>[e(d,null,{default:a(()=>[e(_(Ge))]),_:1}),t[19]||(t[19]=o(" 批量禁用 ",-1))]),_:1,__:[19]},8,["disabled"])]),n("div",yt,[n("div",bt,[e(H,{modelValue:c.keyword,"onUpdate:modelValue":t[1]||(t[1]=s=>c.keyword=s),placeholder:"搜索项目名称或URL",class:"search-input",clearable:"",onKeyup:X(z,["enter"])},{prefix:a(()=>[e(d,null,{default:a(()=>[e(_(P))]),_:1})]),_:1},8,["modelValue"]),e(D,{modelValue:c.status,"onUpdate:modelValue":t[2]||(t[2]=s=>c.status=s),placeholder:"项目状态",class:"filter-select",clearable:""},{default:a(()=>[e(h,{label:"启用",value:1}),e(h,{label:"禁用",value:0})]),_:1},8,["modelValue"]),e(D,{modelValue:c.type,"onUpdate:modelValue":t[3]||(t[3]=s=>c.type=s),placeholder:"项目类型",class:"filter-select",clearable:""},{default:a(()=>[e(h,{label:"泛目录",value:1}),e(h,{label:"寄生虫",value:2}),e(h,{label:"URL劫持",value:3}),e(h,{label:"404劫持",value:4})]),_:1},8,["modelValue"]),e(m,{onClick:z,type:"primary",class:"search-btn"},{default:a(()=>[e(d,null,{default:a(()=>[e(_(P))]),_:1}),t[20]||(t[20]=o(" 搜索 ",-1))]),_:1,__:[20]}),e(m,{onClick:V,class:"reset-btn"},{default:a(()=>[e(d,null,{default:a(()=>[e(_(Xe))]),_:1}),t[21]||(t[21]=o(" 重置 ",-1))]),_:1,__:[21]})])])])]),_:1}),e(T,{class:"table-card"},{default:a(()=>[q((v(),B(O,{data:C.value,style:{width:"100%"},onSelectionChange:R},{default:a(()=>[e(i,{type:"selection",width:"55"}),e(i,{prop:"project_url",label:"项目URL","min-width":"200","show-overflow-tooltip":""}),e(i,{prop:"site_id",label:"SITEID",width:"100"}),e(i,{prop:"url_rules_count",label:"URL规则数",width:"100"},{default:a(({row:s})=>[e(b,{type:"info"},{default:a(()=>[o(f(s.url_rules_count||0),1)]),_:2},1024)]),_:1}),e(i,{prop:"ad_url",label:"广告地址","min-width":"150","show-overflow-tooltip":""}),e(i,{prop:"template_name",label:"站点模板",width:"120"},{default:a(({row:s})=>[s.template_name?(v(),B(b,{key:0},{default:a(()=>[o(f(s.template_name),1)]),_:2},1024)):(v(),x("span",vt,"未设置"))]),_:1}),e(i,{prop:"project_status",label:"项目状态",width:"100"},{default:a(({row:s})=>[e(b,{type:s.project_status===1?"success":"danger"},{default:a(()=>[o(f(s.project_status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"keyword_groups",label:"关联词组",width:"100"},{default:a(({row:s})=>[e(b,{type:"warning"},{default:a(()=>[o(f(s.keyword_groups||0),1)]),_:2},1024)]),_:1}),e(i,{prop:"updated_at",label:"更新时间",width:"160"},{default:a(({row:s})=>[o(f(I(s.updated_at)),1)]),_:1}),e(i,{prop:"project_type",label:"项目类型",width:"100"},{default:a(({row:s})=>[e(b,null,{default:a(()=>[o(f(se(s.project_type)),1)]),_:2},1024)]),_:1}),e(i,{prop:"seo_score",label:"SEO数据",width:"100"},{default:a(({row:s})=>[e(ve,{percentage:s.seo_score||0,"stroke-width":6,"show-text":!1},null,8,["percentage"]),n("span",wt,f(s.seo_score||0)+"%",1)]),_:1}),e(i,{prop:"spider_count",label:"蜘蛛数量",width:"100"},{default:a(({row:s})=>[e(b,{type:"success"},{default:a(()=>[o(f(s.spider_count||0),1)]),_:2},1024)]),_:1}),e(i,{label:"操作",width:"200",fixed:"right"},{default:a(({row:s})=>[e(ke,{onCommand:E=>_e(E,s)},{dropdown:a(()=>[e(we,null,{default:a(()=>[e(w,{command:"edit"},{default:a(()=>t[23]||(t[23]=[o("编辑",-1)])),_:1,__:[23]}),e(w,{command:"sync"},{default:a(()=>t[24]||(t[24]=[o("同步",-1)])),_:1,__:[24]}),e(w,{command:"stats"},{default:a(()=>t[25]||(t[25]=[o("统计",-1)])),_:1,__:[25]}),e(w,{command:"keywords"},{default:a(()=>t[26]||(t[26]=[o("词库管理",-1)])),_:1,__:[26]}),e(w,{command:"templates"},{default:a(()=>t[27]||(t[27]=[o("模板管理",-1)])),_:1,__:[27]}),e(w,{command:"urls"},{default:a(()=>t[28]||(t[28]=[o("URL规则",-1)])),_:1,__:[28]}),e(w,{divided:"",command:"delete",style:{color:"#f56c6c"}},{default:a(()=>t[29]||(t[29]=[o("删除",-1)])),_:1,__:[29]})]),_:1})]),default:a(()=>[e(m,{type:"primary",size:"small"},{default:a(()=>[t[22]||(t[22]=o(" 操作",-1)),e(d,{class:"el-icon--right"},{default:a(()=>[e(_(qe))]),_:1})]),_:1,__:[22]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[J,S.value]]),n("div",kt,[e(Y,{"current-page":r.page,"onUpdate:currentPage":t[4]||(t[4]=s=>r.page=s),"page-size":r.pageSize,"onUpdate:pageSize":t[5]||(t[5]=s=>r.pageSize=s),"page-sizes":[10,20,50,100],total:r.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$,onCurrentChange:F},null,8,["current-page","page-size","total"])])]),_:1})])):p.value==="interface"?(v(),x("div",jt,[n("div",Ct,[n("div",xt,[e(m,{type:"primary",onClick:de},{default:a(()=>[e(d,null,{default:a(()=>[e(_(M))]),_:1}),t[30]||(t[30]=o(" 新增接口内容 ",-1))]),_:1,__:[30]}),e(m,{onClick:L,disabled:!u.value.length},{default:a(()=>[e(d,null,{default:a(()=>[e(_(ee))]),_:1}),t[31]||(t[31]=o(" 批量删除 ",-1))]),_:1,__:[31]},8,["disabled"])]),n("div",St,[e(H,{modelValue:c.keyword,"onUpdate:modelValue":t[6]||(t[6]=s=>c.keyword=s),placeholder:"搜索项目名称或域名",style:{width:"300px","margin-right":"10px"},clearable:"",onKeyup:X(z,["enter"])},{prefix:a(()=>[e(d,null,{default:a(()=>[e(_(P))]),_:1})]),_:1},8,["modelValue"]),e(D,{modelValue:c.interface_type,"onUpdate:modelValue":t[7]||(t[7]=s=>c.interface_type=s),placeholder:"接口类型",style:{width:"120px","margin-right":"10px"},clearable:""},{default:a(()=>[e(h,{label:"泛目录",value:"fanmulu"}),e(h,{label:"寄生虫",value:"jishengchong"}),e(h,{label:"URL劫持",value:"url_hijack"}),e(h,{label:"404劫持",value:"404_hijack"}),e(h,{label:"其他",value:"other"})]),_:1},8,["modelValue"]),e(m,{onClick:z},{default:a(()=>t[32]||(t[32]=[o("搜索",-1)])),_:1,__:[32]}),e(m,{onClick:V},{default:a(()=>t[33]||(t[33]=[o("重置",-1)])),_:1,__:[33]})])]),q((v(),B(O,{data:C.value,style:{width:"100%"},onSelectionChange:R},{default:a(()=>[e(i,{type:"selection",width:"55"}),e(i,{prop:"domain",label:"接口域名","min-width":"200"}),e(i,{prop:"interface_type",label:"接口类型",width:"120"},{default:a(({row:s})=>[e(b,{type:ie(s.interface_type)},{default:a(()=>[o(f(re(s.interface_type)),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"description",label:"接口备注",width:"120"}),e(i,{prop:"status",label:"状态",width:"80"},{default:a(({row:s})=>[e(b,{type:s.status===1?"success":"danger"},{default:a(()=>[o(f(s.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"created_at",label:"创建时间",width:"160"},{default:a(({row:s})=>[o(f(I(s.created_at)),1)]),_:1}),e(i,{label:"操作",width:"150",fixed:"right"},{default:a(({row:s})=>[e(m,{type:"text",size:"small",onClick:E=>N(s)},{default:a(()=>t[34]||(t[34]=[o(" 编辑 ",-1)])),_:2,__:[34]},1032,["onClick"]),e(m,{type:"text",size:"small",onClick:E=>l.handleToggleStatus(s)},{default:a(()=>[o(f(s.status===1?"禁用":"启用"),1)]),_:2},1032,["onClick"]),e(m,{type:"text",size:"small",onClick:E=>K(s),style:{color:"#f56c6c"}},{default:a(()=>t[35]||(t[35]=[o(" 删除 ",-1)])),_:2,__:[35]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,S.value]]),n("div",zt,[e(Y,{"current-page":r.page,"onUpdate:currentPage":t[8]||(t[8]=s=>r.page=s),"page-size":r.pageSize,"onUpdate:pageSize":t[9]||(t[9]=s=>r.pageSize=s),"page-sizes":[10,20,50,100],total:r.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$,onCurrentChange:F},null,8,["current-page","page-size","total"])])])):p.value==="create"?(v(),x("div",Et,[e(We,{onSuccess:ce})])):Ee("",!0)])])}}},Wt=je(Ut,[["__scopeId","data-v-3ec981c4"]]);export{Wt as default};
