import{_ as j}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                *//* empty css                  */import{aC as J,r as E,X as S,c as O,k as G,y as $,z as Q,A as l,Q as t,I as a,u as m,O as g,M as i,a5 as X,J as q,H as W}from"./vendor-ad470fc0.js";import{s as f}from"./index-bb3d8812.js";import{w as Z,E as B,a as ee,c as te,h as ae,s as se,G as le,H as oe,x as ne,B as re,A as ie,C as de,J as pe,K as ue,L as ce,M as _e,N as me,R as T,S as fe}from"./elementPlus-ef333120.js";const C={getList(r){return f({url:"/templates",method:"get",params:r})},create(r){return f({url:"/templates",method:"post",data:r})},get(r){return f({url:`/templates/${r}`,method:"get"})},update(r,y){return f({url:`/templates/${r}`,method:"put",data:y})},delete(r){return f({url:`/templates/${r}`,method:"delete"})},preview(r){return f({url:`/templates/${r}/preview`,method:"post"})}};const ge={class:"page-container"},ve={class:"page-header"},ye={class:"header-content"},he={class:"header-left"},be={class:"header-icon"},we={class:"breadcrumb"},Ce={class:"header-stats"},ke={class:"stat-item"},xe={class:"stat-value"},ze={class:"stat-item"},Ee={class:"stat-value"},Se={class:"page-content"},$e={class:"table-toolbar"},Be={class:"toolbar-left"},Te={class:"search-group"},Ve={__name:"list",setup(r){const y=J(),h=E(!1),v=E([]),d=S({keyword:"",type:""}),n=S({page:1,pageSize:10,total:0}),V=O(()=>v.value.filter(s=>s.status===1).length),D=s=>({article:"文章模板",list:"列表模板",index:"首页模板"})[s]||s,M=s=>Z(s).format("YYYY-MM-DD HH:mm:ss"),p=async()=>{h.value=!0;try{const s={page:n.page,page_size:n.pageSize,keyword:d.keyword,type:d.type},e=await C.getList(s);e.code===200&&(v.value=e.data.list,n.total=e.data.total)}catch(s){console.error("Failed to fetch templates:",s)}finally{h.value=!1}},k=()=>{n.page=1,p()},I=()=>{d.keyword="",d.type="",n.page=1,p()},N=s=>{n.pageSize=s,n.page=1,p()},F=s=>{n.page=s,p()},H=s=>{y.push(`/template/edit/${s.id}`)},L=async s=>{try{(await C.preview(s.id)).code===200&&B.success("预览功能暂未开放")}catch(e){console.error("Failed to preview template:",e)}},P=s=>{ee.confirm(`确定要删除模板 "${s.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{(await C.delete(s.id)).code===200&&(B.success("删除成功"),p())}catch(e){console.error("Failed to delete template:",e)}})};return G(()=>{p()}),(s,e)=>{const u=te,c=ae,U=se,b=le,Y=oe,w=ne,_=re,x=ie,A=de,K=pe,R=ue;return $(),Q("div",ge,[l("div",ve,[l("div",ye,[l("div",he,[l("div",be,[t(u,null,{default:a(()=>[t(m(ce))]),_:1})]),l("div",null,[e[7]||(e[7]=l("h1",{class:"page-title"},"模板管理",-1)),l("div",we,[e[5]||(e[5]=l("span",null,"系统管理",-1)),t(u,null,{default:a(()=>[t(m(_e))]),_:1}),e[6]||(e[6]=l("span",{class:"current"},"模板管理",-1))])])]),l("div",Ce,[l("div",ke,[l("div",xe,g(v.value.length),1),e[8]||(e[8]=l("div",{class:"stat-label"},"总模板数",-1))]),l("div",ze,[l("div",Ee,g(V.value),1),e[9]||(e[9]=l("div",{class:"stat-label"},"启用中",-1))])])])]),l("div",Se,[t(w,{class:"toolbar-card"},{default:a(()=>[l("div",$e,[l("div",Be,[t(c,{type:"primary",onClick:e[0]||(e[0]=o=>s.$router.push("/template/create")),class:"action-btn"},{default:a(()=>[t(u,null,{default:a(()=>[t(m(me))]),_:1}),e[10]||(e[10]=i(" 新建模板 ",-1))]),_:1,__:[10]})]),l("div",Te,[t(U,{modelValue:d.keyword,"onUpdate:modelValue":e[1]||(e[1]=o=>d.keyword=o),placeholder:"搜索模板名称",clearable:"",onKeyup:X(k,["enter"])},{prefix:a(()=>[t(u,null,{default:a(()=>[t(m(T))]),_:1})]),_:1},8,["modelValue"]),t(Y,{modelValue:d.type,"onUpdate:modelValue":e[2]||(e[2]=o=>d.type=o),placeholder:"模板类型",clearable:""},{default:a(()=>[t(b,{label:"文章模板",value:"article"}),t(b,{label:"列表模板",value:"list"}),t(b,{label:"首页模板",value:"index"})]),_:1},8,["modelValue"]),t(c,{onClick:k,class:"search-btn"},{default:a(()=>[t(u,null,{default:a(()=>[t(m(T))]),_:1}),e[11]||(e[11]=i(" 搜索 ",-1))]),_:1,__:[11]}),t(c,{onClick:I,class:"reset-btn"},{default:a(()=>[t(u,null,{default:a(()=>[t(m(fe))]),_:1}),e[12]||(e[12]=i(" 重置 ",-1))]),_:1,__:[12]})])])]),_:1}),t(w,{class:"table-card"},{default:a(()=>[q(($(),W(A,{data:v.value,style:{width:"100%"}},{default:a(()=>[t(_,{prop:"name",label:"模板名称","min-width":"150"}),t(_,{prop:"type",label:"类型",width:"100"},{default:a(({row:o})=>[t(x,null,{default:a(()=>[i(g(D(o.type)),1)]),_:2},1024)]),_:1}),t(_,{prop:"description",label:"描述","show-overflow-tooltip":""}),t(_,{prop:"status",label:"状态",width:"80"},{default:a(({row:o})=>[t(x,{type:o.status===1?"success":"danger"},{default:a(()=>[i(g(o.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(_,{prop:"created_at",label:"创建时间",width:"180"},{default:a(({row:o})=>[i(g(M(o.created_at)),1)]),_:1}),t(_,{label:"操作",width:"200",fixed:"right"},{default:a(({row:o})=>[t(c,{type:"text",size:"small",onClick:z=>H(o)},{default:a(()=>e[13]||(e[13]=[i(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick"]),t(c,{type:"text",size:"small",onClick:z=>L(o)},{default:a(()=>e[14]||(e[14]=[i(" 预览 ",-1)])),_:2,__:[14]},1032,["onClick"]),t(c,{type:"text",size:"small",onClick:z=>P(o),style:{color:"#f56c6c"}},{default:a(()=>e[15]||(e[15]=[i(" 删除 ",-1)])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[R,h.value]])]),_:1}),t(w,{class:"pagination-card"},{default:a(()=>[t(K,{"current-page":n.page,"onUpdate:currentPage":e[3]||(e[3]=o=>n.page=o),"page-size":n.pageSize,"onUpdate:pageSize":e[4]||(e[4]=o=>n.pageSize=o),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:N,onCurrentChange:F},null,8,["current-page","page-size","total"])]),_:1})])])}}},Oe=j(Ve,[["__scopeId","data-v-33bd2963"]]);export{Oe as default};
