import{_ as B}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                     *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 */import{aC as M,aB as R,r as v,c as L,X as N,k as P,y as h,z as y,A as l,O as m,Q as t,I as i,P as S,a4 as T,C as O,M as U}from"./vendor-ad470fc0.js";import{p as f}from"./project-3f17a178.js";import{v as q,E as g,s as z,u as A,I as D,J as $,h as J}from"./elementPlus-05e8f3ef.js";import"./index-24913185.js";const Q={class:"page-container"},X={class:"page-header"},G={class:"page-content"},H={class:"form-container"},K={class:"interface-notes"},W=["onClick"],Y={class:"note-label"},Z={class:"form-actions"},ee={__name:"interface-form",setup(te){const b=M(),d=R(),u=v(),_=v(!1),c=L(()=>!!d.params.id),j=[{label:"请选择",value:"请选择"},{label:"普通类型",value:"普通类型"},{label:"TL 对应",value:"TL 对应"}],e=N({domain:"",interface_type:"",description:"请选择"}),k={domain:[{required:!0,message:"请输入接口域名",trigger:"blur"}],interface_type:[{required:!0,message:"请选择接口类型",trigger:"change"}]},E=a=>{e.description=a},V=async()=>{if(u.value)try{await u.value.validate(),_.value=!0;const a={project_name:`接口项目-${e.domain}`,project_url:e.domain,project_type:C(e.interface_type),project_mode:"interface",description:e.description,interface_config:{domain:e.domain,interface_type:e.interface_type,notes:e.description}};let o;c.value?o=await f.update(d.params.id,a):o=await f.create(a),o.code===200&&(g.success(c.value?"更新成功":"创建成功"),b.push("/project/interface"))}catch(a){console.error("Submit failed:",a),g.error("创建失败，请检查表单数据")}finally{_.value=!1}},C=a=>({fanmulu:1,jishengchong:2,url_hijack:3,"404_hijack":4,other:5})[a]||1,x=async()=>{var a,o,p;if(c.value)try{const r=await f.get(d.params.id);if(r.code===200){const n=r.data;e.domain=n.project_url||((a=n.interface_config)==null?void 0:a.domain)||"",e.interface_type=((o=n.interface_config)==null?void 0:o.interface_type)||"",e.description=((p=n.interface_config)==null?void 0:p.notes)||n.description||"请选择"}}catch(r){console.error("Failed to fetch project:",r)}};return P(()=>{x()}),(a,o)=>{const p=z,r=A,n=D,w=$,F=J,I=q;return h(),y("div",Q,[l("div",X,[l("h2",null,m(c.value?"编辑":"创建")+" Control",1)]),l("div",G,[l("div",H,[t(I,{ref_key:"formRef",ref:u,model:e,rules:k,"label-width":"120px",class:"interface-form"},{default:i(()=>[t(r,{label:"接口域名",prop:"domain"},{default:i(()=>[t(p,{modelValue:e.domain,"onUpdate:modelValue":o[0]||(o[0]=s=>e.domain=s),placeholder:"添加需要更新的域名，不需要请跳过协议",class:"domain-input"},null,8,["modelValue"])]),_:1}),t(r,{label:"接口类型",prop:"interface_type"},{default:i(()=>[t(w,{modelValue:e.interface_type,"onUpdate:modelValue":o[1]||(o[1]=s=>e.interface_type=s),placeholder:"请选择",class:"type-select"},{default:i(()=>[t(n,{label:"泛目录",value:"fanmulu"}),t(n,{label:"寄生虫",value:"jishengchong"}),t(n,{label:"URL劫持",value:"url_hijack"}),t(n,{label:"404劫持",value:"404_hijack"}),t(n,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),t(r,{label:"接口备注",prop:"description"},{default:i(()=>[l("div",K,[(h(),y(S,null,T(j,s=>l("div",{key:s.value,class:O(["note-item",{active:e.description===s.value}]),onClick:ae=>E(s.value)},[l("span",Y,m(s.label),1)],10,W)),64))])]),_:1}),t(r,null,{default:i(()=>[l("div",Z,[t(F,{type:"primary",onClick:V,loading:_.value,class:"submit-btn"},{default:i(()=>[U(m(c.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),_:1})]),_:1},8,["model"])])])])}}},me=B(ee,[["__scopeId","data-v-54f24404"]]);export{me as default};
