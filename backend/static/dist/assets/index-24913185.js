import{y as ht,z as Et,Q as yt,al as gt,ax as bt,r as le,c as wt,ay as _t,az as Rt,au as St,aA as At}from"./vendor-ad470fc0.js";import{E as N,a as Ot,b as Tt,i as Ft}from"./elementPlus-05e8f3ef.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function n(o){if(o.ep)return;o.ep=!0;const s=r(o);fetch(o.href,s)}})();/*! Element Plus v2.10.4 */var Dt={name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 Delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}};const Pt={id:"app"},Ct={__name:"App",setup(e){return(t,r)=>{const n=gt("router-view");return ht(),Et("div",Pt,[yt(n)])}}},Lt="modulepreload",Bt=function(e){return"/"+e},Fe={},y=function(t,r,n){if(!r||r.length===0)return t();const o=document.getElementsByTagName("link");return Promise.all(r.map(s=>{if(s=Bt(s),s in Fe)return;Fe[s]=!0;const i=s.endsWith(".css"),a=i?'[rel="stylesheet"]':"";if(!!n)for(let l=o.length-1;l>=0;l--){const d=o[l];if(d.href===s&&(!i||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${a}`))return;const c=document.createElement("link");if(c.rel=i?"stylesheet":Lt,i||(c.as="script",c.crossOrigin=""),c.href=s,document.head.appendChild(c),i)return new Promise((l,d)=>{c.addEventListener("load",l),c.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${s}`)))})})).then(()=>t()).catch(s=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=s,window.dispatchEvent(i),!i.defaultPrevented)throw s})};function $e(e,t){return function(){return e.apply(t,arguments)}}const{toString:xt}=Object.prototype,{getPrototypeOf:we}=Object,{iterator:ne,toStringTag:ze}=Symbol,oe=(e=>t=>{const r=xt.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),P=e=>(e=e.toLowerCase(),t=>oe(t)===e),se=e=>t=>typeof t===e,{isArray:j}=Array,M=se("undefined");function H(e){return e!==null&&!M(e)&&e.constructor!==null&&!M(e.constructor)&&T(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Je=P("ArrayBuffer");function Nt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Je(e.buffer),t}const Ut=se("string"),T=se("function"),We=se("number"),$=e=>e!==null&&typeof e=="object",kt=e=>e===!0||e===!1,X=e=>{if(oe(e)!=="object")return!1;const t=we(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ze in e)&&!(ne in e)},It=e=>{if(!$(e)||H(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},jt=P("Date"),vt=P("File"),qt=P("Blob"),Vt=P("FileList"),Mt=e=>$(e)&&T(e.pipe),Ht=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||T(e.append)&&((t=oe(e))==="formdata"||t==="object"&&T(e.toString)&&e.toString()==="[object FormData]"))},$t=P("URLSearchParams"),[zt,Jt,Wt,Kt]=["ReadableStream","Request","Response","Headers"].map(P),Xt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function z(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,o;if(typeof e!="object"&&(e=[e]),j(e))for(n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else{if(H(e))return;const s=r?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let a;for(n=0;n<i;n++)a=s[n],t.call(null,e[a],a,e)}}function Ke(e,t){if(H(e))return null;t=t.toLowerCase();const r=Object.keys(e);let n=r.length,o;for(;n-- >0;)if(o=r[n],t===o.toLowerCase())return o;return null}const k=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Xe=e=>!M(e)&&e!==k;function me(){const{caseless:e}=Xe(this)&&this||{},t={},r=(n,o)=>{const s=e&&Ke(t,o)||o;X(t[s])&&X(n)?t[s]=me(t[s],n):X(n)?t[s]=me({},n):j(n)?t[s]=n.slice():t[s]=n};for(let n=0,o=arguments.length;n<o;n++)arguments[n]&&z(arguments[n],r);return t}const Gt=(e,t,r,{allOwnKeys:n}={})=>(z(t,(o,s)=>{r&&T(o)?e[s]=$e(o,r):e[s]=o},{allOwnKeys:n}),e),Yt=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Qt=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Zt=(e,t,r,n)=>{let o,s,i;const a={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!n||n(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=r!==!1&&we(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},er=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},tr=e=>{if(!e)return null;if(j(e))return e;let t=e.length;if(!We(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},rr=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&we(Uint8Array)),nr=(e,t)=>{const n=(e&&e[ne]).call(e);let o;for(;(o=n.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},or=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},sr=P("HTMLFormElement"),ir=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,o){return n.toUpperCase()+o}),De=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),ur=P("RegExp"),Ge=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};z(r,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(n[s]=i||o)}),Object.defineProperties(e,n)},ar=e=>{Ge(e,(t,r)=>{if(T(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(T(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},cr=(e,t)=>{const r={},n=o=>{o.forEach(s=>{r[s]=!0})};return j(e)?n(e):n(String(e).split(t)),r},lr=()=>{},fr=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function dr(e){return!!(e&&T(e.append)&&e[ze]==="FormData"&&e[ne])}const pr=e=>{const t=new Array(10),r=(n,o)=>{if($(n)){if(t.indexOf(n)>=0)return;if(H(n))return n;if(!("toJSON"in n)){t[o]=n;const s=j(n)?[]:{};return z(n,(i,a)=>{const f=r(i,o+1);!M(f)&&(s[a]=f)}),t[o]=void 0,s}}return n};return r(e,0)},mr=P("AsyncFunction"),hr=e=>e&&($(e)||T(e))&&T(e.then)&&T(e.catch),Ye=((e,t)=>e?setImmediate:t?((r,n)=>(k.addEventListener("message",({source:o,data:s})=>{o===k&&s===r&&n.length&&n.shift()()},!1),o=>{n.push(o),k.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",T(k.postMessage)),Er=typeof queueMicrotask<"u"?queueMicrotask.bind(k):typeof process<"u"&&process.nextTick||Ye,yr=e=>e!=null&&T(e[ne]),u={isArray:j,isArrayBuffer:Je,isBuffer:H,isFormData:Ht,isArrayBufferView:Nt,isString:Ut,isNumber:We,isBoolean:kt,isObject:$,isPlainObject:X,isEmptyObject:It,isReadableStream:zt,isRequest:Jt,isResponse:Wt,isHeaders:Kt,isUndefined:M,isDate:jt,isFile:vt,isBlob:qt,isRegExp:ur,isFunction:T,isStream:Mt,isURLSearchParams:$t,isTypedArray:rr,isFileList:Vt,forEach:z,merge:me,extend:Gt,trim:Xt,stripBOM:Yt,inherits:Qt,toFlatObject:Zt,kindOf:oe,kindOfTest:P,endsWith:er,toArray:tr,forEachEntry:nr,matchAll:or,isHTMLForm:sr,hasOwnProperty:De,hasOwnProp:De,reduceDescriptors:Ge,freezeMethods:ar,toObjectSet:cr,toCamelCase:ir,noop:lr,toFiniteNumber:fr,findKey:Ke,global:k,isContextDefined:Xe,isSpecCompliantForm:dr,toJSONObject:pr,isAsyncFn:mr,isThenable:hr,setImmediate:Ye,asap:Er,isIterable:yr};function h(e,t,r,n,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),o&&(this.response=o,this.status=o.status?o.status:null)}u.inherits(h,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:u.toJSONObject(this.config),code:this.code,status:this.status}}});const Qe=h.prototype,Ze={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ze[e]={value:e}});Object.defineProperties(h,Ze);Object.defineProperty(Qe,"isAxiosError",{value:!0});h.from=(e,t,r,n,o,s)=>{const i=Object.create(Qe);return u.toFlatObject(e,i,function(f){return f!==Error.prototype},a=>a!=="isAxiosError"),h.call(i,e.message,t,r,n,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const gr=null;function he(e){return u.isPlainObject(e)||u.isArray(e)}function et(e){return u.endsWith(e,"[]")?e.slice(0,-2):e}function Pe(e,t,r){return e?e.concat(t).map(function(o,s){return o=et(o),!r&&s?"["+o+"]":o}).join(r?".":""):t}function br(e){return u.isArray(e)&&!e.some(he)}const wr=u.toFlatObject(u,{},null,function(t){return/^is[A-Z]/.test(t)});function ie(e,t,r){if(!u.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=u.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,m){return!u.isUndefined(m[E])});const n=r.metaTokens,o=r.visitor||l,s=r.dots,i=r.indexes,f=(r.Blob||typeof Blob<"u"&&Blob)&&u.isSpecCompliantForm(t);if(!u.isFunction(o))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(u.isDate(p))return p.toISOString();if(u.isBoolean(p))return p.toString();if(!f&&u.isBlob(p))throw new h("Blob is not supported. Use a Buffer instead.");return u.isArrayBuffer(p)||u.isTypedArray(p)?f&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function l(p,E,m){let b=p;if(p&&!m&&typeof p=="object"){if(u.endsWith(E,"{}"))E=n?E:E.slice(0,-2),p=JSON.stringify(p);else if(u.isArray(p)&&br(p)||(u.isFileList(p)||u.endsWith(E,"[]"))&&(b=u.toArray(p)))return E=et(E),b.forEach(function(S,L){!(u.isUndefined(S)||S===null)&&t.append(i===!0?Pe([E],L,s):i===null?E:E+"[]",c(S))}),!1}return he(p)?!0:(t.append(Pe(m,E,s),c(p)),!1)}const d=[],g=Object.assign(wr,{defaultVisitor:l,convertValue:c,isVisitable:he});function _(p,E){if(!u.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+E.join("."));d.push(p),u.forEach(p,function(b,R){(!(u.isUndefined(b)||b===null)&&o.call(t,b,u.isString(R)?R.trim():R,E,g))===!0&&_(b,E?E.concat(R):[R])}),d.pop()}}if(!u.isObject(e))throw new TypeError("data must be an object");return _(e),t}function Ce(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function _e(e,t){this._pairs=[],e&&ie(e,this,t)}const tt=_e.prototype;tt.append=function(t,r){this._pairs.push([t,r])};tt.toString=function(t){const r=t?function(n){return t.call(this,n,Ce)}:Ce;return this._pairs.map(function(o){return r(o[0])+"="+r(o[1])},"").join("&")};function _r(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rt(e,t,r){if(!t)return e;const n=r&&r.encode||_r;u.isFunction(r)&&(r={serialize:r});const o=r&&r.serialize;let s;if(o?s=o(t,r):s=u.isURLSearchParams(t)?t.toString():new _e(t,r).toString(n),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Rr{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){u.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Le=Rr,nt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Sr=typeof URLSearchParams<"u"?URLSearchParams:_e,Ar=typeof FormData<"u"?FormData:null,Or=typeof Blob<"u"?Blob:null,Tr={isBrowser:!0,classes:{URLSearchParams:Sr,FormData:Ar,Blob:Or},protocols:["http","https","file","blob","url","data"]},Re=typeof window<"u"&&typeof document<"u",Ee=typeof navigator=="object"&&navigator||void 0,Fr=Re&&(!Ee||["ReactNative","NativeScript","NS"].indexOf(Ee.product)<0),Dr=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Pr=Re&&window.location.href||"http://localhost",Cr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Re,hasStandardBrowserEnv:Fr,hasStandardBrowserWebWorkerEnv:Dr,navigator:Ee,origin:Pr},Symbol.toStringTag,{value:"Module"})),A={...Cr,...Tr};function Lr(e,t){return ie(e,new A.classes.URLSearchParams,{visitor:function(r,n,o,s){return A.isNode&&u.isBuffer(r)?(this.append(n,r.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)},...t})}function Br(e){return u.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function xr(e){const t={},r=Object.keys(e);let n;const o=r.length;let s;for(n=0;n<o;n++)s=r[n],t[s]=e[s];return t}function ot(e){function t(r,n,o,s){let i=r[s++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),f=s>=r.length;return i=!i&&u.isArray(o)?o.length:i,f?(u.hasOwnProp(o,i)?o[i]=[o[i],n]:o[i]=n,!a):((!o[i]||!u.isObject(o[i]))&&(o[i]=[]),t(r,n,o[i],s)&&u.isArray(o[i])&&(o[i]=xr(o[i])),!a)}if(u.isFormData(e)&&u.isFunction(e.entries)){const r={};return u.forEachEntry(e,(n,o)=>{t(Br(n),o,r,0)}),r}return null}function Nr(e,t,r){if(u.isString(e))try{return(t||JSON.parse)(e),u.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Se={transitional:nt,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",o=n.indexOf("application/json")>-1,s=u.isObject(t);if(s&&u.isHTMLForm(t)&&(t=new FormData(t)),u.isFormData(t))return o?JSON.stringify(ot(t)):t;if(u.isArrayBuffer(t)||u.isBuffer(t)||u.isStream(t)||u.isFile(t)||u.isBlob(t)||u.isReadableStream(t))return t;if(u.isArrayBufferView(t))return t.buffer;if(u.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Lr(t,this.formSerializer).toString();if((a=u.isFileList(t))||n.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return ie(a?{"files[]":t}:t,f&&new f,this.formSerializer)}}return s||o?(r.setContentType("application/json",!1),Nr(t)):t}],transformResponse:[function(t){const r=this.transitional||Se.transitional,n=r&&r.forcedJSONParsing,o=this.responseType==="json";if(u.isResponse(t)||u.isReadableStream(t))return t;if(t&&u.isString(t)&&(n&&!this.responseType||o)){const i=!(r&&r.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?h.from(a,h.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:A.classes.FormData,Blob:A.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};u.forEach(["delete","get","head","post","put","patch"],e=>{Se.headers[e]={}});const Ae=Se,Ur=u.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),kr=e=>{const t={};let r,n,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),r=i.substring(0,o).trim().toLowerCase(),n=i.substring(o+1).trim(),!(!r||t[r]&&Ur[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Be=Symbol("internals");function q(e){return e&&String(e).trim().toLowerCase()}function G(e){return e===!1||e==null?e:u.isArray(e)?e.map(G):String(e)}function Ir(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const jr=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function fe(e,t,r,n,o){if(u.isFunction(n))return n.call(this,t,r);if(o&&(t=r),!!u.isString(t)){if(u.isString(n))return t.indexOf(n)!==-1;if(u.isRegExp(n))return n.test(t)}}function vr(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function qr(e,t){const r=u.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(o,s,i){return this[n].call(this,t,o,s,i)},configurable:!0})})}class ue{constructor(t){t&&this.set(t)}set(t,r,n){const o=this;function s(a,f,c){const l=q(f);if(!l)throw new Error("header name must be a non-empty string");const d=u.findKey(o,l);(!d||o[d]===void 0||c===!0||c===void 0&&o[d]!==!1)&&(o[d||f]=G(a))}const i=(a,f)=>u.forEach(a,(c,l)=>s(c,l,f));if(u.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(u.isString(t)&&(t=t.trim())&&!jr(t))i(kr(t),r);else if(u.isObject(t)&&u.isIterable(t)){let a={},f,c;for(const l of t){if(!u.isArray(l))throw TypeError("Object iterator must return a key-value pair");a[c=l[0]]=(f=a[c])?u.isArray(f)?[...f,l[1]]:[f,l[1]]:l[1]}i(a,r)}else t!=null&&s(r,t,n);return this}get(t,r){if(t=q(t),t){const n=u.findKey(this,t);if(n){const o=this[n];if(!r)return o;if(r===!0)return Ir(o);if(u.isFunction(r))return r.call(this,o,n);if(u.isRegExp(r))return r.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=q(t),t){const n=u.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||fe(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let o=!1;function s(i){if(i=q(i),i){const a=u.findKey(n,i);a&&(!r||fe(n,n[a],a,r))&&(delete n[a],o=!0)}}return u.isArray(t)?t.forEach(s):s(t),o}clear(t){const r=Object.keys(this);let n=r.length,o=!1;for(;n--;){const s=r[n];(!t||fe(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const r=this,n={};return u.forEach(this,(o,s)=>{const i=u.findKey(n,s);if(i){r[i]=G(o),delete r[s];return}const a=t?vr(s):String(s).trim();a!==s&&delete r[s],r[a]=G(o),n[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return u.forEach(this,(n,o)=>{n!=null&&n!==!1&&(r[o]=t&&u.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(o=>n.set(o)),n}static accessor(t){const n=(this[Be]=this[Be]={accessors:{}}).accessors,o=this.prototype;function s(i){const a=q(i);n[a]||(qr(o,i),n[a]=!0)}return u.isArray(t)?t.forEach(s):s(t),this}}ue.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);u.reduceDescriptors(ue.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});u.freezeMethods(ue);const D=ue;function de(e,t){const r=this||Ae,n=t||r,o=D.from(n.headers);let s=n.data;return u.forEach(e,function(a){s=a.call(r,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function st(e){return!!(e&&e.__CANCEL__)}function v(e,t,r){h.call(this,e??"canceled",h.ERR_CANCELED,t,r),this.name="CanceledError"}u.inherits(v,h,{__CANCEL__:!0});function it(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new h("Request failed with status code "+r.status,[h.ERR_BAD_REQUEST,h.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Vr(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Mr(e,t){e=e||10;const r=new Array(e),n=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(f){const c=Date.now(),l=n[s];i||(i=c),r[o]=f,n[o]=c;let d=s,g=0;for(;d!==o;)g+=r[d++],d=d%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<t)return;const _=l&&c-l;return _?Math.round(g*1e3/_):void 0}}function Hr(e,t){let r=0,n=1e3/t,o,s;const i=(c,l=Date.now())=>{r=l,o=null,s&&(clearTimeout(s),s=null),e(...c)};return[(...c)=>{const l=Date.now(),d=l-r;d>=n?i(c,l):(o=c,s||(s=setTimeout(()=>{s=null,i(o)},n-d)))},()=>o&&i(o)]}const ee=(e,t,r=3)=>{let n=0;const o=Mr(50,250);return Hr(s=>{const i=s.loaded,a=s.lengthComputable?s.total:void 0,f=i-n,c=o(f),l=i<=a;n=i;const d={loaded:i,total:a,progress:a?i/a:void 0,bytes:f,rate:c||void 0,estimated:c&&a&&l?(a-i)/c:void 0,event:s,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(d)},r)},xe=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Ne=e=>(...t)=>u.asap(()=>e(...t)),$r=A.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,A.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(A.origin),A.navigator&&/(msie|trident)/i.test(A.navigator.userAgent)):()=>!0,zr=A.hasStandardBrowserEnv?{write(e,t,r,n,o,s){const i=[e+"="+encodeURIComponent(t)];u.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),u.isString(n)&&i.push("path="+n),u.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Jr(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Wr(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ut(e,t,r){let n=!Jr(t);return e&&(n||r==!1)?Wr(e,t):t}const Ue=e=>e instanceof D?{...e}:e;function I(e,t){t=t||{};const r={};function n(c,l,d,g){return u.isPlainObject(c)&&u.isPlainObject(l)?u.merge.call({caseless:g},c,l):u.isPlainObject(l)?u.merge({},l):u.isArray(l)?l.slice():l}function o(c,l,d,g){if(u.isUndefined(l)){if(!u.isUndefined(c))return n(void 0,c,d,g)}else return n(c,l,d,g)}function s(c,l){if(!u.isUndefined(l))return n(void 0,l)}function i(c,l){if(u.isUndefined(l)){if(!u.isUndefined(c))return n(void 0,c)}else return n(void 0,l)}function a(c,l,d){if(d in t)return n(c,l);if(d in e)return n(void 0,c)}const f={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(c,l,d)=>o(Ue(c),Ue(l),d,!0)};return u.forEach(Object.keys({...e,...t}),function(l){const d=f[l]||o,g=d(e[l],t[l],l);u.isUndefined(g)&&d!==a||(r[l]=g)}),r}const at=e=>{const t=I({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:a}=t;t.headers=i=D.from(i),t.url=rt(ut(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let f;if(u.isFormData(r)){if(A.hasStandardBrowserEnv||A.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[c,...l]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...l].join("; "))}}if(A.hasStandardBrowserEnv&&(n&&u.isFunction(n)&&(n=n(t)),n||n!==!1&&$r(t.url))){const c=o&&s&&zr.read(s);c&&i.set(o,c)}return t},Kr=typeof XMLHttpRequest<"u",Xr=Kr&&function(e){return new Promise(function(r,n){const o=at(e);let s=o.data;const i=D.from(o.headers).normalize();let{responseType:a,onUploadProgress:f,onDownloadProgress:c}=o,l,d,g,_,p;function E(){_&&_(),p&&p(),o.cancelToken&&o.cancelToken.unsubscribe(l),o.signal&&o.signal.removeEventListener("abort",l)}let m=new XMLHttpRequest;m.open(o.method.toUpperCase(),o.url,!0),m.timeout=o.timeout;function b(){if(!m)return;const S=D.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),O={data:!a||a==="text"||a==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:S,config:e,request:m};it(function(U){r(U),E()},function(U){n(U),E()},O),m=null}"onloadend"in m?m.onloadend=b:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(b)},m.onabort=function(){m&&(n(new h("Request aborted",h.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new h("Network Error",h.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let L=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const O=o.transitional||nt;o.timeoutErrorMessage&&(L=o.timeoutErrorMessage),n(new h(L,O.clarifyTimeoutError?h.ETIMEDOUT:h.ECONNABORTED,e,m)),m=null},s===void 0&&i.setContentType(null),"setRequestHeader"in m&&u.forEach(i.toJSON(),function(L,O){m.setRequestHeader(O,L)}),u.isUndefined(o.withCredentials)||(m.withCredentials=!!o.withCredentials),a&&a!=="json"&&(m.responseType=o.responseType),c&&([g,p]=ee(c,!0),m.addEventListener("progress",g)),f&&m.upload&&([d,_]=ee(f),m.upload.addEventListener("progress",d),m.upload.addEventListener("loadend",_)),(o.cancelToken||o.signal)&&(l=S=>{m&&(n(!S||S.type?new v(null,e,m):S),m.abort(),m=null)},o.cancelToken&&o.cancelToken.subscribe(l),o.signal&&(o.signal.aborted?l():o.signal.addEventListener("abort",l)));const R=Vr(o.url);if(R&&A.protocols.indexOf(R)===-1){n(new h("Unsupported protocol "+R+":",h.ERR_BAD_REQUEST,e));return}m.send(s||null)})},Gr=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,o;const s=function(c){if(!o){o=!0,a();const l=c instanceof Error?c:this.reason;n.abort(l instanceof h?l:new v(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,s(new h(`timeout ${t} of ms exceeded`,h.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(s):c.removeEventListener("abort",s)}),e=null)};e.forEach(c=>c.addEventListener("abort",s));const{signal:f}=n;return f.unsubscribe=()=>u.asap(a),f}},Yr=Gr,Qr=function*(e,t){let r=e.byteLength;if(!t||r<t){yield e;return}let n=0,o;for(;n<r;)o=n+t,yield e.slice(n,o),n=o},Zr=async function*(e,t){for await(const r of en(e))yield*Qr(r,t)},en=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},ke=(e,t,r,n)=>{const o=Zr(e,t);let s=0,i,a=f=>{i||(i=!0,n&&n(f))};return new ReadableStream({async pull(f){try{const{done:c,value:l}=await o.next();if(c){a(),f.close();return}let d=l.byteLength;if(r){let g=s+=d;r(g)}f.enqueue(new Uint8Array(l))}catch(c){throw a(c),c}},cancel(f){return a(f),o.return()}},{highWaterMark:2})},ae=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ct=ae&&typeof ReadableStream=="function",tn=ae&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),lt=(e,...t)=>{try{return!!e(...t)}catch{return!1}},rn=ct&&lt(()=>{let e=!1;const t=new Request(A.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ie=64*1024,ye=ct&&lt(()=>u.isReadableStream(new Response("").body)),te={stream:ye&&(e=>e.body)};ae&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!te[t]&&(te[t]=u.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new h(`Response type '${t}' is not supported`,h.ERR_NOT_SUPPORT,n)})})})(new Response);const nn=async e=>{if(e==null)return 0;if(u.isBlob(e))return e.size;if(u.isSpecCompliantForm(e))return(await new Request(A.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(u.isArrayBufferView(e)||u.isArrayBuffer(e))return e.byteLength;if(u.isURLSearchParams(e)&&(e=e+""),u.isString(e))return(await tn(e)).byteLength},on=async(e,t)=>{const r=u.toFiniteNumber(e.getContentLength());return r??nn(t)},sn=ae&&(async e=>{let{url:t,method:r,data:n,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:f,responseType:c,headers:l,withCredentials:d="same-origin",fetchOptions:g}=at(e);c=c?(c+"").toLowerCase():"text";let _=Yr([o,s&&s.toAbortSignal()],i),p;const E=_&&_.unsubscribe&&(()=>{_.unsubscribe()});let m;try{if(f&&rn&&r!=="get"&&r!=="head"&&(m=await on(l,n))!==0){let O=new Request(t,{method:"POST",body:n,duplex:"half"}),B;if(u.isFormData(n)&&(B=O.headers.get("content-type"))&&l.setContentType(B),O.body){const[U,W]=xe(m,ee(Ne(f)));n=ke(O.body,Ie,U,W)}}u.isString(d)||(d=d?"include":"omit");const b="credentials"in Request.prototype;p=new Request(t,{...g,signal:_,method:r.toUpperCase(),headers:l.normalize().toJSON(),body:n,duplex:"half",credentials:b?d:void 0});let R=await fetch(p,g);const S=ye&&(c==="stream"||c==="response");if(ye&&(a||S&&E)){const O={};["status","statusText","headers"].forEach(Te=>{O[Te]=R[Te]});const B=u.toFiniteNumber(R.headers.get("content-length")),[U,W]=a&&xe(B,ee(Ne(a),!0))||[];R=new Response(ke(R.body,Ie,U,()=>{W&&W(),E&&E()}),O)}c=c||"text";let L=await te[u.findKey(te,c)||"text"](R,e);return!S&&E&&E(),await new Promise((O,B)=>{it(O,B,{data:L,headers:D.from(R.headers),status:R.status,statusText:R.statusText,config:e,request:p})})}catch(b){throw E&&E(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new h("Network Error",h.ERR_NETWORK,e,p),{cause:b.cause||b}):h.from(b,b&&b.code,e,p)}}),ge={http:gr,xhr:Xr,fetch:sn};u.forEach(ge,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const je=e=>`- ${e}`,un=e=>u.isFunction(e)||e===null||e===!1,ft={getAdapter:e=>{e=u.isArray(e)?e:[e];const{length:t}=e;let r,n;const o={};for(let s=0;s<t;s++){r=e[s];let i;if(n=r,!un(r)&&(n=ge[(i=String(r)).toLowerCase()],n===void 0))throw new h(`Unknown adapter '${i}'`);if(n)break;o[i||"#"+s]=n}if(!n){const s=Object.entries(o).map(([a,f])=>`adapter ${a} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(je).join(`
`):" "+je(s[0]):"as no adapter specified";throw new h("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:ge};function pe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new v(null,e)}function ve(e){return pe(e),e.headers=D.from(e.headers),e.data=de.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ft.getAdapter(e.adapter||Ae.adapter)(e).then(function(n){return pe(e),n.data=de.call(e,e.transformResponse,n),n.headers=D.from(n.headers),n},function(n){return st(n)||(pe(e),n&&n.response&&(n.response.data=de.call(e,e.transformResponse,n.response),n.response.headers=D.from(n.response.headers))),Promise.reject(n)})}const dt="1.11.0",ce={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ce[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const qe={};ce.transitional=function(t,r,n){function o(s,i){return"[Axios v"+dt+"] Transitional option '"+s+"'"+i+(n?". "+n:"")}return(s,i,a)=>{if(t===!1)throw new h(o(i," has been removed"+(r?" in "+r:"")),h.ERR_DEPRECATED);return r&&!qe[i]&&(qe[i]=!0,console.warn(o(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(s,i,a):!0}};ce.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function an(e,t,r){if(typeof e!="object")throw new h("options must be an object",h.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let o=n.length;for(;o-- >0;){const s=n[o],i=t[s];if(i){const a=e[s],f=a===void 0||i(a,s,e);if(f!==!0)throw new h("option "+s+" must be "+f,h.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new h("Unknown option "+s,h.ERR_BAD_OPTION)}}const Y={assertOptions:an,validators:ce},C=Y.validators;class re{constructor(t){this.defaults=t||{},this.interceptors={request:new Le,response:new Le}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{n.stack?s&&!String(n.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+s):n.stack=s}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=I(this.defaults,r);const{transitional:n,paramsSerializer:o,headers:s}=r;n!==void 0&&Y.assertOptions(n,{silentJSONParsing:C.transitional(C.boolean),forcedJSONParsing:C.transitional(C.boolean),clarifyTimeoutError:C.transitional(C.boolean)},!1),o!=null&&(u.isFunction(o)?r.paramsSerializer={serialize:o}:Y.assertOptions(o,{encode:C.function,serialize:C.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Y.assertOptions(r,{baseUrl:C.spelling("baseURL"),withXsrfToken:C.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=s&&u.merge(s.common,s[r.method]);s&&u.forEach(["delete","get","head","post","put","patch","common"],p=>{delete s[p]}),r.headers=D.concat(i,s);const a=[];let f=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(r)===!1||(f=f&&E.synchronous,a.unshift(E.fulfilled,E.rejected))});const c=[];this.interceptors.response.forEach(function(E){c.push(E.fulfilled,E.rejected)});let l,d=0,g;if(!f){const p=[ve.bind(this),void 0];for(p.unshift(...a),p.push(...c),g=p.length,l=Promise.resolve(r);d<g;)l=l.then(p[d++],p[d++]);return l}g=a.length;let _=r;for(d=0;d<g;){const p=a[d++],E=a[d++];try{_=p(_)}catch(m){E.call(this,m);break}}try{l=ve.call(this,_)}catch(p){return Promise.reject(p)}for(d=0,g=c.length;d<g;)l=l.then(c[d++],c[d++]);return l}getUri(t){t=I(this.defaults,t);const r=ut(t.baseURL,t.url,t.allowAbsoluteUrls);return rt(r,t.params,t.paramsSerializer)}}u.forEach(["delete","get","head","options"],function(t){re.prototype[t]=function(r,n){return this.request(I(n||{},{method:t,url:r,data:(n||{}).data}))}});u.forEach(["post","put","patch"],function(t){function r(n){return function(s,i,a){return this.request(I(a||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}re.prototype[t]=r(),re.prototype[t+"Form"]=r(!0)});const Q=re;class Oe{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(s){r=s});const n=this;this.promise.then(o=>{if(!n._listeners)return;let s=n._listeners.length;for(;s-- >0;)n._listeners[s](o);n._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(a=>{n.subscribe(a),s=a}).then(o);return i.cancel=function(){n.unsubscribe(s)},i},t(function(s,i,a){n.reason||(n.reason=new v(s,i,a),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Oe(function(o){t=o}),cancel:t}}}const cn=Oe;function ln(e){return function(r){return e.apply(null,r)}}function fn(e){return u.isObject(e)&&e.isAxiosError===!0}const be={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(be).forEach(([e,t])=>{be[t]=e});const dn=be;function pt(e){const t=new Q(e),r=$e(Q.prototype.request,t);return u.extend(r,Q.prototype,t,{allOwnKeys:!0}),u.extend(r,t,null,{allOwnKeys:!0}),r.create=function(o){return pt(I(e,o))},r}const w=pt(Ae);w.Axios=Q;w.CanceledError=v;w.CancelToken=cn;w.isCancel=st;w.VERSION=dt;w.toFormData=ie;w.AxiosError=h;w.Cancel=w.CanceledError;w.all=function(t){return Promise.all(t)};w.spread=ln;w.isAxiosError=fn;w.mergeConfig=I;w.AxiosHeaders=D;w.formToJSON=e=>ot(u.isHTMLForm(e)?new FormData(e):e);w.getAdapter=ft.getAdapter;w.HttpStatusCode=dn;w.default=w;const pn=w,F=pn.create({baseURL:"/api",timeout:1e4});F.interceptors.request.use(e=>{const t=V();return t.token&&(e.headers.Authorization=`Bearer ${t.token}`),e},e=>(console.error("Request error:",e),Promise.reject(e)));F.interceptors.response.use(e=>{const t=e.data;return t.code!==200?(N.error(t.message||"请求失败"),t.code===401&&(N.error(t.message||"账号或密码输入错误"),Ot.confirm("账号或密码输入错误，请重新登录","系统提示",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{V().logout().then(()=>{Z.push("/login")})}).catch(()=>{V().logout().then(()=>{Z.push("/login")})})),Promise.reject(new Error(t.message||"请求失败"))):t},e=>{console.error("Response error:",e);let t="请求失败";if(e.response){const{status:r,data:n}=e.response;switch(r){case 400:t=n.message||"请求参数错误";break;case 401:t=n.message||"账号或密码输入错误",setTimeout(()=>{V().logout().then(()=>{Z.push("/login")})},100);break;case 403:t="拒绝访问";break;case 404:t="请求的资源不存在";break;case 500:t="服务器内部错误";break;default:t=n.message||`请求失败 (${r})`}}else(e.code==="ECONNABORTED"||e.message)&&(t="网络错误，请联系管理员");return N.error(t),Promise.reject(e)});const K={login(e){return F({url:"/auth/login",method:"post",data:e})},logout(){return F({url:"/auth/logout",method:"post"})},getProfile(){return F({url:"/auth/profile",method:"get"})},updateProfile(e){return F({url:"/auth/profile",method:"put",data:e})},getSecurityStatus(){return F({url:"/auth/security/status",method:"get"})},unlockAccount(e){return F({url:`/auth/security/unlock/${e}`,method:"post"})},getUserList(e){return F({url:"/users",method:"get",params:e})},createUser(e){return F({url:"/users",method:"post",data:e})},getUser(e){return F({url:`/users/${e}`,method:"get"})},updateUser(e,t){return F({url:`/users/${e}`,method:"put",data:t})},deleteUser(e){return F({url:`/users/${e}`,method:"delete"})}},V=bt("auth",()=>{const e=le(localStorage.getItem("token")||""),t=le(JSON.parse(localStorage.getItem("user")||"null")),r=le(!1),n=wt(()=>!!e.value&&!!t.value),o=async f=>{r.value=!0;try{const c=await K.login(f);if(c.code===200){const{token:l,user:d}=c.data;return e.value=l,t.value=d,localStorage.setItem("token",l),localStorage.setItem("user",JSON.stringify(d)),N.success("登录成功"),!0}else throw new Error(c.message||"登录失败")}catch(c){throw c}finally{r.value=!1}},s=async()=>{try{e.value&&await K.logout()}catch(f){console.error("Logout error:",f)}finally{e.value="",t.value=null,localStorage.removeItem("token"),localStorage.removeItem("user"),N.success("已退出登录")}},i=async()=>{try{const f=await K.getProfile();f.code===200&&(t.value=f.data,localStorage.setItem("user",JSON.stringify(f.data)))}catch(f){console.error("Fetch profile error:",f)}};return{token:e,user:t,loading:r,isLoggedIn:n,login:o,logout:s,fetchProfile:i,updateProfile:async f=>{try{const c=await K.updateProfile(f);return c.code===200?(await i(),N.success("更新成功"),!0):(N.error(c.message||"更新失败"),!1)}catch(c){return N.error(c.message||"更新失败"),!1}}}}),x=()=>y(()=>import("./index-ff892955.js"),["assets/index-ff892955.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/index-d2fc7ccd.css","assets/el-button-f2767652.css","assets/el-dropdown-item-b7fb1426.css","assets/el-scrollbar-d2ed595a.css"]),mn=()=>y(()=>import("./index-2507ee7b.js"),["assets/index-2507ee7b.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/index-9897de7f.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css"]),hn=()=>y(()=>import("./index-964b28be.js"),["assets/index-964b28be.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/charts-d5c9e496.js","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/index-8805c370.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css"]),Ve=()=>y(()=>import("./list-f7c3fcb6.js"),["assets/list-f7c3fcb6.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/project-3f17a178.js","assets/elementPlus-05e8f3ef.js","assets/form-f586b152.js","assets/form-125faf68.css","assets/el-card-7155ea48.css","assets/el-tab-pane-3f3f12a2.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-form-item-a926de16.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-button-f2767652.css","assets/list-0551d9ef.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-dropdown-item-b7fb1426.css"]),Me=()=>y(()=>import("./form-f586b152.js"),["assets/form-f586b152.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/project-3f17a178.js","assets/elementPlus-05e8f3ef.js","assets/form-125faf68.css","assets/el-card-7155ea48.css","assets/el-tab-pane-3f3f12a2.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-form-item-a926de16.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-button-f2767652.css"]),En=()=>y(()=>import("./stats-dbd4d206.js"),["assets/stats-dbd4d206.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/charts-d5c9e496.js","assets/vendor-ad470fc0.js","assets/spider-fa0c41a6.js","assets/elementPlus-05e8f3ef.js","assets/stats-32969ec9.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),yn=()=>y(()=>import("./today-abdef144.js"),["assets/today-abdef144.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/spider-fa0c41a6.js","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/today-34a74b20.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),gn=()=>y(()=>import("./yesterday-c5bd9bf2.js"),["assets/yesterday-c5bd9bf2.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/charts-d5c9e496.js","assets/vendor-ad470fc0.js","assets/spider-fa0c41a6.js","assets/elementPlus-05e8f3ef.js","assets/yesterday-d0e38197.css","assets/el-loading-de58975a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),bn=()=>y(()=>import("./logs-ef3242cf.js"),["assets/logs-ef3242cf.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/spider-fa0c41a6.js","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/logs-979ca32f.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css","assets/el-card-7155ea48.css"]),wn=()=>y(()=>import("./list-f4e45ceb.js"),["assets/list-f4e45ceb.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/list-2fa51596.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),He=()=>y(()=>import("./form-2abb320c.js"),["assets/form-2abb320c.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/el-empty-06735b9d.css"]),_n=()=>y(()=>import("./list-b96a840e.js"),["assets/list-b96a840e.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/list-5c19e3ef.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css"]),Rn=()=>y(()=>import("./index-b6219dd8.js"),["assets/index-b6219dd8.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/el-empty-06735b9d.css"]),Sn=[{path:"/login",name:"Login",component:mn,meta:{title:"登录",requiresAuth:!1}},{path:"/",component:x,redirect:"/dashboard",meta:{requiresAuth:!0},children:[{path:"dashboard",name:"Dashboard",component:hn,meta:{title:"Dashboard",icon:"TrendCharts"}}]},{path:"/project",component:x,meta:{title:"项目管理",icon:"User",requiresAuth:!0},children:[{path:"standalone",name:"ProjectStandalone",component:Ve,meta:{title:"项目列表(单例模式)"}},{path:"interface",name:"ProjectInterface",component:Ve,meta:{title:"项目列表(接口模式)"}},{path:"url-rules",name:"ProjectUrlRules",component:()=>y(()=>import("./url-rules-b1e7729a.js"),["assets/url-rules-b1e7729a.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/url-rules-90ac264d.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-radio-eac3aa84.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css"]),meta:{title:"URL规则管理"}},{path:"external-links",name:"ProjectExternalLinks",component:()=>y(()=>import("./external-links-7096e827.js"),["assets/external-links-7096e827.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/external-links-fa6230db.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css"]),meta:{title:"外链管理"}},{path:"create",name:"ProjectCreate",component:Me,meta:{title:"创建项目",hidden:!0}},{path:"interface/create",name:"ProjectInterfaceCreate",component:()=>y(()=>import("./interface-form-3fae134e.js"),["assets/interface-form-3fae134e.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/project-3f17a178.js","assets/elementPlus-05e8f3ef.js","assets/interface-form-51e007a3.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"创建接口项目",hidden:!0}},{path:"interface/edit/:id",name:"ProjectInterfaceEdit",component:()=>y(()=>import("./interface-form-3fae134e.js"),["assets/interface-form-3fae134e.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/project-3f17a178.js","assets/elementPlus-05e8f3ef.js","assets/interface-form-51e007a3.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"编辑接口项目",hidden:!0}},{path:"edit/:id",name:"ProjectEdit",component:Me,meta:{title:"编辑项目",hidden:!0}},{path:":id/urls",name:"ProjectUrlManage",component:()=>y(()=>import("./url-rules-b1e7729a.js"),["assets/url-rules-b1e7729a.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-05e8f3ef.js","assets/url-rules-90ac264d.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-radio-eac3aa84.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css"]),meta:{title:"项目URL管理",hidden:!0}}]},{path:"/spider",component:x,meta:{title:"蜘蛛管理",icon:"Promotion",requiresAuth:!0},children:[{path:"today",name:"SpiderToday",component:yn,meta:{title:"今日蜘蛛统计"}},{path:"yesterday",name:"SpiderYesterday",component:gn,meta:{title:"昨日蜘蛛统计"}},{path:"stats",name:"SpiderStats",component:En,meta:{title:"蜘蛛数据看板"}},{path:"logs",name:"SpiderLogs",component:bn,meta:{title:"访问日志"}}]},{path:"/tools",component:x,meta:{title:"辅助工具",icon:"Tools",requiresAuth:!0},children:[{path:"spider-simulator",name:"SpiderSimulator",component:()=>y(()=>import("./spider-simulator-19217142.js"),["assets/spider-simulator-19217142.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/spider-simulator-c7cbe110.css","assets/el-tab-pane-3f3f12a2.css","assets/el-col-60e44389.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"在线蜘蛛模拟"}},{path:"404-hijack-detector",name:"404HijackDetector",component:()=>y(()=>import("./404-hijack-detector-8c8dce51.js"),["assets/404-hijack-detector-8c8dce51.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/404-hijack-detector-63a7e52e.css","assets/el-tab-pane-3f3f12a2.css","assets/el-col-60e44389.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"404劫持检测"}},{path:"htaccess-generator",name:"HtaccessGenerator",component:()=>y(()=>import("./htaccess-generator-476fd984.js"),["assets/htaccess-generator-476fd984.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/htaccess-generator-df4bbba2.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css"]),meta:{title:".htaccess全站劫持生成"}},{path:"iis6-generator",name:"IIS6Generator",component:()=>y(()=>import("./iis6-generator-031eed18.js"),["assets/iis6-generator-031eed18.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/iis6-generator-15a4ddcf.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css"]),meta:{title:"IIS 6 站群劫持生成"}},{path:"iis-domain-export",name:"IISDomainExport",component:()=>y(()=>import("./iis-domain-export-b95599c4.js"),["assets/iis-domain-export-b95599c4.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/iis-domain-export-90557033.css","assets/el-card-7155ea48.css"]),meta:{title:"IIS 域名导出"}},{path:"file-index-encrypt",name:"FileIndexEncrypt",component:()=>y(()=>import("./file-index-encrypt-9160157a.js"),["assets/file-index-encrypt-9160157a.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/file-index-encrypt-b98b7592.css","assets/el-checkbox-group-aabff9cd.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css","assets/el-checkbox-4bf2f35b.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-radio-eac3aa84.css"]),meta:{title:"文件索引加密"}},{path:"js-encrypt-decrypt",name:"JSEncryptDecrypt",component:()=>y(()=>import("./js-encrypt-decrypt-d0ace9b3.js"),["assets/js-encrypt-decrypt-d0ace9b3.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/js-encrypt-decrypt-08c100c7.css","assets/el-tab-pane-3f3f12a2.css","assets/el-tag-afac09bb.css","assets/el-checkbox-group-aabff9cd.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css","assets/el-checkbox-4bf2f35b.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css"]),meta:{title:"JS 加密 or 解密"}}]},{path:"/system",component:x,meta:{title:"系统设置",icon:"Setting",requiresAuth:!0},children:[{path:"users",name:"UserList",component:wn,meta:{title:"用户管理"}},{path:"users/create",name:"UserCreate",component:He,meta:{title:"创建用户",hidden:!0}},{path:"users/edit/:id",name:"UserEdit",component:He,meta:{title:"编辑用户",hidden:!0}},{path:"useragent",name:"UserAgentList",component:_n,meta:{title:"User-Agent管理"}}]},{path:"/permission",component:x,meta:{title:"权限控制",icon:"Lock",requiresAuth:!0},children:[{path:"roles",name:"RoleList",component:()=>y(()=>import("./roles-d6f51fcc.js"),["assets/roles-d6f51fcc.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/roles-96ca680c.css","assets/el-card-7155ea48.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css"]),meta:{title:"角色管理"}},{path:"permissions",name:"PermissionList",component:()=>y(()=>import("./permissions-c1189fce.js"),["assets/permissions-c1189fce.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/permissions-af52c065.css","assets/el-card-7155ea48.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css"]),meta:{title:"权限管理"}}]},{path:"/sites",component:x,redirect:"/sites/list",meta:{title:"站群管理",icon:"Connection",requiresAuth:!1},children:[{path:"list",name:"SiteList",component:()=>y(()=>import("./list-e93494c8.js"),["assets/list-e93494c8.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/elementPlus-05e8f3ef.js","assets/vendor-ad470fc0.js","assets/list-7f42df47.css","assets/el-tab-pane-3f3f12a2.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-card-7155ea48.css","assets/el-input-a97a1ae3.css","assets/el-button-f2767652.css"]),meta:{title:"站点列表"}}]},{path:"/profile",component:x,meta:{requiresAuth:!0},children:[{path:"",name:"Profile",component:Rn,meta:{title:"个人资料"}}]}],mt=_t({history:Rt(),routes:Sn});mt.beforeEach((e,t,r)=>{const n=V();if(e.meta.title&&(document.title=`${e.meta.title} - SEO Platform`),e.meta.requiresAuth!==!1&&!n.isLoggedIn){r("/login");return}if(e.path==="/login"&&n.isLoggedIn){r("/");return}r()});const Z=mt;const J=St(Ct);for(const[e,t]of Object.entries(Tt))J.component(e,t);J.use(At());J.use(Z);J.use(Ft,{locale:Dt});J.mount("#app");export{F as s,V as u};
