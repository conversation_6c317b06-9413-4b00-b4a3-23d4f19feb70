import{s as e}from"./index-bb3d8812.js";const u={getList(t){return e({url:"/apis",method:"get",params:t})},create(t){return e({url:"/apis",method:"post",data:t})},get(t){return e({url:`/apis/${t}`,method:"get"})},update(t,r){return e({url:`/apis/${t}`,method:"put",data:r})},delete(t){return e({url:`/apis/${t}`,method:"delete"})},test(t){return e({url:`/apis/${t}/test`,method:"post"})}};export{u as a};
