import{s as r}from"./index-bb3d8812.js";const d={getList(e){return r({url:"/keywords",method:"get",params:e})},create(e){return r({url:"/keywords",method:"post",data:e})},get(e){return r({url:`/keywords/${e}`,method:"get"})},update(e,t){return r({url:`/keywords/${e}`,method:"put",data:t})},delete(e){return r({url:`/keywords/${e}`,method:"delete"})},import(e,t){return r({url:`/keywords/${e}/import`,method:"post",data:t})},export(e){return r({url:`/keywords/${e}/export`,method:"get"})}};export{d as k};
