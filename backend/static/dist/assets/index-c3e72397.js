import{_ as x}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                     *//* empty css                  *//* empty css                 */import{aC as V,r as m,X as h,y as f,z as E,A as r,Q as s,I as l,H as b,L as k,M as z,a5 as B}from"./vendor-ad470fc0.js";import{u as C}from"./index-bb3d8812.js";import{s as F,u as S,h as I,v as L}from"./elementPlus-ef333120.js";const N={class:"login-container"},R={class:"login-box"},U={__name:"index",setup(q){const _=V(),g=C(),n=m(),d=m(!1),p=m(!1),o=h({username:"",password:"",code:""}),v={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],code:[{required:p.value,message:"请输入验证码",trigger:"blur"}]},c=async()=>{if(n.value)try{await n.value.validate(),d.value=!0,await g.login({username:o.username,password:o.password,code:o.code})&&_.push("/")}catch(u){console.error("Login validation failed:",u)}finally{d.value=!1}};return(u,e)=>{const i=F,t=S,w=I,y=L;return f(),E("div",N,[r("div",R,[e[4]||(e[4]=r("div",{class:"login-header"},[r("h2",null,"SEO Platform"),r("p",null,"SEO优化管理平台")],-1)),s(y,{ref_key:"loginFormRef",ref:n,model:o,rules:v,class:"login-form",onKeyup:B(c,["enter"])},{default:l(()=>[s(t,{prop:"username"},{default:l(()=>[s(i,{modelValue:o.username,"onUpdate:modelValue":e[0]||(e[0]=a=>o.username=a),placeholder:"用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),s(t,{prop:"password"},{default:l(()=>[s(i,{modelValue:o.password,"onUpdate:modelValue":e[1]||(e[1]=a=>o.password=a),type:"password",placeholder:"密码",size:"large","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),p.value?(f(),b(t,{key:0,prop:"code"},{default:l(()=>[s(i,{modelValue:o.code,"onUpdate:modelValue":e[2]||(e[2]=a=>o.code=a),placeholder:"验证码",size:"large","prefix-icon":"Key"},null,8,["modelValue"])]),_:1})):k("",!0),s(t,null,{default:l(()=>[s(w,{type:"primary",size:"large",loading:d.value,onClick:c,class:"login-btn"},{default:l(()=>e[3]||(e[3]=[z(" 登录 ",-1)])),_:1,__:[3]},8,["loading"])]),_:1})]),_:1},8,["model"]),e[5]||(e[5]=r("div",{class:"login-footer"},[r("p",null,"© 2024 SEO Platform. All rights reserved.")],-1))])])}}},T=x(U,[["__scopeId","data-v-11fdd2d1"]]);export{T as default};
