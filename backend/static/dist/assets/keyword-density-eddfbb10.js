import{_ as H}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                 *//* empty css                 *//* empty css                    *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                   *//* empty css               *//* empty css                     *//* empty css                     *//* empty css                  *//* empty css                          *//* empty css                 *//* empty css                 *//* empty css                */import{E as V,c as J,x as Q,aj as Y,ak as Z,u as $,s as tt,ag as et,ah as lt,h as ot,v as at,y as st,A as nt,ai as rt,z as dt,al as it,B as ut,C as pt,I as _t,am as yt,a6 as mt,a2 as ft,an as ct,R as wt,a9 as gt,ao as kt,t as vt,ap as bt}from"./elementPlus-ef333120.js";import{h as ht}from"./charts-5a62b0c7.js";import{r as S,X as Et,al as xt,y as i,z as f,Q as t,I as l,A as r,u as _,M as s,H as c,L as g,O as w,P as N,a4 as z,n as A}from"./vendor-ad470fc0.js";const Vt={class:"keyword-density-container"},Ct={class:"header-content"},Ot={class:"results-header"},St={class:"stats-overview"},Tt={key:0,class:"target-keywords"},Ut={class:"top-keywords"},Lt={style:{"margin-left":"10px"}},Rt={key:1,class:"density-chart"},Bt={key:2,class:"suggestions"},It={__name:"keyword-density",setup(Dt){const k=S(!1),n=S(null),C=S(null),a=Et({type:"text",url:"",content:"",targetKeywords:"",options:["stopWords"]}),T=async()=>{if(a.type==="url"&&!a.url){V.warning("请输入要分析的网页URL");return}if(a.type==="text"&&!a.content){V.warning("请输入要分析的文本内容");return}k.value=!0;try{const d=await fetch("/api/tools/keyword-density",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:a.type,url:a.url,content:a.content,target_keywords:a.targetKeywords.split(",").map(e=>e.trim()).filter(e=>e),options:a.options})});if(d.ok)n.value=await d.json(),V.success("关键词密度分析完成"),A(()=>{U()});else throw new Error("分析失败")}catch(d){V.error("分析失败: "+d.message),n.value={total_words:856,unique_words:324,total_chars:4280,paragraphs:8,target_analysis:[{keyword:"SEO优化",count:12,density:1.4,positions:["标题","第1段","第3段"],suggestion:"密度适中，建议在第5段再次提及"},{keyword:"关键词分析",count:8,density:.9,positions:["第2段","第4段"],suggestion:"密度偏低，建议适当增加"}],top_keywords:[{rank:1,keyword:"网站",count:25,density:2.9,category:"名词"},{rank:2,keyword:"优化",count:18,density:2.1,category:"动词"},{rank:3,keyword:"SEO",count:15,density:1.8,category:"名词"},{rank:4,keyword:"搜索",count:12,density:1.4,category:"动词"},{rank:5,keyword:"排名",count:10,density:1.2,category:"名词"}],suggestions:[{title:"关键词密度优化",description:"主要关键词密度在1-3%之间较为理想，当前部分关键词密度偏低",type:"warning"},{title:"关键词分布",description:"建议在文章开头、中间和结尾都适当出现目标关键词",type:"info"}]},A(()=>{U()})}finally{k.value=!1}},U=()=>{if(!C.value||!n.value)return;const d=ht(C.value),e={title:{text:"关键词密度分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c}% ({d}%)"},series:[{name:"关键词密度",type:"pie",radius:"50%",data:n.value.top_keywords.slice(0,8).map(u=>({value:u.density,name:u.keyword})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};d.setOption(e)},K=d=>d>=1&&d<=3?"success":d>3?"warning":"info",j=d=>({名词:"primary",动词:"success",形容词:"warning",副词:"info"})[d]||"default";return(d,e)=>{const u=J,v=Q,L=Y,F=Z,y=$,O=tt,b=et,P=lt,R=ot,q=at,m=st,h=nt,E=rt,B=dt,I=it,p=ut,D=pt,G=_t,M=xt("Lightbulb"),W=yt,X=mt;return i(),f("div",Vt,[t(v,{class:"page-header"},{default:l(()=>[r("div",Ct,[r("h2",null,[t(u,null,{default:l(()=>[t(_(ft))]),_:1}),e[5]||(e[5]=s(" 关键词密度分析器",-1))]),e[6]||(e[6]=r("p",null,"分析网页或文本内容中关键词的分布密度，优化SEO效果",-1))])]),_:1}),t(B,{gutter:20},{default:l(()=>[t(m,{span:10},{default:l(()=>[t(v,{class:"analysis-form"},{header:l(()=>[r("span",null,[t(u,null,{default:l(()=>[t(_(ct))]),_:1}),e[7]||(e[7]=s(" 内容分析",-1))])]),default:l(()=>[t(q,{model:a,"label-width":"80px"},{default:l(()=>[t(y,{label:"分析方式"},{default:l(()=>[t(F,{modelValue:a.type,"onUpdate:modelValue":e[0]||(e[0]=o=>a.type=o)},{default:l(()=>[t(L,{label:"url"},{default:l(()=>e[8]||(e[8]=[s("网页URL",-1)])),_:1,__:[8]}),t(L,{label:"text"},{default:l(()=>e[9]||(e[9]=[s("文本内容",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1}),a.type==="url"?(i(),c(y,{key:0,label:"网页URL"},{default:l(()=>[t(O,{modelValue:a.url,"onUpdate:modelValue":e[1]||(e[1]=o=>a.url=o),placeholder:"请输入要分析的网页URL"},{prepend:l(()=>e[10]||(e[10]=[s("https://",-1)])),_:1},8,["modelValue"])]),_:1})):g("",!0),a.type==="text"?(i(),c(y,{key:1,label:"文本内容"},{default:l(()=>[t(O,{modelValue:a.content,"onUpdate:modelValue":e[2]||(e[2]=o=>a.content=o),type:"textarea",rows:8,placeholder:"请输入要分析的文本内容...","show-word-limit":"",maxlength:"10000"},null,8,["modelValue"])]),_:1})):g("",!0),t(y,{label:"目标关键词"},{default:l(()=>[t(O,{modelValue:a.targetKeywords,"onUpdate:modelValue":e[3]||(e[3]=o=>a.targetKeywords=o),placeholder:"输入目标关键词，用逗号分隔"},null,8,["modelValue"]),e[11]||(e[11]=r("small",null,"例如: SEO优化,关键词分析,网站排名",-1))]),_:1,__:[11]}),t(y,{label:"分析选项"},{default:l(()=>[t(P,{modelValue:a.options,"onUpdate:modelValue":e[4]||(e[4]=o=>a.options=o)},{default:l(()=>[t(b,{label:"stopWords"},{default:l(()=>e[12]||(e[12]=[s("过滤停用词",-1)])),_:1,__:[12]}),t(b,{label:"stemming"},{default:l(()=>e[13]||(e[13]=[s("词干提取",-1)])),_:1,__:[13]}),t(b,{label:"phrases"},{default:l(()=>e[14]||(e[14]=[s("分析短语",-1)])),_:1,__:[14]}),t(b,{label:"synonyms"},{default:l(()=>e[15]||(e[15]=[s("同义词分析",-1)])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1}),t(y,null,{default:l(()=>[t(R,{type:"primary",onClick:T,loading:k.value,style:{width:"100%"}},{default:l(()=>[t(u,null,{default:l(()=>[t(_(wt))]),_:1}),s(" "+w(k.value?"分析中...":"开始分析"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),t(m,{span:14},{default:l(()=>[n.value?(i(),c(v,{key:0,class:"analysis-results"},{header:l(()=>[r("div",Ot,[r("span",null,[t(u,null,{default:l(()=>[t(_(gt))]),_:1}),e[16]||(e[16]=s(" 分析结果",-1))]),t(h,{type:"info"},{default:l(()=>[s("总词数: "+w(n.value.total_words),1)]),_:1})])]),default:l(()=>[r("div",St,[t(B,{gutter:20},{default:l(()=>[t(m,{span:6},{default:l(()=>[t(E,{title:"总词数",value:n.value.total_words},null,8,["value"])]),_:1}),t(m,{span:6},{default:l(()=>[t(E,{title:"唯一词数",value:n.value.unique_words},null,8,["value"])]),_:1}),t(m,{span:6},{default:l(()=>[t(E,{title:"字符数",value:n.value.total_chars},null,8,["value"])]),_:1}),t(m,{span:6},{default:l(()=>[t(E,{title:"段落数",value:n.value.paragraphs},null,8,["value"])]),_:1})]),_:1})]),t(I),n.value.target_analysis?(i(),f("div",Tt,[r("h4",null,[t(u,null,{default:l(()=>[t(_(kt))]),_:1}),e[17]||(e[17]=s(" 目标关键词分析",-1))]),t(D,{data:n.value.target_analysis,style:{width:"100%"}},{default:l(()=>[t(p,{prop:"keyword",label:"关键词",width:"150"}),t(p,{prop:"count",label:"出现次数",width:"100"}),t(p,{prop:"density",label:"密度",width:"100"},{default:l(o=>[t(h,{type:K(o.row.density)},{default:l(()=>[s(w(o.row.density)+"% ",1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"positions",label:"位置分布",width:"200"},{default:l(o=>[(i(!0),f(N,null,z(o.row.positions,x=>(i(),c(h,{key:x,size:"small",style:{"margin-right":"5px"}},{default:l(()=>[s(w(x),1)]),_:2},1024))),128))]),_:1}),t(p,{prop:"suggestion",label:"建议"})]),_:1},8,["data"])])):g("",!0),t(I),r("div",Ut,[r("h4",null,[t(u,null,{default:l(()=>[t(_(vt))]),_:1}),e[18]||(e[18]=s(" 高频词汇分析",-1))]),t(D,{data:n.value.top_keywords,style:{width:"100%"}},{default:l(()=>[t(p,{prop:"rank",label:"排名",width:"80"}),t(p,{prop:"keyword",label:"关键词",width:"150"}),t(p,{prop:"count",label:"出现次数",width:"100"}),t(p,{prop:"density",label:"密度",width:"100"},{default:l(o=>[t(G,{percentage:o.row.density,"stroke-width":8,"show-text":!1},null,8,["percentage"]),r("span",Lt,w(o.row.density)+"%",1)]),_:1}),t(p,{prop:"category",label:"词性",width:"80"},{default:l(o=>[t(h,{size:"small",type:j(o.row.category)},{default:l(()=>[s(w(o.row.category),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),n.value.chart_data?(i(),f("div",Rt,[r("h4",null,[t(u,null,{default:l(()=>[t(_(bt))]),_:1}),e[19]||(e[19]=s(" 密度分布图",-1))]),r("div",{ref_key:"chartContainer",ref:C,style:{height:"300px"}},null,512)])):g("",!0),n.value.suggestions?(i(),f("div",Bt,[r("h4",null,[t(u,null,{default:l(()=>[t(M)]),_:1}),e[20]||(e[20]=s(" 优化建议",-1))]),(i(!0),f(N,null,z(n.value.suggestions,(o,x)=>(i(),c(W,{key:x,title:o.title,description:o.description,type:o.type,style:{"margin-bottom":"10px"},"show-icon":""},null,8,["title","description","type"]))),128))])):g("",!0)]),_:1})):(i(),c(v,{key:1,class:"empty-state"},{default:l(()=>[t(X,{description:"请输入内容并开始分析"},{default:l(()=>[t(R,{type:"primary",onClick:T},{default:l(()=>e[21]||(e[21]=[s("开始分析",-1)])),_:1,__:[21]})]),_:1})]),_:1}))]),_:1})]),_:1})])}}},oe=H(It,[["__scopeId","data-v-2da42cf5"]]);export{oe as default};
