import{_ as X}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                  *//* empty css                         *//* empty css                     */import"./el-tooltip-4ed993c7.js";import{aB as Y,aC as Z,r as ee,c as f,al as h,y as a,z as u,Q as t,I as e,A as d,H as l,u as A,L as te,P as v,a4 as E,K as I,O as i,M as p}from"./vendor-ad470fc0.js";import{u as ne}from"./index-bb3d8812.js";import{a as ae,c as oe,d as se,e as le,f as ce,g as re,h as _e,j as ue,k as de,l as ie,m as pe,n as me,o as fe,p as he,q as ve,r as xe,t as D}from"./elementPlus-ef333120.js";const be={class:"layout-container"},Ee={class:"logo"},ge={key:2},we={class:"header-left"},ye={class:"header-right"},ke={class:"user-dropdown"},Be={class:"username"},Ce={__name:"index",setup(Me){const g=Y(),x=Z(),w=ne(),_=ee(!1),b=f(()=>w.user),S=f(()=>g.path),F=f(()=>x.getRoutes().filter(r=>{var o,s;return((o=r.meta)==null?void 0:o.title)&&((s=r.meta)==null?void 0:s.icon)&&r.path!=="/login"&&r.path!=="/profile"})),N=f(()=>g.matched.filter(o=>{var s;return(s=o.meta)==null?void 0:s.title}).map(o=>({title:o.meta.title,path:o.path}))),R=()=>{_.value=!_.value},V=r=>{switch(r){case"profile":x.push("/profile");break;case"logout":ae.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{w.logout().then(()=>{x.push("/login")})});break}};return(r,o)=>{const s=oe,y=se,H=le,T=ce,q=re,z=h("Fold"),L=h("Expand"),O=_e,P=ue,$=de,j=ie,K=h("ArrowDown"),k=pe,Q=me,U=fe,G=he,J=h("router-view"),W=ve,B=xe;return a(),u("div",be,[t(B,null,{default:e(()=>[t(q,{width:_.value?"64px":"200px",class:"sidebar"},{default:e(()=>[d("div",Ee,[_.value?(a(),l(s,{key:1,class:"logo-icon-collapsed"},{default:e(()=>[t(A(D))]),_:1})):(a(),l(s,{key:0,class:"logo-icon"},{default:e(()=>[t(A(D))]),_:1})),_.value?te("",!0):(a(),u("span",ge,"SEO Platform"))]),t(T,{"default-active":S.value,collapse:_.value,"unique-opened":!0,router:"",class:"sidebar-menu"},{default:e(()=>[(a(!0),u(v,null,E(F.value,n=>(a(),u(v,{key:n.path},[n.children&&n.children.length>1?(a(),l(H,{key:0,index:n.path},{title:e(()=>[t(s,null,{default:e(()=>[(a(),l(I(n.meta.icon)))]),_:2},1024),d("span",null,i(n.meta.title),1)]),default:e(()=>[(a(!0),u(v,null,E(n.children.filter(c=>{var m;return!((m=c.meta)!=null&&m.hidden)}),c=>(a(),l(y,{key:c.path,index:c.path===""?n.path:`${n.path}/${c.path}`},{default:e(()=>[p(i(c.meta.title),1)]),_:2},1032,["index"]))),128))]),_:2},1032,["index"])):(a(),l(y,{key:1,index:n.path},{title:e(()=>[p(i(n.meta.title),1)]),default:e(()=>[t(s,null,{default:e(()=>[(a(),l(I(n.meta.icon)))]),_:2},1024)]),_:2},1032,["index"]))],64))),128))]),_:1},8,["default-active","collapse"])]),_:1},8,["width"]),t(B,null,{default:e(()=>[t(G,{class:"header"},{default:e(()=>[d("div",we,[t(O,{type:"text",onClick:R,class:"collapse-btn"},{default:e(()=>[t(s,null,{default:e(()=>[_.value?(a(),l(L,{key:1})):(a(),l(z,{key:0}))]),_:1})]),_:1}),t($,{separator:"/"},{default:e(()=>[(a(!0),u(v,null,E(N.value,n=>(a(),l(P,{key:n.path,to:n.path},{default:e(()=>[p(i(n.title),1)]),_:2},1032,["to"]))),128))]),_:1})]),d("div",ye,[t(U,{onCommand:V},{dropdown:e(()=>[t(Q,null,{default:e(()=>[t(k,{command:"profile"},{default:e(()=>o[0]||(o[0]=[p("个人资料",-1)])),_:1,__:[0]}),t(k,{divided:"",command:"logout"},{default:e(()=>o[1]||(o[1]=[p("退出登录",-1)])),_:1,__:[1]})]),_:1})]),default:e(()=>{var n,c;return[d("span",ke,[t(j,{size:32,src:(n=b.value)==null?void 0:n.avatar},{default:e(()=>{var m,C,M;return[p(i((M=(C=(m=b.value)==null?void 0:m.username)==null?void 0:C.charAt(0))==null?void 0:M.toUpperCase()),1)]}),_:1},8,["src"]),d("span",Be,i((c=b.value)==null?void 0:c.username),1),t(s,null,{default:e(()=>[t(K)]),_:1})])]}),_:1})])]),_:1}),t(W,{class:"main-content"},{default:e(()=>[t(J)]),_:1})]),_:1})]),_:1})])}}},He=X(Ce,[["__scopeId","data-v-e2be9ff4"]]);export{He as default};
