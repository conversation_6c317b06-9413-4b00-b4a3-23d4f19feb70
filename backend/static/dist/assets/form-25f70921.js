import{_ as S}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                *//* empty css                     *//* empty css               *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                  */import{aB as N,aC as O,r as w,c as $,X as F,k as M,y as b,z as U,A as o,Q as e,I as t,u as n,O as h,M as y,H as q,L as A}from"./vendor-ad470fc0.js";import{k as v}from"./keyword-dca4d9f3.js";import{E as H,c as L,h as P,s as Q,u as X,y as j,G as D,H as G,z as K,v as T,x as J,a2 as W,X as Y,a3 as Z,L as ee,t as te,a4 as ae,a5 as oe,P as le,Q as se}from"./elementPlus-ef333120.js";import"./index-bb3d8812.js";const re={class:"keyword-form-page"},ne={class:"page-header"},de={class:"header-content"},ie={class:"header-left"},ue={class:"header-text"},_e={class:"header-actions"},ce={class:"page-content"},pe={class:"form-container"},me={class:"card-header"},fe={class:"option-item"},ye={class:"option-item"},ve={class:"option-item"},ge={class:"form-actions"},ke={__name:"form",setup(we){const _=N(),E=O(),c=w(),u=w(!1),i=$(()=>!!_.params.id),s=F({display_name:"",keyword_type:"",description:""}),V={display_name:[{required:!0,message:"请输入显示名称",trigger:"blur"}],keyword_type:[{required:!0,message:"请选择关键词类型",trigger:"change"}]},x=async()=>{if(c.value)try{await c.value.validate(),u.value=!0;let l;i.value?l=await v.update(_.params.id,s):l=await v.create(s),l.code===200&&(H.success(i.value?"更新成功":"创建成功"),E.push("/keyword"))}catch(l){console.error("Submit failed:",l)}finally{u.value=!1}},C=async()=>{if(i.value)try{const l=await v.get(_.params.id);l.code===200&&Object.assign(s,l.data)}catch(l){console.error("Failed to fetch keyword:",l)}};return M(()=>{C()}),(l,a)=>{const r=L,p=P,g=Q,m=X,k=j,f=D,z=G,B=K,I=T,R=J;return b(),U("div",re,[o("div",ne,[o("div",de,[o("div",ie,[e(r,{class:"header-icon"},{default:t(()=>[e(n(W))]),_:1}),o("div",ue,[o("h1",null,h(i.value?"编辑关键词库":"创建关键词库"),1),a[5]||(a[5]=o("p",null,"管理和配置关键词库，优化SEO效果",-1))])]),o("div",_e,[e(p,{onClick:a[0]||(a[0]=d=>l.$router.back()),class:"back-btn"},{default:t(()=>[e(r,null,{default:t(()=>[e(n(Y))]),_:1}),a[6]||(a[6]=y(" 返回列表 ",-1))]),_:1,__:[6]})])])]),o("div",ce,[o("div",pe,[e(R,{class:"form-card",shadow:"hover"},{header:t(()=>[o("div",me,[e(r,{class:"card-icon"},{default:t(()=>[e(n(Z))]),_:1}),a[7]||(a[7]=o("span",{class:"card-title"},"基础信息",-1))])]),default:t(()=>[e(I,{ref_key:"formRef",ref:c,model:s,rules:V,"label-width":"120px",class:"keyword-form"},{default:t(()=>[e(B,{gutter:24},{default:t(()=>[e(k,{span:12},{default:t(()=>[e(m,{label:"显示名称",prop:"display_name"},{default:t(()=>[e(g,{modelValue:s.display_name,"onUpdate:modelValue":a[1]||(a[1]=d=>s.display_name=d),placeholder:"请输入显示名称",size:"large",clearable:""},{prefix:t(()=>[e(r,null,{default:t(()=>[e(n(ee))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(k,{span:12},{default:t(()=>[e(m,{label:"关键词类型",prop:"keyword_type"},{default:t(()=>[e(z,{modelValue:s.keyword_type,"onUpdate:modelValue":a[2]||(a[2]=d=>s.keyword_type=d),placeholder:"请选择关键词类型",size:"large",clearable:""},{default:t(()=>[e(f,{label:"长尾词",value:"long_tail"},{default:t(()=>[o("div",fe,[e(r,null,{default:t(()=>[e(n(te))]),_:1}),a[8]||(a[8]=o("span",null,"长尾词",-1))])]),_:1}),e(f,{label:"核心词",value:"core"},{default:t(()=>[o("div",ye,[e(r,null,{default:t(()=>[e(n(ae))]),_:1}),a[9]||(a[9]=o("span",null,"核心词",-1))])]),_:1}),e(f,{label:"品牌词",value:"brand"},{default:t(()=>[o("div",ve,[e(r,null,{default:t(()=>[e(n(oe))]),_:1}),a[10]||(a[10]=o("span",null,"品牌词",-1))])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{label:"描述",prop:"description"},{default:t(()=>[e(g,{modelValue:s.description,"onUpdate:modelValue":a[3]||(a[3]=d=>s.description=d),type:"textarea",rows:4,placeholder:"请输入关键词库描述，详细说明该关键词库的用途和特点",maxlength:"500","show-word-limit":"",resize:"none"},null,8,["modelValue"])]),_:1}),o("div",ge,[e(p,{type:"primary",onClick:x,loading:u.value,size:"large",class:"submit-btn"},{default:t(()=>[u.value?A("",!0):(b(),q(r,{key:0},{default:t(()=>[e(n(le))]),_:1})),y(" "+h(i.value?"更新关键词库":"创建关键词库"),1)]),_:1},8,["loading"]),e(p,{onClick:a[4]||(a[4]=d=>l.$router.back()),size:"large",class:"cancel-btn"},{default:t(()=>[e(r,null,{default:t(()=>[e(n(se))]),_:1}),a[11]||(a[11]=y(" 取消 ",-1))]),_:1,__:[11]})])]),_:1},8,["model"])]),_:1})])])])}}},$e=S(ke,[["__scopeId","data-v-d3f340d6"]]);export{$e as default};
