import{_ as E}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                  */import{x as k,E as r,c as v,h as x,B,A as T,C as h,O as $}from"./elementPlus-05e8f3ef.js";import{r as w,k as z,y as I,z as M,A as l,Q as e,I as o,u as N,M as i,O as V}from"./vendor-ad470fc0.js";const A={class:"permissions-container"},D={class:"card-header"},O={__name:"permissions",setup(j){const d=w([{id:1,name:"用户管理",code:"user:manage",type:"模块",description:"用户增删改查权限"},{id:2,name:"项目管理",code:"project:manage",type:"模块",description:"项目管理权限"},{id:3,name:"蜘蛛管理",code:"spider:manage",type:"模块",description:"蜘蛛数据管理权限"},{id:4,name:"系统设置",code:"system:setting",type:"功能",description:"系统配置权限"},{id:5,name:"权限控制",code:"permission:control",type:"功能",description:"权限分配管理"}]),c=n=>({模块:"primary",功能:"success",操作:"warning"})[n]||"info",_=()=>{r.info("创建权限功能开发中...")},m=n=>{r.info(`编辑权限: ${n.name}`)},u=n=>{r.info(`删除权限: ${n.name}`)};return z(()=>{}),(n,t)=>{const f=v,p=x,s=B,y=T,g=h,C=k;return I(),M("div",A,[t[4]||(t[4]=l("div",{class:"page-header"},[l("h2",null,"权限管理"),l("p",null,"管理系统功能权限")],-1)),e(C,{class:"content-card"},{header:o(()=>[l("div",D,[t[1]||(t[1]=l("span",null,"权限列表",-1)),e(p,{type:"primary",onClick:_},{default:o(()=>[e(f,null,{default:o(()=>[e(N($))]),_:1}),t[0]||(t[0]=i(" 新增权限 ",-1))]),_:1,__:[0]})])]),default:o(()=>[e(g,{data:d.value,style:{width:"100%"},"row-key":"id","default-expand-all":""},{default:o(()=>[e(s,{prop:"name",label:"权限名称"}),e(s,{prop:"code",label:"权限代码"}),e(s,{prop:"type",label:"类型"},{default:o(({row:a})=>[e(y,{type:c(a.type)},{default:o(()=>[i(V(a.type),1)]),_:2},1032,["type"])]),_:1}),e(s,{prop:"description",label:"描述"}),e(s,{label:"操作",width:"200"},{default:o(({row:a})=>[e(p,{size:"small",onClick:b=>m(a)},{default:o(()=>t[2]||(t[2]=[i("编辑",-1)])),_:2,__:[2]},1032,["onClick"]),e(p,{size:"small",type:"danger",onClick:b=>u(a)},{default:o(()=>t[3]||(t[3]=[i("删除",-1)])),_:2,__:[3]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},R=E(O,[["__scopeId","data-v-6aaf6978"]]);export{R as default};
