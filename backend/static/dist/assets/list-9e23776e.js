import{_ as P}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                *//* empty css                  */import{aC as R,r as C,X as x,c as U,k as j,y as z,z as J,A as s,Q as e,I as a,u as c,O as u,M as r,a5 as O,J as Q,H as X}from"./vendor-ad470fc0.js";import{k as v}from"./keyword-dca4d9f3.js";import{w as q,E as h,a as G,c as W,h as Z,s as tt,x as et,B as at,A as st,C as ot,J as lt,K as nt,L as rt,M as E,N as it,R as S,S as dt}from"./elementPlus-ef333120.js";import"./index-bb3d8812.js";const pt={class:"page-container"},ct={class:"page-header"},_t={class:"header-content"},ut={class:"header-left"},mt={class:"header-icon"},ft={class:"header-text"},gt={class:"breadcrumb"},yt={class:"header-stats"},vt={class:"stat-item"},ht={class:"stat-value"},bt={class:"stat-item"},kt={class:"stat-value"},wt={class:"page-content"},Ct={class:"table-toolbar"},xt={class:"toolbar-left"},zt={class:"toolbar-right"},Et={class:"search-group"},St={class:"pagination-container"},Bt={__name:"list",setup($t){const B=R(),f=C(!1),g=C([]),m=x({keyword:""}),n=x({page:1,pageSize:10,total:0}),$=o=>q(o).format("YYYY-MM-DD HH:mm:ss"),_=async()=>{f.value=!0;try{const o={page:n.page,page_size:n.pageSize,keyword:m.keyword},t=await v.getList(o);t.code===200&&(g.value=t.data.list,n.total=t.data.total)}catch(o){console.error("Failed to fetch keywords:",o)}finally{f.value=!1}},b=()=>{n.page=1,_()},D=()=>{m.keyword="",n.page=1,_()},M=o=>{n.pageSize=o,n.page=1,_()},T=o=>{n.page=o,_()},I=o=>{B.push(`/keyword/edit/${o.id}`)},V=o=>{h.info("导入功能暂未开放")},F=async o=>{try{(await v.export(o.id)).code===200&&h.success("导出成功")}catch(t){console.error("Failed to export keywords:",t)}},K=o=>{G.confirm(`确定要删除关键词库 "${o.display_name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{(await v.delete(o.id)).code===200&&(h.success("删除成功"),_())}catch(t){console.error("Failed to delete keyword:",t)}})},N=U(()=>g.value.filter(o=>o.status===1).length);return j(()=>{_()}),(o,t)=>{const d=W,p=Z,Y=tt,k=et,i=at,w=st,A=ot,H=lt,L=nt;return z(),J("div",pt,[s("div",ct,[s("div",_t,[s("div",ut,[s("div",mt,[e(d,null,{default:a(()=>[e(c(rt))]),_:1})]),s("div",ft,[t[6]||(t[6]=s("h2",null,"关键词管理",-1)),s("div",gt,[e(d,null,{default:a(()=>[e(c(E))]),_:1}),t[4]||(t[4]=s("span",null,"SEO工具",-1)),e(d,null,{default:a(()=>[e(c(E))]),_:1}),t[5]||(t[5]=s("span",null,"关键词库管理",-1))])])]),s("div",yt,[s("div",vt,[s("div",ht,u(n.total),1),t[7]||(t[7]=s("div",{class:"stat-label"},"总关键词库",-1))]),s("div",bt,[s("div",kt,u(N.value),1),t[8]||(t[8]=s("div",{class:"stat-label"},"启用中",-1))])])])]),s("div",wt,[e(k,{class:"toolbar-card"},{default:a(()=>[s("div",Ct,[s("div",xt,[e(p,{type:"primary",class:"action-btn",onClick:t[0]||(t[0]=l=>o.$router.push("/keyword/create"))},{default:a(()=>[e(d,null,{default:a(()=>[e(c(it))]),_:1}),t[9]||(t[9]=r(" 新建关键词库 ",-1))]),_:1,__:[9]})]),s("div",zt,[s("div",Et,[e(Y,{modelValue:m.keyword,"onUpdate:modelValue":t[1]||(t[1]=l=>m.keyword=l),placeholder:"搜索关键词库名称",class:"search-input",clearable:"",onKeyup:O(b,["enter"])},{prefix:a(()=>[e(d,null,{default:a(()=>[e(c(S))]),_:1})]),_:1},8,["modelValue"]),e(p,{type:"primary",class:"search-btn",onClick:b},{default:a(()=>[e(d,null,{default:a(()=>[e(c(S))]),_:1}),t[10]||(t[10]=r(" 搜索 ",-1))]),_:1,__:[10]}),e(p,{class:"reset-btn",onClick:D},{default:a(()=>[e(d,null,{default:a(()=>[e(c(dt))]),_:1}),t[11]||(t[11]=r(" 重置 ",-1))]),_:1,__:[11]})])])])]),_:1}),e(k,{class:"table-card"},{default:a(()=>[Q((z(),X(A,{data:g.value,style:{width:"100%"}},{default:a(()=>[e(i,{prop:"display_name",label:"显示名称","min-width":"150"}),e(i,{prop:"table_name",label:"表名","min-width":"150"}),e(i,{prop:"keyword_type",label:"类型",width:"100"},{default:a(({row:l})=>[e(w,null,{default:a(()=>[r(u(l.keyword_type||"默认"),1)]),_:2},1024)]),_:1}),e(i,{prop:"count",label:"关键词数量",width:"120"}),e(i,{prop:"description",label:"描述","show-overflow-tooltip":""}),e(i,{prop:"status",label:"状态",width:"80"},{default:a(({row:l})=>[e(w,{type:l.status===1?"success":"danger"},{default:a(()=>[r(u(l.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"created_at",label:"创建时间",width:"180"},{default:a(({row:l})=>[r(u($(l.created_at)),1)]),_:1}),e(i,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[e(p,{type:"text",size:"small",onClick:y=>I(l)},{default:a(()=>t[12]||(t[12]=[r(" 编辑 ",-1)])),_:2,__:[12]},1032,["onClick"]),e(p,{type:"text",size:"small",onClick:y=>V(l)},{default:a(()=>t[13]||(t[13]=[r(" 导入 ",-1)])),_:2,__:[13]},1032,["onClick"]),e(p,{type:"text",size:"small",onClick:y=>F(l)},{default:a(()=>t[14]||(t[14]=[r(" 导出 ",-1)])),_:2,__:[14]},1032,["onClick"]),e(p,{type:"text",size:"small",onClick:y=>K(l),style:{color:"#f56c6c"}},{default:a(()=>t[15]||(t[15]=[r(" 删除 ",-1)])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[L,f.value]]),s("div",St,[e(H,{"current-page":n.page,"onUpdate:currentPage":t[2]||(t[2]=l=>n.page=l),"page-size":n.pageSize,"onUpdate:pageSize":t[3]||(t[3]=l=>n.pageSize=l),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:M,onCurrentChange:T},null,8,["current-page","page-size","total"])])]),_:1})])])}}},Jt=P(Bt,[["__scopeId","data-v-120b20b8"]]);export{Jt as default};
