import{_ as o}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                */import{x as _}from"./elementPlus-ef333120.js";import{y as n,z as l,Q as r,I as a,A as t}from"./vendor-ad470fc0.js";const c={class:"template-manage"},p={__name:"template-manage",setup(d){return(m,e)=>{const s=_;return n(),l("div",c,[r(s,null,{header:a(()=>e[0]||(e[0]=[t("div",{class:"card-header"},[t("span",null,"模板管理")],-1)])),default:a(()=>[e[1]||(e[1]=t("div",{class:"content"},[t("p",null,"模板管理功能正在开发中...")],-1))]),_:1,__:[1]})])}}},x=o(p,[["__scopeId","data-v-c693901c"]]);export{x as default};
