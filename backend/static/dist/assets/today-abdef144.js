import{_ as st}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css               *//* empty css                *//* empty css                  */import{s as E}from"./spider-fa0c41a6.js";import{w as et,E as M,c as at,h as ot,x as lt,y as nt,z as dt,s as it,A as rt,B as ct,C as _t,L as pt,M as ut,a3 as ft,N as mt,T as V,a4 as vt,a5 as gt,S as k,a6 as ht}from"./elementPlus-05e8f3ef.js";import{r as b,X as A,c as yt,k as bt,y as T,z as N,A as t,Q as e,I as a,u as r,O as n,M as _,C as Ct,P as wt,a4 as St,J as xt,H as zt,al as Et}from"./vendor-ad470fc0.js";import"./index-24913185.js";const kt={class:"page-container"},Tt={class:"page-header"},Dt={class:"header-content"},Ft={class:"header-left"},It={class:"header-icon"},Pt={class:"breadcrumb"},Bt={class:"header-stats"},Mt={class:"stat-item"},Vt={class:"stat-value"},At={class:"stat-item"},Nt={class:"stat-value"},Ut={class:"stat-item"},Yt={class:"stat-value"},Lt={class:"page-content"},Ht={class:"toolbar-content"},Ot={class:"toolbar-left"},jt={class:"stats-content"},Gt={class:"stats-icon"},Rt={class:"stats-info"},Jt={class:"stats-number"},Qt={class:"stats-content"},Xt={class:"stats-icon"},$t={class:"stats-info"},qt={class:"stats-number"},Kt={class:"stats-percent"},Wt={class:"stats-content"},Zt={class:"stats-icon"},ts={class:"stats-info"},ss={class:"stats-number"},es={class:"stats-percent"},as={class:"stats-content"},os={class:"stats-icon"},ls={class:"stats-info"},ns={class:"stats-number"},ds={class:"stats-percent"},is={class:"filter-toolbar"},rs={class:"filter-buttons"},cs={class:"filter-actions"},_s={class:"time-text"},ps={class:"pagination-container"},us={__name:"today",setup(fs){const v=b(!1),D=b([]),p=b({}),S=b([]),x=b(""),u=A({domain:"",spider_type:""}),i=A({page:1,pageSize:20,total:0}),U=[{type:"",name:"全部蜘蛛"},{type:1,name:"百度蜘蛛"},{type:2,name:"搜狗蜘蛛"},{type:3,name:"360蜘蛛"},{type:4,name:"必应蜘蛛"},{type:5,name:"谷歌蜘蛛"},{type:6,name:"神马蜘蛛"},{type:7,name:"Yandex蜘蛛"},{type:8,name:"Coccoc蜘蛛"},{type:9,name:"Naver蜘蛛"}],g=yt(()=>S.value.reduce((o,s)=>o+s.count,0)),f=o=>{const s=S.value.find(d=>d.name===o);return s?s.count:0},F=o=>{const s=f(o);return g.value>0?(s/g.value*100).toFixed(1):0},I=()=>{const o=f("百度"),s=f("谷歌");return g.value-o-s},Y=()=>{const o=I();return g.value>0?(o/g.value*100).toFixed(1):0},L=o=>({1:"primary",2:"success",3:"warning",4:"info",5:"danger",6:"",7:"success",8:"warning",9:"info"})[o]||"info",H=o=>{if(!o)return"";const s=o.match(/\d+\.\d+\.\d+\.\d+/);return s?s[0]:o},O=o=>et(o).format("YYYY-MM-DD HH:mm:ss"),j=o=>{x.value=o,u.spider_type=o,C()},C=()=>{i.page=1,h()},G=()=>{u.domain="",u.spider_type="",x.value="",C()},R=o=>{i.pageSize=o,h()},J=o=>{i.page=o,h()},Q=()=>{P(),B(),h()},X=()=>{M.info("导出功能开发中...")},P=async()=>{try{const o=await E.getTodayStats();o.code===200&&(p.value=o.data)}catch(o){console.error("Failed to fetch today stats:",o)}},B=async()=>{try{const o=await E.getStats();o.code===200&&(S.value=o.data)}catch(o){console.error("Failed to fetch spider stats:",o)}},h=async()=>{v.value=!0;try{const o={page:i.page,page_size:i.pageSize,spider_type:u.spider_type,domain:u.domain,date_type:"today"},s=await E.getLogs(o);s.code===200&&(D.value=s.data.list,i.total=s.data.total)}catch(o){console.error("Failed to fetch spider logs:",o),M.error("获取数据失败")}finally{v.value=!1}};return bt(()=>{P(),B(),h()}),(o,s)=>{const d=at,y=ot,c=lt,w=nt,$=Et("Globe"),q=dt,K=it,z=rt,m=ct,W=_t,Z=pt,tt=ut;return T(),N("div",kt,[t("div",Tt,[t("div",Dt,[t("div",Ft,[t("div",It,[e(d,null,{default:a(()=>[e(r(ft))]),_:1})]),t("div",null,[s[5]||(s[5]=t("h1",{class:"page-title"},"今日蜘蛛统计",-1)),t("div",Pt,[s[3]||(s[3]=t("span",null,"蜘蛛管理",-1)),e(d,null,{default:a(()=>[e(r(mt))]),_:1}),s[4]||(s[4]=t("span",{class:"current"},"今日统计",-1))])])]),t("div",Bt,[t("div",Mt,[t("div",Vt,n(p.value.total_count||0),1),s[6]||(s[6]=t("div",{class:"stat-label"},"今日访问",-1))]),t("div",At,[t("div",Nt,n(f("百度")),1),s[7]||(s[7]=t("div",{class:"stat-label"},"百度蜘蛛",-1))]),t("div",Ut,[t("div",Yt,n(f("谷歌")),1),s[8]||(s[8]=t("div",{class:"stat-label"},"谷歌蜘蛛",-1))])])])]),t("div",Lt,[e(c,{class:"toolbar-card"},{default:a(()=>[t("div",Ht,[t("div",Ot,[e(y,{onClick:Q,loading:v.value,class:"action-btn"},{default:a(()=>[e(d,null,{default:a(()=>[e(r(V))]),_:1}),s[9]||(s[9]=_(" 刷新数据 ",-1))]),_:1,__:[9]},8,["loading"]),e(y,{onClick:X,class:"action-btn"},{default:a(()=>[e(d,null,{default:a(()=>[e(r(vt))]),_:1}),s[10]||(s[10]=_(" 导出数据 ",-1))]),_:1,__:[10]})])])]),_:1}),e(q,{gutter:20,class:"stats-cards"},{default:a(()=>[e(w,{span:6},{default:a(()=>[e(c,{class:"stats-card total"},{default:a(()=>{var l;return[t("div",jt,[t("div",Gt,[e(d,null,{default:a(()=>[e(r(gt))]),_:1})]),t("div",Rt,[t("div",Jt,n(p.value.total_count||0),1),s[11]||(s[11]=t("div",{class:"stats-label"},"总访问量",-1)),t("div",{class:Ct(["stats-growth",{positive:p.value.growth_rate>0,negative:p.value.growth_rate<0}])},n(p.value.growth_rate>0?"+":"")+n(((l=p.value.growth_rate)==null?void 0:l.toFixed(1))||0)+"% ",3)])])]}),_:1})]),_:1}),e(w,{span:6},{default:a(()=>[e(c,{class:"stats-card baidu"},{default:a(()=>[t("div",Qt,[t("div",Xt,[e(d,null,{default:a(()=>[e(r(k))]),_:1})]),t("div",$t,[t("div",qt,n(f("百度")),1),s[12]||(s[12]=t("div",{class:"stats-label"},"百度蜘蛛",-1)),t("div",Kt,n(F("百度"))+"%",1)])])]),_:1})]),_:1}),e(w,{span:6},{default:a(()=>[e(c,{class:"stats-card google"},{default:a(()=>[t("div",Wt,[t("div",Zt,[e(d,null,{default:a(()=>[e($)]),_:1})]),t("div",ts,[t("div",ss,n(f("谷歌")),1),s[13]||(s[13]=t("div",{class:"stats-label"},"谷歌蜘蛛",-1)),t("div",es,n(F("谷歌"))+"%",1)])])]),_:1})]),_:1}),e(w,{span:6},{default:a(()=>[e(c,{class:"stats-card others"},{default:a(()=>[t("div",as,[t("div",os,[e(d,null,{default:a(()=>[e(r(ht))]),_:1})]),t("div",ls,[t("div",ns,n(I()),1),s[14]||(s[14]=t("div",{class:"stats-label"},"其他蜘蛛",-1)),t("div",ds,n(Y())+"%",1)])])]),_:1})]),_:1})]),_:1}),e(c,{class:"filter-card"},{default:a(()=>[t("div",is,[t("div",rs,[(T(),N(wt,null,St(U,l=>e(y,{key:l.type,type:x.value===l.type?"primary":"",onClick:ms=>j(l.type),class:"spider-filter-btn"},{default:a(()=>[_(n(l.name),1)]),_:2},1032,["type","onClick"])),64))]),t("div",cs,[e(K,{modelValue:u.domain,"onUpdate:modelValue":s[0]||(s[0]=l=>u.domain=l),placeholder:"搜索域名",style:{width:"200px","margin-right":"10px"},clearable:"",onChange:C},{prefix:a(()=>[e(d,null,{default:a(()=>[e(r(k))]),_:1})]),_:1},8,["modelValue"]),e(y,{onClick:C,class:"search-btn"},{default:a(()=>[e(d,null,{default:a(()=>[e(r(k))]),_:1}),s[15]||(s[15]=_(" 搜索 ",-1))]),_:1,__:[15]}),e(y,{onClick:G,class:"reset-btn"},{default:a(()=>[e(d,null,{default:a(()=>[e(r(V))]),_:1}),s[16]||(s[16]=_(" 重置 ",-1))]),_:1,__:[16]})])])]),_:1}),e(c,{class:"table-card"},{header:a(()=>s[17]||(s[17]=[t("div",{class:"table-header"},[t("span",null,"今日蜘蛛爬行统计")],-1)])),default:a(()=>[xt((T(),zt(W,{data:D.value,style:{width:"100%"},"default-sort":{prop:"spider_times",order:"descending"}},{default:a(()=>[e(m,{prop:"spider_name",label:"蜘蛛名称",width:"120"},{default:a(({row:l})=>[e(z,{type:L(l.spider_type)},{default:a(()=>[_(n(l.spider_name),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"user_agent",label:"User-Agent","show-overflow-tooltip":""}),e(m,{prop:"spider_uri",label:"爬行URI","show-overflow-tooltip":""}),e(m,{prop:"spider_type",label:"爬行类型",width:"100"},{default:a(({row:l})=>[e(z,{size:"small",type:l.spider_type===1?"success":"info"},{default:a(()=>[_(n(l.spider_type===1?"首次":"重复"),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"ip_source",label:"来源IP",width:"140"},{default:a(({row:l})=>[t("code",null,n(H(l.ip_source)),1)]),_:1}),e(m,{prop:"spider_first",label:"首次访问",width:"100"},{default:a(({row:l})=>[e(z,{type:l.spider_first?"danger":"success",size:"small"},{default:a(()=>[_(n(l.spider_first?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"spider_times",label:"爬行时间",width:"180",sortable:""},{default:a(({row:l})=>[t("span",_s,n(O(l.spider_times)),1)]),_:1})]),_:1},8,["data"])),[[tt,v.value]]),e(c,{class:"pagination-card"},{default:a(()=>[t("div",ps,[e(Z,{"current-page":i.page,"onUpdate:currentPage":s[1]||(s[1]=l=>i.page=l),"page-size":i.pageSize,"onUpdate:pageSize":s[2]||(s[2]=l=>i.pageSize=l),"page-sizes":[10,20,50,100],small:!1,disabled:v.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:i.total,onSizeChange:R,onCurrentChange:J},null,8,["current-page","page-size","disabled","total"])])]),_:1})]),_:1})])])}}},Bs=st(us,[["__scopeId","data-v-b55debb9"]]);export{Bs as default};
