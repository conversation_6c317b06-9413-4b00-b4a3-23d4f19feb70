import{_ as dt}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css               *//* empty css                *//* empty css                  */import{u as ct,i as it,b as ut,g as _t,a as pt,c as yt,d as ft,e as vt,f as mt,H as T}from"./charts-d5c9e496.js";import{s as A}from"./spider-fa0c41a6.js";import{w as L,E as N,c as ht,h as gt,x as bt,y as Ct,z as xt,A as wt,B as Dt,C as kt,M as Tt}from"./elementPlus-05e8f3ef.js";import{r as f,c as E,k as At,y as u,z as Et,A as o,Q as t,I as a,M as v,O as l,u as C,C as S,H as m,J as St,al as _}from"./vendor-ad470fc0.js";import"./index-24913185.js";const Yt={class:"page-container"},Mt={class:"page-header"},$t={class:"header-actions"},Bt={class:"page-content"},Ft={class:"stats-content"},zt={class:"stats-icon"},Ot={class:"stats-info"},Pt={class:"stats-number"},Rt={class:"stats-date"},Ht={class:"stats-content"},It={class:"stats-icon"},Lt={class:"stats-info"},Nt={class:"stats-number"},Vt={class:"stats-date"},Ut={class:"stats-content"},Wt={class:"stats-icon"},jt={class:"stats-info"},qt={class:"stats-trend"},Jt={class:"chart-container"},Qt={class:"chart-container"},Gt={class:"table-header"},Kt={class:"table-actions"},Xt={class:"count-number"},Zt={class:"count-number"},te={class:"trend-indicator"},ee={class:"chart-container"},ae={__name:"yesterday",setup(se){ct([it,ut,_t,pt,yt,ft,vt,mt]);const x=f(!1),h=f({}),d=f([]),V=f([]),U=L(),Y=L().subtract(1,"day"),c=E(()=>{const e=h.value.today_count||0,s=h.value.yesterday_count||0;return s===0?0:(e-s)/s*100}),M=E(()=>d.value.reduce((e,s)=>e+(s.yesterday_count||0),0)),$=E(()=>d.value.reduce((e,s)=>e+(s.today_count||0),0)),B=f({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"昨日访问",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),w=f({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["昨日","今日"]},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"昨日",type:"bar",data:[],itemStyle:{color:"#91cc75"}},{name:"今日",type:"bar",data:[],itemStyle:{color:"#5470c6"}}]}),D=f({tooltip:{trigger:"axis"},xAxis:{type:"category",data:Array.from({length:24},(e,s)=>`${s}:00`)},yAxis:{type:"value"},series:[{name:"访问量",type:"line",data:[],smooth:!0,areaStyle:{opacity:.3}}]}),F=e=>e.format("YYYY-MM-DD"),W=()=>c.value>0?"上升":c.value<0?"下降":"持平",j=e=>({1:"primary",2:"success",3:"warning",4:"info",5:"danger",6:"",7:"success",8:"warning",9:"info"})[e]||"info",g=e=>(e.today_count||0)-(e.yesterday_count||0),p=e=>{const s=e.yesterday_count||0;return s===0?0:((e.today_count||0)-s)/s*100},q=e=>M.value>0?((e.yesterday_count||0)/M.value*100).toFixed(1):0,J=e=>$.value>0?((e.today_count||0)/$.value*100).toFixed(1):0,z=()=>{K(),X(),Z()},Q=()=>{N.info("跳转到今日统计页面")},G=()=>{N.info("导出对比数据功能开发中...")},K=async()=>{try{const e=await A.getTodayStats();e.code===200&&(h.value=e.data)}catch(e){console.error("Failed to fetch yesterday stats:",e)}},X=async()=>{x.value=!0;try{const e=await A.getComparisonStats();e.code===200&&(d.value=e.data,tt())}catch(e){console.error("Failed to fetch comparison data:",e),d.value=[]}finally{x.value=!1}},Z=async()=>{try{const e=await A.getHourlyStats({date:Y.format("YYYY-MM-DD")});e.code===200&&(V.value=e.data,D.value.series[0].data=e.data)}catch(e){console.error("Failed to fetch hourly data:",e),D.value.series[0].data=[]}},tt=()=>{B.value.series[0].data=d.value.map(e=>({name:e.spider_name,value:e.yesterday_count||0})),w.value.xAxis.data=d.value.map(e=>e.spider_name),w.value.series[0].data=d.value.map(e=>e.yesterday_count||0),w.value.series[1].data=d.value.map(e=>e.today_count||0)};return At(()=>{z()}),(e,s)=>{const et=_("Refresh"),r=ht,k=gt,O=_("TrendCharts"),at=_("Calendar"),y=bt,b=Ct,st=_("Clock"),P=_("ArrowUp"),R=_("ArrowDown"),H=_("Minus"),I=xt,ot=_("Download"),nt=wt,i=Dt,lt=kt,rt=Tt;return u(),Et("div",Yt,[o("div",Mt,[s[2]||(s[2]=o("h2",null,"昨日蜘蛛统计",-1)),o("div",$t,[t(k,{onClick:z,loading:x.value},{default:a(()=>[t(r,null,{default:a(()=>[t(et)]),_:1}),s[0]||(s[0]=v(" 刷新数据 ",-1))]),_:1,__:[0]},8,["loading"]),t(k,{onClick:Q},{default:a(()=>[t(r,null,{default:a(()=>[t(O)]),_:1}),s[1]||(s[1]=v(" 对比今日 ",-1))]),_:1,__:[1]})])]),o("div",Bt,[t(I,{gutter:20,class:"stats-cards"},{default:a(()=>[t(b,{span:8},{default:a(()=>[t(y,{class:"stats-card yesterday"},{default:a(()=>[o("div",Ft,[o("div",zt,[t(r,null,{default:a(()=>[t(at)]),_:1})]),o("div",Ot,[o("div",Pt,l(h.value.yesterday_count||0),1),s[3]||(s[3]=o("div",{class:"stats-label"},"昨日总访问",-1)),o("div",Rt,l(F(C(Y))),1)])])]),_:1})]),_:1}),t(b,{span:8},{default:a(()=>[t(y,{class:"stats-card today"},{default:a(()=>[o("div",Ht,[o("div",It,[t(r,null,{default:a(()=>[t(st)]),_:1})]),o("div",Lt,[o("div",Nt,l(h.value.today_count||0),1),s[4]||(s[4]=o("div",{class:"stats-label"},"今日总访问",-1)),o("div",Vt,l(F(C(U))),1)])])]),_:1})]),_:1}),t(b,{span:8},{default:a(()=>[t(y,{class:"stats-card comparison"},{default:a(()=>[o("div",Ut,[o("div",Wt,[t(r,null,{default:a(()=>[t(O)]),_:1})]),o("div",jt,[o("div",{class:S(["stats-number",{positive:c.value>0,negative:c.value<0}])},l(c.value>0?"+":"")+l(c.value.toFixed(1))+"% ",3),s[5]||(s[5]=o("div",{class:"stats-label"},"增长率",-1)),o("div",qt,[c.value>0?(u(),m(r,{key:0,class:"trend-up"},{default:a(()=>[t(P)]),_:1})):c.value<0?(u(),m(r,{key:1,class:"trend-down"},{default:a(()=>[t(R)]),_:1})):(u(),m(r,{key:2,class:"trend-equal"},{default:a(()=>[t(H)]),_:1})),v(" "+l(W()),1)])])])]),_:1})]),_:1})]),_:1}),t(I,{gutter:20,class:"charts-row"},{default:a(()=>[t(b,{span:12},{default:a(()=>[t(y,null,{header:a(()=>s[6]||(s[6]=[o("span",null,"昨日蜘蛛类型分布",-1)])),default:a(()=>[o("div",Jt,[t(C(T),{option:B.value,style:{height:"300px"}},null,8,["option"])])]),_:1})]),_:1}),t(b,{span:12},{default:a(()=>[t(y,null,{header:a(()=>s[7]||(s[7]=[o("span",null,"今日vs昨日对比",-1)])),default:a(()=>[o("div",Qt,[t(C(T),{option:w.value,style:{height:"300px"}},null,8,["option"])])]),_:1})]),_:1})]),_:1}),t(y,{class:"table-card"},{header:a(()=>[o("div",Gt,[s[9]||(s[9]=o("span",null,"蜘蛛类型详细对比",-1)),o("div",Kt,[t(k,{size:"small",onClick:G},{default:a(()=>[t(r,null,{default:a(()=>[t(ot)]),_:1}),s[8]||(s[8]=v(" 导出对比数据 ",-1))]),_:1,__:[8]})])])]),default:a(()=>[St((u(),m(lt,{data:d.value,style:{width:"100%"},"default-sort":{prop:"yesterday_count",order:"descending"}},{default:a(()=>[t(i,{prop:"spider_name",label:"蜘蛛类型",width:"120"},{default:a(({row:n})=>[t(nt,{type:j(n.spider_type)},{default:a(()=>[v(l(n.spider_name),1)]),_:2},1032,["type"])]),_:1}),t(i,{prop:"yesterday_count",label:"昨日访问",width:"120",sortable:""},{default:a(({row:n})=>[o("span",Xt,l(n.yesterday_count||0),1)]),_:1}),t(i,{prop:"today_count",label:"今日访问",width:"120",sortable:""},{default:a(({row:n})=>[o("span",Zt,l(n.today_count||0),1)]),_:1}),t(i,{label:"变化量",width:"120",sortable:""},{default:a(({row:n})=>[o("span",{class:S({"change-positive":g(n)>0,"change-negative":g(n)<0,"change-neutral":g(n)===0})},l(g(n)>0?"+":"")+l(g(n)),3)]),_:1}),t(i,{label:"变化率",width:"120",sortable:""},{default:a(({row:n})=>[o("span",{class:S({"change-positive":p(n)>0,"change-negative":p(n)<0,"change-neutral":p(n)===0})},l(p(n)>0?"+":"")+l(p(n).toFixed(1))+"% ",3)]),_:1}),t(i,{label:"昨日占比",width:"100"},{default:a(({row:n})=>[v(l(q(n))+"% ",1)]),_:1}),t(i,{label:"今日占比",width:"100"},{default:a(({row:n})=>[v(l(J(n))+"% ",1)]),_:1}),t(i,{label:"趋势",width:"100"},{default:a(({row:n})=>[o("div",te,[p(n)>5?(u(),m(r,{key:0,class:"trend-up"},{default:a(()=>[t(P)]),_:1})):p(n)<-5?(u(),m(r,{key:1,class:"trend-down"},{default:a(()=>[t(R)]),_:1})):(u(),m(r,{key:2,class:"trend-stable"},{default:a(()=>[t(H)]),_:1}))])]),_:1})]),_:1},8,["data"])),[[rt,x.value]])]),_:1}),t(y,{class:"hourly-card"},{header:a(()=>s[10]||(s[10]=[o("span",null,"昨日小时访问分布",-1)])),default:a(()=>[o("div",ee,[t(C(T),{option:D.value,style:{height:"250px"}},null,8,["option"])])]),_:1})])])}}},ge=dt(ae,[["__scopeId","data-v-66660636"]]);export{ge as default};
