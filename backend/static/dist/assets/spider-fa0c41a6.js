import{s as t}from"./index-24913185.js";const s={getLogs(e){return t({url:"/spider/logs",method:"get",params:e})},getStats(){return t({url:"/spider/stats",method:"get"})},getTodayStats(){return t({url:"/spider/stats/today",method:"get"})},getChartData(e){return t({url:"/spider/stats/chart",method:"get",params:e})},clearLogs(e){return t({url:"/spider/logs/clear",method:"post",data:e})},getComparisonStats(){return t({url:"/spider/stats/comparison",method:"get"})},getHourlyStats(e){return t({url:"/spider/stats/hourly",method:"get",params:e})},getYesterdayStats(){return t({url:"/spider/stats/yesterday",method:"get"})}};export{s};
