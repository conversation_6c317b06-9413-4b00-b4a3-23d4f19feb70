import{_ as U}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                    *//* empty css               *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                 */import{E as h,c as K,s as O,H as B,h as $,A as H,F as W,G,y as D,z as I,D as N,W as R}from"./elementPlus-ef333120.js";import{r,y as f,z as g,A as t,Q as s,I as l,P as F,a4 as X,L as Y,u as Q,C as j,O as i,M as S}from"./vendor-ad470fc0.js";const q={class:"spider-simulator"},J={class:"breadcrumb"},Z={class:"simulator-container"},ee={class:"spider-list"},te=["onClick"],ae={class:"operation-area"},se={class:"input-section"},le={class:"controls"},oe={key:0,class:"result-area"},ne={class:"result-header"},ie={class:"result-status"},re={class:"result-content"},ue={class:"response-info"},ce={class:"info-item"},de={class:"info-value"},pe={class:"info-item"},me={class:"info-value"},_e={class:"info-item"},ve={class:"info-value"},be={class:"info-item"},he={class:"info-value"},fe={class:"headers-content"},ge={class:"page-content-area"},we={__name:"spider-simulator",setup(Me){const d=r(!1),p=r("360pc"),m=r(""),w=r("standard"),M=r("response"),o=r(null),T=[{label:"360PC蜘蛛模拟",value:"360pc"},{label:"360移动蜘蛛模拟",value:"360mobile"},{label:"百度PC蜘蛛模拟",value:"baidupc"},{label:"百度移动蜘蛛模拟",value:"baidumobile"},{label:"搜狗PC蜘蛛模拟",value:"sogoupc"},{label:"搜狗移动蜘蛛模拟",value:"sogoumobile"},{label:"神马移动蜘蛛",value:"shenma"},{label:"正常访问",value:"normal"}],C=a=>{p.value=a},k=async()=>{if(!m.value.trim()){h.warning("请输入要抓取的URL");return}d.value=!0;try{await new Promise(e=>setTimeout(e,2e3));const a=y(p.value);o.value={statusCode:200,statusText:"OK",responseTime:Math.floor(Math.random()*500)+100,contentType:"text/html; charset=utf-8",contentLength:Math.floor(Math.random()*5e4)+1e4,server:"nginx/1.18.0",headers:`HTTP/1.1 200 OK
Server: nginx/1.18.0
Date: ${new Date().toUTCString()}
Content-Type: text/html; charset=utf-8
Content-Length: ${Math.floor(Math.random()*5e4)+1e4}
Connection: keep-alive
Cache-Control: max-age=3600
User-Agent: ${a.userAgent}`,content:`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面 - ${a.name}抓取</title>
    <meta name="description" content="这是一个示例页面，用于演示${a.name}的抓取效果">
    <meta name="keywords" content="SEO, 蜘蛛抓取, ${a.name}">
</head>
<body>
    <h1>欢迎访问示例网站</h1>
    <p>当前页面正在被${a.name}抓取</p>
    <p>抓取时间: ${new Date().toLocaleString()}</p>
    <div class="content">
        <h2>页面内容</h2>
        <p>这里是页面的主要内容...</p>
    </div>
</body>
</html>`},h.success(`${a.name}模拟抓取完成`)}catch{h.error("模拟抓取失败，请检查URL是否正确")}finally{d.value=!1}},y=a=>{const e={"360pc":{name:"360PC蜘蛛",userAgent:"Mozilla/5.0 (compatible; 360Spider; +http://webscan.360.cn)"},"360mobile":{name:"360移动蜘蛛",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A465 Safari/9537.53 (compatible; 360Spider; +http://webscan.360.cn)"},baidupc:{name:"百度PC蜘蛛",userAgent:"Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)"},baidumobile:{name:"百度移动蜘蛛",userAgent:"Mozilla/5.0 (Linux; u; Android 4.2.2; zh-cn; ) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile Safari/10600.6.3 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)"},sogoupc:{name:"搜狗PC蜘蛛",userAgent:"Sogou web spider/4.0(+http://www.sogou.com/docs/help/webmasters.htm#07)"},sogoumobile:{name:"搜狗移动蜘蛛",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9A334 (compatible; Sogou web spider/4.0;+http://www.sogou.com/docs/help/webmasters.htm#07)"},shenma:{name:"神马移动蜘蛛",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 (compatible; YisouSpider; +http://www.yisou.com/help/spider.html)"},normal:{name:"正常访问",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}};return e[a]||e["360pc"]},A=a=>a>=200&&a<300?"success":a>=300&&a<400?"warning":a>=400?"danger":"info",P=a=>{if(!a)return"0 B";const e=1024,_=["B","KB","MB","GB"],u=Math.floor(Math.log(a)/Math.log(e));return parseFloat((a/Math.pow(e,u)).toFixed(2))+" "+_[u]};return(a,e)=>{const _=K,u=O,v=G,x=B,V=$,z=H,c=D,E=I,b=N,L=W;return f(),g("div",q,[t("div",J,[s(_,{class:"breadcrumb-icon"},{default:l(()=>[s(Q(R))]),_:1}),e[4]||(e[4]=t("span",{class:"breadcrumb-text"},"辅助工具",-1)),e[5]||(e[5]=t("span",{class:"breadcrumb-separator"},">",-1)),e[6]||(e[6]=t("span",{class:"breadcrumb-current"},"在线蜘蛛模拟",-1))]),t("div",Z,[t("div",ee,[(f(),g(F,null,X(T,n=>t("div",{key:n.value,class:j(["spider-item",{active:p.value===n.value}]),onClick:Se=>C(n.value)},i(n.label),11,te)),64))]),t("div",ae,[t("div",se,[s(u,{modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=n=>m.value=n),placeholder:"请输入人待抓取的URL...",class:"url-input",size:"large"},null,8,["modelValue"]),t("div",le,[s(x,{modelValue:w.value,"onUpdate:modelValue":e[1]||(e[1]=n=>w.value=n),placeholder:"蜘蛛模式",class:"mode-select",size:"large"},{default:l(()=>[s(v,{label:"标准模式",value:"standard"}),s(v,{label:"快速模式",value:"fast"}),s(v,{label:"详细模式",value:"detailed"})]),_:1},8,["modelValue"]),s(V,{type:"primary",onClick:k,loading:d.value,class:"simulate-btn",size:"large"},{default:l(()=>e[7]||(e[7]=[S(" 模拟抓取 ",-1)])),_:1,__:[7]},8,["loading"])])]),o.value?(f(),g("div",oe,[t("div",ne,[e[8]||(e[8]=t("h3",null,"抓取结果",-1)),t("div",ie,[s(z,{type:A(o.value.statusCode),size:"large"},{default:l(()=>[S(i(o.value.statusCode)+" "+i(o.value.statusText),1)]),_:1},8,["type"])])]),t("div",re,[s(L,{modelValue:M.value,"onUpdate:modelValue":e[3]||(e[3]=n=>M.value=n),class:"result-tabs"},{default:l(()=>[s(b,{label:"响应信息",name:"response"},{default:l(()=>[t("div",ue,[s(E,{gutter:20},{default:l(()=>[s(c,{span:12},{default:l(()=>[t("div",ce,[e[9]||(e[9]=t("span",{class:"info-label"},"响应时间:",-1)),t("span",de,i(o.value.responseTime)+"ms",1)])]),_:1}),s(c,{span:12},{default:l(()=>[t("div",pe,[e[10]||(e[10]=t("span",{class:"info-label"},"内容大小:",-1)),t("span",me,i(P(o.value.contentLength)),1)])]),_:1}),s(c,{span:12},{default:l(()=>[t("div",_e,[e[11]||(e[11]=t("span",{class:"info-label"},"内容类型:",-1)),t("span",ve,i(o.value.contentType),1)])]),_:1}),s(c,{span:12},{default:l(()=>[t("div",be,[e[12]||(e[12]=t("span",{class:"info-label"},"服务器:",-1)),t("span",he,i(o.value.server),1)])]),_:1})]),_:1})])]),_:1}),s(b,{label:"HTTP头",name:"headers"},{default:l(()=>[t("div",fe,[t("pre",null,i(o.value.headers),1)])]),_:1}),s(b,{label:"页面内容",name:"content"},{default:l(()=>[t("div",ge,[s(u,{modelValue:o.value.content,"onUpdate:modelValue":e[2]||(e[2]=n=>o.value.content=n),type:"textarea",rows:20,readonly:"",class:"content-textarea"},null,8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])])])):Y("",!0)])])])}}},Le=U(we,[["__scopeId","data-v-53c085fe"]]);export{Le as default};
