import{_ as q}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                             *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 */import{E as w,h as T,v as H,ae as B,aj as G,ak as z,u as Q,G as X,H as J,ag as K,ah as Y,s as Z,c as ee,ad as te,aD as ne,au as le,a8 as oe}from"./elementPlus-ef333120.js";import{r as ie,X as se,y as p,z as _,A as i,Q as l,I as o,O as y,L as b,M as a,H as x,u as $}from"./vendor-ad470fc0.js";const re={class:"page-container"},ae={class:"page-content"},ce={class:"tool-container"},de={key:0,class:"result-container"},ue={class:"result-header"},pe={class:"result-actions"},fe={class:"config-info"},me={class:"code-container"},ge={class:"usage-instructions"},_e={key:0,class:"instruction-content"},be={key:1,class:"instruction-content"},ye={key:2,class:"instruction-content"},Ee={key:3,class:"instruction-content"},ve={__name:"file-index-encrypt",setup(he){const m=ie(""),n=se({encryptMode:"htaccess",protectionLevel:"standard",protectionOptions:["directory-listing","file-access"],protectedDirs:`/admin
/uploads
/config`,allowedExtensions:"jpg,png,gif,css,js",ipWhitelist:"",errorPage:"403",customErrorPage:""}),I=()=>{let e="";switch(n.encryptMode){case"htaccess":e=R();break;case"webconfig":e=D();break;case"nginx":e=V();break;case"php":e=C();break}m.value=e,w.success("加密配置生成成功")},R=()=>{let e=`# File Index Encryption Configuration
`;e+=`# Generated by SEO Platform

`,u("directory-listing")&&(e+=`# Disable directory browsing
`,e+=`Options -Indexes

`),u("file-access")&&(e+=`# Deny access to sensitive files
`,e+=`<FilesMatch "\\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
`,e+=`    Require all denied
`,e+=`</FilesMatch>

`);const t=n.protectedDirs.split(`
`).filter(s=>s.trim());if(t.length>0&&(e+=`# Protected directories
`,t.forEach(s=>{e+=`<Directory "${s.trim()}">
`,e+=`    Require all denied
`,e+=`</Directory>
`}),e+=`
`),u("extension-filter")&&n.allowedExtensions){const s=n.allowedExtensions.split(",").map(r=>r.trim()).join("|");e+=`# File extension filtering
`,e+=`RewriteEngine On
`,e+=`RewriteCond %{REQUEST_FILENAME} !\\.(${s})$ [NC]
`,e+=`RewriteRule ^(.*)$ - [F,L]

`}return u("ip-whitelist")&&n.ipWhitelist&&(e+=`# IP whitelist
`,e+=`<RequireAll>
`,n.ipWhitelist.split(`
`).filter(r=>r.trim()).forEach(r=>{e+=`    Require ip ${r.trim()}
`}),e+=`</RequireAll>

`),n.errorPage!=="custom"?(e+=`# Custom error page
`,e+=`ErrorDocument ${n.errorPage} "Access Denied"
`):n.customErrorPage&&(e+=`# Custom error page
`,e+=`ErrorDocument 403 ${n.customErrorPage}
`),e},D=()=>{let e=`<?xml version="1.0" encoding="UTF-8"?>
`;return e+=`<configuration>
`,e+=`  <system.webServer>
`,u("directory-listing")&&(e+=`    <directoryBrowse enabled="false" />
`),u("file-access")&&(e+=`    <security>
`,e+=`      <requestFiltering>
`,e+=`        <hiddenSegments>
`,e+=`          <add segment="web.config" />
`,e+=`          <add segment="bin" />
`,e+=`          <add segment="App_Data" />
`,e+=`        </hiddenSegments>
`,e+=`      </requestFiltering>
`,e+=`    </security>
`),e+=`    <rewrite>
`,e+=`      <rules>
`,n.protectedDirs.split(`
`).filter(s=>s.trim()).forEach((s,r)=>{e+=`        <rule name="Block_${s.trim().replace("/","")}" stopProcessing="true">
`,e+=`          <match url="^${s.trim().substring(1)}(/.*)?$" />
`,e+=`          <action type="CustomResponse" statusCode="403" statusReason="Forbidden" />
`,e+=`        </rule>
`}),e+=`      </rules>
`,e+=`    </rewrite>
`,e+=`  </system.webServer>
`,e+="</configuration>",e},V=()=>{let e=`# File Index Encryption Configuration
`;return e+=`# Generated by SEO Platform

`,u("directory-listing")&&(e+=`# Disable directory browsing
`,e+=`autoindex off;

`),n.protectedDirs.split(`
`).filter(s=>s.trim()).forEach(s=>{e+=`# Protect ${s.trim()}
`,e+=`location ${s.trim()} {
`,e+=`    deny all;
`,e+=`    return 403;
`,e+=`}

`}),u("file-access")&&(e+=`# Protect sensitive files
`,e+=`location ~* \\.(htaccess|htpasswd|ini|log|sh|inc|bak)$ {
`,e+=`    deny all;
`,e+=`    return 403;
`,e+=`}

`),u("ip-whitelist")&&n.ipWhitelist&&(e+=`# IP whitelist
`,n.ipWhitelist.split(`
`).filter(r=>r.trim()).forEach(r=>{e+=`allow ${r.trim()};
`}),e+=`deny all;

`),e},C=()=>{let e=`<?php
`;if(e+=`// File Index Encryption Protection
`,e+=`// Generated by SEO Platform

`,e+=`function checkFileAccess() {
`,u("ip-whitelist")&&n.ipWhitelist){const s=n.ipWhitelist.split(`
`).filter(r=>r.trim()).map(r=>`'${r.trim()}'`).join(", ");e+="    $allowedIPs = ["+s+`];
`,e+=`    $clientIP = $_SERVER['REMOTE_ADDR'];
`,e+=`    if (!in_array($clientIP, $allowedIPs)) {
`,e+=`        http_response_code(403);
`,e+=`        die('Access Denied');
`,e+=`    }

`}u("user-agent-filter")&&(e+=`    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
`,e+=`    $blockedAgents = ['bot', 'crawler', 'spider'];
`,e+=`    foreach ($blockedAgents as $agent) {
`,e+=`        if (stripos($userAgent, $agent) !== false) {
`,e+=`            http_response_code(403);
`,e+=`            die('Access Denied');
`,e+=`        }
`,e+=`    }

`);const t=n.protectedDirs.split(`
`).filter(s=>s.trim());if(t.length>0){const s=t.map(r=>`'${r.trim()}'`).join(", ");e+="    $protectedDirs = ["+s+`];
`,e+=`    $requestURI = $_SERVER['REQUEST_URI'];
`,e+=`    foreach ($protectedDirs as $dir) {
`,e+=`        if (strpos($requestURI, $dir) === 0) {
`,e+=`            http_response_code(403);
`,e+=`            die('Access Denied');
`,e+=`        }
`,e+=`    }

`}return e+=`    return true;
`,e+=`}

`,e+=`// Call the protection function
`,e+=`checkFileAccess();
`,e+="?>",e},u=e=>{var s;return n.protectionLevel==="custom"?n.protectionOptions.includes(e):((s={basic:["directory-listing"],standard:["directory-listing","file-access"],advanced:["directory-listing","file-access","extension-filter","ip-whitelist"]}[n.protectionLevel])==null?void 0:s.includes(e))||!1},A=()=>({htaccess:"Apache .htaccess",webconfig:"IIS web.config",nginx:"Nginx配置",php:"PHP脚本"})[n.encryptMode]||n.encryptMode,U=()=>({basic:"基础保护",standard:"标准保护",advanced:"高级保护",custom:"自定义"})[n.protectionLevel]||n.protectionLevel,L=()=>n.protectedDirs.split(`
`).filter(e=>e.trim()).length,N=()=>n.errorPage==="custom"?n.customErrorPage||"自定义页面":`${n.errorPage} 错误页面`,O=async()=>{try{await navigator.clipboard.writeText(m.value),w.success("配置已复制到剪贴板")}catch{w.error("复制失败，请手动复制")}},S=()=>{const t={htaccess:".htaccess",webconfig:"web.config",nginx:"nginx.conf",php:"protection.php"}[n.encryptMode]||"config.txt",s=new Blob([m.value],{type:"text/plain"}),r=URL.createObjectURL(s),c=document.createElement("a");c.href=r,c.download=t,document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(r),w.success("文件下载成功")},F=()=>{n.encryptMode="htaccess",n.protectionLevel="standard",n.protectionOptions=["directory-listing","file-access"],n.protectedDirs=`/admin
/uploads
/config`,n.allowedExtensions="jpg,png,gif,css,js",n.ipWhitelist="",n.errorPage="403",n.customErrorPage="",m.value=""};return(e,t)=>{const s=G,r=z,c=Q,f=X,k=J,g=K,M=Y,E=Z,P=ee,v=T,j=H,h=te,W=B;return p(),_("div",re,[t[28]||(t[28]=i("div",{class:"page-header"},[i("h2",null,"文件索引加密"),i("p",{class:"page-description"},"对网站文件索引进行加密处理，防止目录遍历和文件泄露")],-1)),i("div",ae,[i("div",ce,[l(j,{model:n,"label-width":"140px",class:"encrypt-form"},{default:o(()=>[l(c,{label:"加密模式",required:""},{default:o(()=>[l(r,{modelValue:n.encryptMode,"onUpdate:modelValue":t[0]||(t[0]=d=>n.encryptMode=d)},{default:o(()=>[l(s,{label:"htaccess"},{default:o(()=>t[8]||(t[8]=[a("Apache .htaccess",-1)])),_:1,__:[8]}),l(s,{label:"webconfig"},{default:o(()=>t[9]||(t[9]=[a("IIS web.config",-1)])),_:1,__:[9]}),l(s,{label:"nginx"},{default:o(()=>t[10]||(t[10]=[a("Nginx配置",-1)])),_:1,__:[10]}),l(s,{label:"php"},{default:o(()=>t[11]||(t[11]=[a("PHP脚本",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"保护级别"},{default:o(()=>[l(k,{modelValue:n.protectionLevel,"onUpdate:modelValue":t[1]||(t[1]=d=>n.protectionLevel=d),placeholder:"选择保护级别"},{default:o(()=>[l(f,{label:"基础保护",value:"basic"}),l(f,{label:"标准保护",value:"standard"}),l(f,{label:"高级保护",value:"advanced"}),l(f,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),n.protectionLevel==="custom"?(p(),x(c,{key:0,label:"保护选项"},{default:o(()=>[l(M,{modelValue:n.protectionOptions,"onUpdate:modelValue":t[2]||(t[2]=d=>n.protectionOptions=d)},{default:o(()=>[l(g,{label:"directory-listing"},{default:o(()=>t[12]||(t[12]=[a("禁止目录浏览",-1)])),_:1,__:[12]}),l(g,{label:"file-access"},{default:o(()=>t[13]||(t[13]=[a("限制文件访问",-1)])),_:1,__:[13]}),l(g,{label:"extension-filter"},{default:o(()=>t[14]||(t[14]=[a("文件扩展名过滤",-1)])),_:1,__:[14]}),l(g,{label:"ip-whitelist"},{default:o(()=>t[15]||(t[15]=[a("IP白名单",-1)])),_:1,__:[15]}),l(g,{label:"user-agent-filter"},{default:o(()=>t[16]||(t[16]=[a("User-Agent过滤",-1)])),_:1,__:[16]}),l(g,{label:"referer-check"},{default:o(()=>t[17]||(t[17]=[a("Referer检查",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1})):b("",!0),l(c,{label:"受保护目录"},{default:o(()=>[l(E,{modelValue:n.protectedDirs,"onUpdate:modelValue":t[3]||(t[3]=d=>n.protectedDirs=d),type:"textarea",rows:4,placeholder:`每行一个目录路径，如：
/admin
/uploads
/config
/logs`},null,8,["modelValue"])]),_:1}),n.protectionOptions.includes("extension-filter")?(p(),x(c,{key:1,label:"允许扩展名"},{default:o(()=>[l(E,{modelValue:n.allowedExtensions,"onUpdate:modelValue":t[4]||(t[4]=d=>n.allowedExtensions=d),placeholder:"允许访问的文件扩展名，用逗号分隔，如：jpg,png,gif,css,js"},null,8,["modelValue"])]),_:1})):b("",!0),n.protectionOptions.includes("ip-whitelist")?(p(),x(c,{key:2,label:"IP白名单"},{default:o(()=>[l(E,{modelValue:n.ipWhitelist,"onUpdate:modelValue":t[5]||(t[5]=d=>n.ipWhitelist=d),type:"textarea",rows:3,placeholder:`每行一个IP地址或IP段，如：
***********
10.0.0.0/8`},null,8,["modelValue"])]),_:1})):b("",!0),l(c,{label:"错误页面"},{default:o(()=>[l(k,{modelValue:n.errorPage,"onUpdate:modelValue":t[6]||(t[6]=d=>n.errorPage=d),placeholder:"选择错误页面类型"},{default:o(()=>[l(f,{label:"403 Forbidden",value:"403"}),l(f,{label:"404 Not Found",value:"404"}),l(f,{label:"500 Internal Error",value:"500"}),l(f,{label:"自定义页面",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),n.errorPage==="custom"?(p(),x(c,{key:3,label:"自定义错误页"},{default:o(()=>[l(E,{modelValue:n.customErrorPage,"onUpdate:modelValue":t[7]||(t[7]=d=>n.customErrorPage=d),placeholder:"自定义错误页面路径，如：/error.html"},null,8,["modelValue"])]),_:1})):b("",!0),l(c,null,{default:o(()=>[l(v,{type:"primary",onClick:I,size:"large"},{default:o(()=>[l(P,null,{default:o(()=>[l($(ne))]),_:1}),t[18]||(t[18]=a(" 生成加密配置 ",-1))]),_:1,__:[18]}),l(v,{onClick:F},{default:o(()=>t[19]||(t[19]=[a("重置",-1)])),_:1,__:[19]})]),_:1})]),_:1},8,["model"]),m.value?(p(),_("div",de,[i("div",ue,[t[22]||(t[22]=i("h3",null,"生成的加密配置",-1)),i("div",pe,[l(v,{type:"primary",size:"small",onClick:O},{default:o(()=>[l(P,null,{default:o(()=>[l($(le))]),_:1}),t[20]||(t[20]=a(" 复制配置 ",-1))]),_:1,__:[20]}),l(v,{type:"success",size:"small",onClick:S},{default:o(()=>[l(P,null,{default:o(()=>[l($(oe))]),_:1}),t[21]||(t[21]=a(" 下载文件 ",-1))]),_:1,__:[21]})])]),i("div",fe,[l(W,{column:2,border:"",size:"small"},{default:o(()=>[l(h,{label:"配置类型"},{default:o(()=>[a(y(A()),1)]),_:1}),l(h,{label:"保护级别"},{default:o(()=>[a(y(U()),1)]),_:1}),l(h,{label:"保护目录"},{default:o(()=>[a(y(L())+" 个",1)]),_:1}),l(h,{label:"错误页面"},{default:o(()=>[a(y(N()),1)]),_:1})]),_:1})]),i("div",me,[i("pre",null,[i("code",null,y(m.value),1)])]),i("div",ge,[t[27]||(t[27]=i("h4",null,"部署说明：",-1)),n.encryptMode==="htaccess"?(p(),_("div",_e,t[23]||(t[23]=[i("ol",null,[i("li",null,"将生成的代码保存为 .htaccess 文件"),i("li",null,"上传到需要保护的目录中"),i("li",null,"确保Apache服务器启用了相关模块"),i("li",null,"测试配置是否生效")],-1)]))):n.encryptMode==="webconfig"?(p(),_("div",be,t[24]||(t[24]=[i("ol",null,[i("li",null,"将生成的代码保存为 web.config 文件"),i("li",null,"放置在网站根目录或需要保护的目录"),i("li",null,"确保IIS启用了URL重写模块"),i("li",null,"重启IIS应用程序池")],-1)]))):n.encryptMode==="nginx"?(p(),_("div",ye,t[25]||(t[25]=[i("ol",null,[i("li",null,"将生成的配置添加到Nginx配置文件"),i("li",null,"放置在server块或location块中"),i("li",null,"重新加载Nginx配置"),i("li",null,"测试配置是否生效")],-1)]))):n.encryptMode==="php"?(p(),_("div",Ee,t[26]||(t[26]=[i("ol",null,[i("li",null,"将生成的PHP代码保存为 .php 文件"),i("li",null,"在需要保护的页面顶部包含此文件"),i("li",null,"确保PHP环境正常运行"),i("li",null,"测试保护功能")],-1)]))):b("",!0)])])):b("",!0)])])])}}},Ne=q(ve,[["__scopeId","data-v-c70276c1"]]);export{Ne as default};
