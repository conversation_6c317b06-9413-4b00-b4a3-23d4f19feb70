import{_ as J}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                 *//* empty css                    *//* empty css               *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                             *//* empty css                    *//* empty css                     *//* empty css                  *//* empty css                          *//* empty css                 *//* empty css                */import{r as x,X as G,y as d,z as C,Q as e,I as t,A as a,u as w,M as n,a5 as Q,O as r,H as c,L as h,P as W,a4 as X}from"./vendor-ad470fc0.js";import{E as T,c as $,x as q,s as Y,u as Z,ag as ee,ah as te,h as le,v as se,y as ae,A as oe,I as ne,ad as re,ae as ue,D as ie,B as de,C as _e,ai as pe,z as ce,F as me,a6 as fe,a7 as ve,R as B,L as ye}from"./elementPlus-ef333120.js";const ge={class:"seo-health-container"},ke={class:"header-content"},be={class:"results-header"},we={class:"score-overview"},he={class:"score-description"},Ee={class:"check-item"},Se={class:"check-item"},xe={class:"check-item"},Ce={class:"speed-suggestions"},Te={class:"check-item"},Ve={__name:"seo-health",setup(Oe){const f=x(!1),V=x("meta"),s=x(null),i=G({url:"",checkItems:["meta","keywords","speed","structure"]}),E=async()=>{if(!i.url){T.warning("请输入要检测的网站URL");return}f.value=!0;try{const o=await fetch("/api/tools/seo-health",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:i.url.startsWith("http")?i.url:`https://${i.url}`,check_items:i.checkItems})});if(o.ok)s.value=await o.json(),T.success("SEO检测完成");else throw new Error("检测失败")}catch(o){T.error("检测失败: "+o.message),s.value={overall_score:75,meta:{title:{status:"good",content:"示例网站 - 专业的SEO优化服务",length:18},description:{status:"warning",content:"这是一个示例网站的描述，提供专业的SEO优化服务",length:28}},keywords:{top_keywords:[{keyword:"SEO",count:15,density:2.5,suggestion:"密度适中"},{keyword:"优化",count:12,density:2,suggestion:"密度适中"},{keyword:"网站",count:8,density:1.3,suggestion:"可以适当增加"}]},speed:{load_time:1250,page_size:850,suggestions:["压缩图片以减少页面大小","启用浏览器缓存","压缩CSS和JavaScript文件"]},structure:{h1_count:1,heading_structure:"H1(1) > H2(3) > H3(5)",internal_links:25,external_links:8}}}finally{f.value=!1}},L=o=>o>=80?"success":o>=60?"warning":"danger",U=o=>o>=80?"#67c23a":o>=60?"#e6a23c":"#f56c6c",R=o=>o>=80?"SEO状况良好，继续保持！":o>=60?"SEO状况一般，建议优化":"SEO状况较差，需要重点优化",z=o=>o>=1&&o<=3?"success":o>3?"warning":"info";return(o,l)=>{const v=$,y=q,M=Y,S=Z,_=ee,N=te,O=le,P=se,g=ae,m=oe,F=ne,p=re,I=ue,k=ie,b=de,A=_e,H=pe,D=ce,K=me,j=fe;return d(),C("div",ge,[e(y,{class:"page-header"},{default:t(()=>[a("div",ke,[a("h2",null,[e(v,null,{default:t(()=>[e(w(ve))]),_:1}),l[3]||(l[3]=n(" SEO健康度检测",-1))]),l[4]||(l[4]=a("p",null,"全面分析网站的SEO健康状况，提供专业的优化建议",-1))])]),_:1}),e(D,{gutter:20},{default:t(()=>[e(g,{span:8},{default:t(()=>[e(y,{class:"analysis-form"},{header:t(()=>[a("span",null,[e(v,null,{default:t(()=>[e(w(B))]),_:1}),l[5]||(l[5]=n(" 网站检测",-1))])]),default:t(()=>[e(P,{model:i,"label-width":"80px"},{default:t(()=>[e(S,{label:"网站URL"},{default:t(()=>[e(M,{modelValue:i.url,"onUpdate:modelValue":l[0]||(l[0]=u=>i.url=u),placeholder:"请输入要检测的网站URL",onKeyup:Q(E,["enter"])},{prepend:t(()=>l[6]||(l[6]=[n("https://",-1)])),_:1},8,["modelValue"])]),_:1}),e(S,{label:"检测项目"},{default:t(()=>[e(N,{modelValue:i.checkItems,"onUpdate:modelValue":l[1]||(l[1]=u=>i.checkItems=u)},{default:t(()=>[e(_,{label:"meta"},{default:t(()=>l[7]||(l[7]=[n("Meta标签检查",-1)])),_:1,__:[7]}),e(_,{label:"keywords"},{default:t(()=>l[8]||(l[8]=[n("关键词密度分析",-1)])),_:1,__:[8]}),e(_,{label:"speed"},{default:t(()=>l[9]||(l[9]=[n("页面加载速度",-1)])),_:1,__:[9]}),e(_,{label:"structure"},{default:t(()=>l[10]||(l[10]=[n("页面结构分析",-1)])),_:1,__:[10]}),e(_,{label:"links"},{default:t(()=>l[11]||(l[11]=[n("链接质量检查",-1)])),_:1,__:[11]}),e(_,{label:"images"},{default:t(()=>l[12]||(l[12]=[n("图片优化检查",-1)])),_:1,__:[12]})]),_:1},8,["modelValue"])]),_:1}),e(S,null,{default:t(()=>[e(O,{type:"primary",onClick:E,loading:f.value,style:{width:"100%"}},{default:t(()=>[e(v,null,{default:t(()=>[e(w(B))]),_:1}),n(" "+r(f.value?"检测中...":"开始检测"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(g,{span:16},{default:t(()=>[s.value?(d(),c(y,{key:0,class:"analysis-results"},{header:t(()=>[a("div",be,[a("span",null,[e(v,null,{default:t(()=>[e(w(ye))]),_:1}),l[13]||(l[13]=n(" 检测结果",-1))]),e(m,{type:L(s.value.overall_score),size:"large"},{default:t(()=>[n(" 总分: "+r(s.value.overall_score)+"/100 ",1)]),_:1},8,["type"])])]),default:t(()=>[a("div",we,[e(F,{percentage:s.value.overall_score,color:U(s.value.overall_score),"stroke-width":20,"text-inside":""},null,8,["percentage","color"]),a("p",he,r(R(s.value.overall_score)),1)]),e(K,{modelValue:V.value,"onUpdate:modelValue":l[2]||(l[2]=u=>V.value=u),class:"results-tabs"},{default:t(()=>[s.value.meta?(d(),c(k,{key:0,label:"Meta标签",name:"meta"},{default:t(()=>[a("div",Ee,[l[14]||(l[14]=a("h4",null,"Meta标签检查结果",-1)),e(I,{column:2,border:""},{default:t(()=>[e(p,{label:"Title标签"},{default:t(()=>[e(m,{type:s.value.meta.title.status==="good"?"success":"warning"},{default:t(()=>[n(r(s.value.meta.title.status==="good"?"正常":"需优化"),1)]),_:1},8,["type"]),a("p",null,r(s.value.meta.title.content||"未找到Title标签"),1),a("small",null,"长度: "+r(s.value.meta.title.length)+"字符 (建议: 30-60字符)",1)]),_:1}),e(p,{label:"Description"},{default:t(()=>[e(m,{type:s.value.meta.description.status==="good"?"success":"warning"},{default:t(()=>[n(r(s.value.meta.description.status==="good"?"正常":"需优化"),1)]),_:1},8,["type"]),a("p",null,r(s.value.meta.description.content||"未找到Description标签"),1),a("small",null,"长度: "+r(s.value.meta.description.length)+"字符 (建议: 120-160字符)",1)]),_:1})]),_:1})])]),_:1})):h("",!0),s.value.keywords?(d(),c(k,{key:1,label:"关键词密度",name:"keywords"},{default:t(()=>[a("div",Se,[l[15]||(l[15]=a("h4",null,"关键词密度分析",-1)),e(A,{data:s.value.keywords.top_keywords,style:{width:"100%"}},{default:t(()=>[e(b,{prop:"keyword",label:"关键词",width:"200"}),e(b,{prop:"count",label:"出现次数",width:"120"}),e(b,{prop:"density",label:"密度",width:"120"},{default:t(u=>[e(m,{type:z(u.row.density)},{default:t(()=>[n(r(u.row.density)+"% ",1)]),_:2},1032,["type"])]),_:1}),e(b,{prop:"suggestion",label:"建议"})]),_:1},8,["data"])])]),_:1})):h("",!0),s.value.speed?(d(),c(k,{key:2,label:"页面速度",name:"speed"},{default:t(()=>[a("div",xe,[l[17]||(l[17]=a("h4",null,"页面加载速度分析",-1)),e(D,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(H,{title:"加载时间",value:s.value.speed.load_time,suffix:"ms"},null,8,["value"])]),_:1}),e(g,{span:12},{default:t(()=>[e(H,{title:"页面大小",value:s.value.speed.page_size,suffix:"KB"},null,8,["value"])]),_:1})]),_:1}),a("div",Ce,[l[16]||(l[16]=a("h5",null,"优化建议:",-1)),a("ul",null,[(d(!0),C(W,null,X(s.value.speed.suggestions,u=>(d(),C("li",{key:u},r(u),1))),128))])])])]),_:1})):h("",!0),s.value.structure?(d(),c(k,{key:3,label:"页面结构",name:"structure"},{default:t(()=>[a("div",Te,[l[18]||(l[18]=a("h4",null,"页面结构分析",-1)),e(I,{column:2,border:""},{default:t(()=>[e(p,{label:"H1标签"},{default:t(()=>[e(m,{type:s.value.structure.h1_count===1?"success":"warning"},{default:t(()=>[n(r(s.value.structure.h1_count)+"个 ",1)]),_:1},8,["type"])]),_:1}),e(p,{label:"H2-H6标签"},{default:t(()=>[a("span",null,r(s.value.structure.heading_structure),1)]),_:1}),e(p,{label:"内部链接"},{default:t(()=>[a("span",null,r(s.value.structure.internal_links)+"个",1)]),_:1}),e(p,{label:"外部链接"},{default:t(()=>[a("span",null,r(s.value.structure.external_links)+"个",1)]),_:1})]),_:1})])]),_:1})):h("",!0)]),_:1},8,["modelValue"])]),_:1})):(d(),c(y,{key:1,class:"empty-state"},{default:t(()=>[e(j,{description:"请输入网站URL并开始检测"},{default:t(()=>[e(O,{type:"primary",onClick:E},{default:t(()=>l[19]||(l[19]=[n("开始检测",-1)])),_:1,__:[19]})]),_:1})]),_:1}))]),_:1})]),_:1})])}}},Xe=J(Ve,[["__scopeId","data-v-69616d59"]]);export{Xe as default};
