import{_ as Te}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                    *//* empty css                 *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                */import{aB as ze,r as u,X as z,k as Ue,y as m,z as U,A as s,Q as t,I as a,O as d,H as B,L as q,a5 as Be,J as Ie,u as b,M as n,P as $,a4 as A}from"./vendor-ad470fc0.js";import{a as J,E as w,c as Fe,x as De,h as Pe,s as Re,H as Me,C as Oe,J as Ne,ac as $e,K as Ae,w as Ge,G as Le,B as He,A as Ke,u as Ye,V as qe,aj as Je,ak as Xe,v as Qe,am as We,aA as Ze,M as et,N as tt,aB as X,a8 as lt,O as at,R as ot}from"./elementPlus-ef333120.js";const st={class:"page-container"},nt={class:"page-header"},rt={class:"breadcrumb"},it={class:"page-content"},ut={class:"project-info"},dt={class:"project-basic"},pt={class:"project-stats"},mt={class:"stat-item"},_t={class:"stat-number"},ct={class:"stat-item"},ft={class:"stat-number"},gt={class:"stat-item"},vt={class:"stat-number"},yt={class:"table-toolbar"},bt={class:"toolbar-left"},wt={class:"toolbar-right"},kt={class:"pagination-container"},ht={class:"dialog-footer"},Vt={class:"import-content"},Ct={key:0,class:"file-info"},xt={class:"dialog-footer"},Et={__name:"keyword-manage",setup(jt){const Q=ze(),D=u(!1),P=u([]),V=u([]),k=u(!1),C=u(!1),x=u(!1),R=u(!1),M=u(!1),O=u(),W=u(),h=u(null),E=u(""),j=z({id:"",project_name:"",project_url:""}),I=z({total:0,groups:0,active:0}),F=u([]),_=z({keyword:"",group:"",status:""}),p=z({page:1,pageSize:10,total:0}),r=z({keyword:"",group_id:"",search_volume:0,competition:1,status:1,notes:""}),Z={keyword:[{required:!0,message:"请输入关键词",trigger:"blur"}],group_id:[{required:!0,message:"请选择词组分类",trigger:"change"}]},ee=o=>({1:"低",2:"中",3:"高"})[o]||"-",te=o=>({1:"success",2:"warning",3:"danger"})[o]||"info",le=o=>Ge(o).format("YYYY-MM-DD HH:mm:ss"),ae=async()=>{try{const o=Q.params.id;await new Promise(e=>setTimeout(e,300)),Object.assign(j,{id:o,project_name:"测试项目1",project_url:"https://example1.com"})}catch(o){console.error("Failed to fetch project info:",o)}},oe=async()=>{try{await new Promise(o=>setTimeout(o,300)),F.value=[{id:1,name:"核心词汇"},{id:2,name:"长尾词汇"},{id:3,name:"品牌词汇"}]}catch(o){console.error("Failed to fetch keyword groups:",o)}},S=async()=>{try{await new Promise(o=>setTimeout(o,300)),Object.assign(I,{total:150,groups:3,active:120})}catch(o){console.error("Failed to fetch stats:",o)}},c=async()=>{D.value=!0;try{await new Promise(o=>setTimeout(o,500)),P.value=[{id:1,keyword:"SEO优化",group_id:1,group_name:"核心词汇",search_volume:1200,competition:2,ranking:15,status:1,notes:"重点优化词汇",created_at:new Date}],p.total=1}catch(o){console.error("Failed to fetch keywords:",o)}finally{D.value=!1}},G=()=>{p.page=1,c()},se=()=>{_.keyword="",_.group="",_.status="",p.page=1,c()},ne=o=>{p.pageSize=o,p.page=1,c()},re=o=>{p.page=o,c()},ie=o=>{V.value=o},ue=()=>{x.value=!1,be(),k.value=!0},de=o=>{x.value=!0,Object.assign(r,o),k.value=!0},pe=o=>{J.confirm(`确定要删除关键词 "${o.keyword}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{w.success("删除成功"),c(),S()})},me=()=>{V.value.length&&J.confirm(`确定要删除选中的 ${V.value.length} 个关键词吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{w.success("批量删除成功"),c(),S(),V.value=[]})},_e=o=>{w.info("关键词分析功能开发中...")},ce=()=>{C.value=!0,h.value=null,E.value=""},fe=()=>{w.info("正在导出词库..."),setTimeout(()=>{w.success("导出完成")},1e3)},ge=o=>{h.value=o},ve=async()=>{if(!(!h.value||!E.value)){M.value=!0;try{await new Promise(o=>setTimeout(o,2e3)),w.success("导入成功"),C.value=!1,c(),S()}catch(o){console.error("Import failed:",o)}finally{M.value=!1}}},ye=async()=>{if(O.value)try{await O.value.validate(),R.value=!0,await new Promise(o=>setTimeout(o,1e3)),w.success(x.value?"更新成功":"添加成功"),k.value=!1,c(),S()}catch(o){console.error("Submit failed:",o)}finally{R.value=!1}},be=()=>{Object.assign(r,{keyword:"",group_id:"",search_volume:0,competition:1,status:1,notes:""})};return Ue(()=>{ae(),oe(),S(),c()}),(o,e)=>{const v=Fe,we=De,i=Pe,N=Re,g=Le,T=Me,f=He,L=Ke,ke=Oe,he=Ne,y=Ye,Ve=qe,H=Je,Ce=Xe,xe=Qe,K=$e,Ee=We,je=Ze,Se=Ae;return m(),U("div",st,[s("div",nt,[e[17]||(e[17]=s("h2",null,"项目词库管理",-1)),s("div",rt,[e[16]||(e[16]=s("span",null,"项目管理",-1)),t(v,null,{default:a(()=>[t(b(et))]),_:1}),s("span",null,d(j.project_name||"项目词库管理"),1)])]),s("div",it,[j.id?(m(),B(we,{key:0,class:"project-info-card"},{default:a(()=>[s("div",ut,[s("div",dt,[s("h3",null,d(j.project_name),1),s("p",null,d(j.project_url),1)]),s("div",pt,[s("div",mt,[s("span",_t,d(I.total),1),e[18]||(e[18]=s("span",{class:"stat-label"},"总关键词",-1))]),s("div",ct,[s("span",ft,d(I.groups),1),e[19]||(e[19]=s("span",{class:"stat-label"},"词组数量",-1))]),s("div",gt,[s("span",vt,d(I.active),1),e[20]||(e[20]=s("span",{class:"stat-label"},"启用词汇",-1))])])])]),_:1})):q("",!0),s("div",yt,[s("div",bt,[t(i,{type:"primary",onClick:ue},{default:a(()=>[t(v,null,{default:a(()=>[t(b(tt))]),_:1}),e[21]||(e[21]=n(" 添加关键词 ",-1))]),_:1,__:[21]}),t(i,{onClick:ce},{default:a(()=>[t(v,null,{default:a(()=>[t(b(X))]),_:1}),e[22]||(e[22]=n(" 批量导入 ",-1))]),_:1,__:[22]}),t(i,{onClick:fe,disabled:!P.value.length},{default:a(()=>[t(v,null,{default:a(()=>[t(b(lt))]),_:1}),e[23]||(e[23]=n(" 导出词库 ",-1))]),_:1,__:[23]},8,["disabled"]),t(i,{onClick:me,disabled:!V.value.length},{default:a(()=>[t(v,null,{default:a(()=>[t(b(at))]),_:1}),e[24]||(e[24]=n(" 批量删除 ",-1))]),_:1,__:[24]},8,["disabled"])]),s("div",wt,[t(N,{modelValue:_.keyword,"onUpdate:modelValue":e[0]||(e[0]=l=>_.keyword=l),placeholder:"搜索关键词",style:{width:"300px","margin-right":"10px"},clearable:"",onKeyup:Be(G,["enter"])},{prefix:a(()=>[t(v,null,{default:a(()=>[t(b(ot))]),_:1})]),_:1},8,["modelValue"]),t(T,{modelValue:_.group,"onUpdate:modelValue":e[1]||(e[1]=l=>_.group=l),placeholder:"词组分类",style:{width:"150px","margin-right":"10px"},clearable:""},{default:a(()=>[(m(!0),U($,null,A(F.value,l=>(m(),B(g,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(T,{modelValue:_.status,"onUpdate:modelValue":e[2]||(e[2]=l=>_.status=l),placeholder:"状态",style:{width:"100px","margin-right":"10px"},clearable:""},{default:a(()=>[t(g,{label:"启用",value:1}),t(g,{label:"禁用",value:0})]),_:1},8,["modelValue"]),t(i,{onClick:G},{default:a(()=>e[25]||(e[25]=[n("搜索",-1)])),_:1,__:[25]}),t(i,{onClick:se},{default:a(()=>e[26]||(e[26]=[n("重置",-1)])),_:1,__:[26]})])]),Ie((m(),B(ke,{data:P.value,style:{width:"100%"},onSelectionChange:ie},{default:a(()=>[t(f,{type:"selection",width:"55"}),t(f,{prop:"keyword",label:"关键词","min-width":"150"}),t(f,{prop:"group_name",label:"词组分类",width:"120"}),t(f,{prop:"search_volume",label:"搜索量",width:"100"},{default:a(({row:l})=>[s("span",null,d(l.search_volume||"-"),1)]),_:1}),t(f,{prop:"competition",label:"竞争度",width:"100"},{default:a(({row:l})=>[t(L,{type:te(l.competition)},{default:a(()=>[n(d(ee(l.competition)),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"ranking",label:"当前排名",width:"100"},{default:a(({row:l})=>[s("span",null,d(l.ranking||"-"),1)]),_:1}),t(f,{prop:"status",label:"状态",width:"80"},{default:a(({row:l})=>[t(L,{type:l.status===1?"success":"danger"},{default:a(()=>[n(d(l.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"created_at",label:"添加时间",width:"160"},{default:a(({row:l})=>[n(d(le(l.created_at)),1)]),_:1}),t(f,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[t(i,{type:"text",size:"small",onClick:Y=>de(l)},{default:a(()=>e[27]||(e[27]=[n(" 编辑 ",-1)])),_:2,__:[27]},1032,["onClick"]),t(i,{type:"text",size:"small",onClick:Y=>_e(l)},{default:a(()=>e[28]||(e[28]=[n(" 分析 ",-1)])),_:2,__:[28]},1032,["onClick"]),t(i,{type:"text",size:"small",onClick:Y=>pe(l),style:{color:"#f56c6c"}},{default:a(()=>e[29]||(e[29]=[n(" 删除 ",-1)])),_:2,__:[29]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Se,D.value]]),s("div",kt,[t(he,{"current-page":p.page,"onUpdate:currentPage":e[3]||(e[3]=l=>p.page=l),"page-size":p.pageSize,"onUpdate:pageSize":e[4]||(e[4]=l=>p.pageSize=l),"page-sizes":[10,20,50,100],total:p.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ne,onCurrentChange:re},null,8,["current-page","page-size","total"])])]),t(K,{modelValue:k.value,"onUpdate:modelValue":e[12]||(e[12]=l=>k.value=l),title:x.value?"编辑关键词":"添加关键词",width:"500px"},{footer:a(()=>[s("span",ht,[t(i,{onClick:e[11]||(e[11]=l=>k.value=!1)},{default:a(()=>e[32]||(e[32]=[n("取消",-1)])),_:1,__:[32]}),t(i,{type:"primary",onClick:ye,loading:R.value},{default:a(()=>[n(d(x.value?"更新":"添加"),1)]),_:1},8,["loading"])])]),default:a(()=>[t(xe,{ref_key:"formRef",ref:O,model:r,rules:Z,"label-width":"100px"},{default:a(()=>[t(y,{label:"关键词",prop:"keyword"},{default:a(()=>[t(N,{modelValue:r.keyword,"onUpdate:modelValue":e[5]||(e[5]=l=>r.keyword=l),placeholder:"请输入关键词"},null,8,["modelValue"])]),_:1}),t(y,{label:"词组分类",prop:"group_id"},{default:a(()=>[t(T,{modelValue:r.group_id,"onUpdate:modelValue":e[6]||(e[6]=l=>r.group_id=l),placeholder:"请选择词组分类",style:{width:"100%"}},{default:a(()=>[(m(!0),U($,null,A(F.value,l=>(m(),B(g,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"搜索量",prop:"search_volume"},{default:a(()=>[t(Ve,{modelValue:r.search_volume,"onUpdate:modelValue":e[7]||(e[7]=l=>r.search_volume=l),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(y,{label:"竞争度",prop:"competition"},{default:a(()=>[t(T,{modelValue:r.competition,"onUpdate:modelValue":e[8]||(e[8]=l=>r.competition=l),placeholder:"请选择竞争度",style:{width:"100%"}},{default:a(()=>[t(g,{label:"低",value:1}),t(g,{label:"中",value:2}),t(g,{label:"高",value:3})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"状态",prop:"status"},{default:a(()=>[t(Ce,{modelValue:r.status,"onUpdate:modelValue":e[9]||(e[9]=l=>r.status=l)},{default:a(()=>[t(H,{label:1},{default:a(()=>e[30]||(e[30]=[n("启用",-1)])),_:1,__:[30]}),t(H,{label:0},{default:a(()=>e[31]||(e[31]=[n("禁用",-1)])),_:1,__:[31]})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"备注",prop:"notes"},{default:a(()=>[t(N,{modelValue:r.notes,"onUpdate:modelValue":e[10]||(e[10]=l=>r.notes=l),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(K,{modelValue:C.value,"onUpdate:modelValue":e[15]||(e[15]=l=>C.value=l),title:"批量导入关键词",width:"600px"},{footer:a(()=>[s("span",xt,[t(i,{onClick:e[14]||(e[14]=l=>C.value=!1)},{default:a(()=>e[35]||(e[35]=[n("取消",-1)])),_:1,__:[35]}),t(i,{type:"primary",onClick:ve,loading:M.value,disabled:!h.value||!E.value},{default:a(()=>e[36]||(e[36]=[n(" 开始导入 ",-1)])),_:1,__:[36]},8,["loading","disabled"])])]),default:a(()=>[s("div",Vt,[t(Ee,{title:"导入说明",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:a(()=>e[33]||(e[33]=[s("p",null,"支持CSV、TXT格式文件，每行一个关键词，或使用逗号分隔",-1),s("p",null,"格式示例：关键词1,关键词2,关键词3",-1)])),_:1,__:[33]}),t(je,{ref_key:"uploadRef",ref:W,"auto-upload":!1,"on-change":ge,"show-file-list":!1,accept:".csv,.txt"},{default:a(()=>[t(i,{type:"primary"},{default:a(()=>[t(v,null,{default:a(()=>[t(b(X))]),_:1}),e[34]||(e[34]=n(" 选择文件 ",-1))]),_:1,__:[34]})]),_:1},512),h.value?(m(),U("div",Ct,[s("p",null,"已选择文件："+d(h.value.name),1)])):q("",!0),t(y,{label:"词组分类",style:{"margin-top":"20px"}},{default:a(()=>[t(T,{modelValue:E.value,"onUpdate:modelValue":e[13]||(e[13]=l=>E.value=l),placeholder:"请选择词组分类",style:{width:"100%"}},{default:a(()=>[(m(!0),U($,null,A(F.value,l=>(m(),B(g,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["modelValue"])])}}},qt=Te(Et,[["__scopeId","data-v-6b21a0cf"]]);export{qt as default};
