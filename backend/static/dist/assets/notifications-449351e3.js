import{_ as ie}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css               *//* empty css                */import{r as C,X as D,k as de,al as re,y as g,z as R,A as s,Q as e,I as a,a5 as ue,J as ce,H as z,u as m,O as d,M as i,C as _e,L as k,V as F}from"./vendor-ad470fc0.js";import{E,a as Y,c as pe,z as fe,h as me,s as ve,H as ge,C as ye,J as he,ac as be,K as we,w as Ce,x as ke,y as xe,G as Ve,B as ze,A as Ee,M as Te,aw as Se,ax as Me,ay as Be,az as De,P as Re,O as Fe,R as Ne}from"./elementPlus-ef333120.js";const Pe={class:"page-container"},Ue={class:"page-header"},$e={class:"breadcrumb"},Oe={class:"page-content"},Ae={class:"stats-cards"},He={class:"stats-content"},Ie={class:"stats-icon unread"},Le={class:"stats-info"},Ye={class:"stats-number"},je={class:"stats-content"},Je={class:"stats-icon total"},Ke={class:"stats-info"},Ge={class:"stats-number"},Qe={class:"stats-content"},Xe={class:"stats-icon today"},qe={class:"stats-info"},We={class:"stats-number"},Ze={class:"stats-content"},et={class:"stats-icon important"},tt={class:"stats-info"},at={class:"stats-number"},st={class:"table-toolbar"},lt={class:"toolbar-left"},ot={class:"toolbar-right"},nt={class:"pagination-container"},it={key:0,class:"message-detail"},dt={class:"detail-header"},rt={class:"detail-meta"},ut={class:"detail-time"},ct={class:"detail-content"},_t={key:0,class:"extra-data"},pt={class:"dialog-footer"},ft={__name:"notifications",setup(mt){const T=C(!1),S=C([]),h=C([]),b=C(!1),n=C(null),c=D({unread:0,total:0,today:0,important:0}),_=D({keyword:"",type:"",status:""}),u=D({page:1,pageSize:10,total:0}),N=l=>({1:"系统通知",2:"项目提醒",3:"错误警告",4:"任务完成"})[l]||"未知",P=l=>({1:"info",2:"primary",3:"danger",4:"success"})[l]||"info",U=l=>({1:"普通",2:"重要",3:"紧急"})[l]||"普通",$=l=>({1:"info",2:"warning",3:"danger"})[l]||"info",O=l=>Ce(l).format("YYYY-MM-DD HH:mm:ss"),M=async()=>{try{await new Promise(l=>setTimeout(l,300)),Object.assign(c,{unread:5,total:25,today:3,important:2})}catch(l){console.error("Failed to fetch stats:",l)}},v=async()=>{T.value=!0;try{await new Promise(l=>setTimeout(l,500)),S.value=[{id:1,title:"系统维护通知",content:"系统将于今晚23:00-01:00进行维护，期间可能影响正常使用",type:1,level:2,is_read:!1,extra_data:{maintenance_time:"23:00-01:00"},created_at:new Date},{id:2,title:"项目同步完成",content:'项目"测试项目1"数据同步已完成',type:4,level:1,is_read:!0,extra_data:null,created_at:new Date(Date.now()-36e5)}],u.total=2}catch(l){console.error("Failed to fetch notifications:",l)}finally{T.value=!1}},A=()=>{u.page=1,v()},j=()=>{_.keyword="",_.type="",_.status="",u.page=1,v()},J=l=>{u.pageSize=l,u.page=1,v()},K=l=>{u.page=l,v()},G=l=>{h.value=l},Q=l=>{H(l)},H=l=>{n.value=l,b.value=!0,l.is_read||B(l,!1)},B=async(l,t=!0)=>{try{await new Promise(r=>setTimeout(r,300)),l.is_read=!0,c.unread=Math.max(0,c.unread-1),t&&E.success("已标记为已读")}catch(r){console.error("Failed to mark as read:",r)}},X=()=>{n.value&&!n.value.is_read&&B(n.value,!1),b.value=!1},q=async()=>{try{await new Promise(l=>setTimeout(l,500)),S.value.forEach(l=>{l.is_read=!0}),c.unread=0,E.success("所有消息已标记为已读")}catch(l){console.error("Failed to mark all as read:",l)}},W=l=>{Y.confirm(`确定要删除消息 "${l.title}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{E.success("删除成功"),v(),M()})},Z=()=>{h.value.length&&Y.confirm(`确定要删除选中的 ${h.value.length} 条消息吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{E.success("批量删除成功"),v(),M(),h.value=[]})};return de(()=>{M(),v()}),(l,t)=>{const r=pe,x=ke,V=xe,ee=fe,p=me,te=ve,y=Ve,I=ge,f=ze,ae=re("CircleFilled"),w=Ee,se=ye,le=he,oe=be,ne=we;return g(),R("div",Pe,[s("div",Ue,[t[9]||(t[9]=s("h2",null,"消息通知",-1)),s("div",$e,[t[7]||(t[7]=s("span",null,"项目管理",-1)),e(r,null,{default:a(()=>[e(m(Te))]),_:1}),t[8]||(t[8]=s("span",null,"消息通知",-1))])]),s("div",Oe,[s("div",Ae,[e(ee,{gutter:20},{default:a(()=>[e(V,{span:6},{default:a(()=>[e(x,{class:"stats-card"},{default:a(()=>[s("div",He,[s("div",Ie,[e(r,null,{default:a(()=>[e(m(Se))]),_:1})]),s("div",Le,[s("div",Ye,d(c.unread),1),t[10]||(t[10]=s("div",{class:"stats-label"},"未读消息",-1))])])]),_:1})]),_:1}),e(V,{span:6},{default:a(()=>[e(x,{class:"stats-card"},{default:a(()=>[s("div",je,[s("div",Je,[e(r,null,{default:a(()=>[e(m(Me))]),_:1})]),s("div",Ke,[s("div",Ge,d(c.total),1),t[11]||(t[11]=s("div",{class:"stats-label"},"总消息数",-1))])])]),_:1})]),_:1}),e(V,{span:6},{default:a(()=>[e(x,{class:"stats-card"},{default:a(()=>[s("div",Qe,[s("div",Xe,[e(r,null,{default:a(()=>[e(m(Be))]),_:1})]),s("div",qe,[s("div",We,d(c.today),1),t[12]||(t[12]=s("div",{class:"stats-label"},"今日消息",-1))])])]),_:1})]),_:1}),e(V,{span:6},{default:a(()=>[e(x,{class:"stats-card"},{default:a(()=>[s("div",Ze,[s("div",et,[e(r,null,{default:a(()=>[e(m(De))]),_:1})]),s("div",tt,[s("div",at,d(c.important),1),t[13]||(t[13]=s("div",{class:"stats-label"},"重要消息",-1))])])]),_:1})]),_:1})]),_:1})]),s("div",st,[s("div",lt,[e(p,{type:"primary",onClick:q,disabled:!c.unread},{default:a(()=>[e(r,null,{default:a(()=>[e(m(Re))]),_:1}),t[14]||(t[14]=i(" 全部标记已读 ",-1))]),_:1,__:[14]},8,["disabled"]),e(p,{onClick:Z,disabled:!h.value.length},{default:a(()=>[e(r,null,{default:a(()=>[e(m(Fe))]),_:1}),t[15]||(t[15]=i(" 批量删除 ",-1))]),_:1,__:[15]},8,["disabled"])]),s("div",ot,[e(te,{modelValue:_.keyword,"onUpdate:modelValue":t[0]||(t[0]=o=>_.keyword=o),placeholder:"搜索消息内容",style:{width:"300px","margin-right":"10px"},clearable:"",onKeyup:ue(A,["enter"])},{prefix:a(()=>[e(r,null,{default:a(()=>[e(m(Ne))]),_:1})]),_:1},8,["modelValue"]),e(I,{modelValue:_.type,"onUpdate:modelValue":t[1]||(t[1]=o=>_.type=o),placeholder:"消息类型",style:{width:"120px","margin-right":"10px"},clearable:""},{default:a(()=>[e(y,{label:"系统通知",value:1}),e(y,{label:"项目提醒",value:2}),e(y,{label:"错误警告",value:3}),e(y,{label:"任务完成",value:4})]),_:1},8,["modelValue"]),e(I,{modelValue:_.status,"onUpdate:modelValue":t[2]||(t[2]=o=>_.status=o),placeholder:"读取状态",style:{width:"120px","margin-right":"10px"},clearable:""},{default:a(()=>[e(y,{label:"未读",value:0}),e(y,{label:"已读",value:1})]),_:1},8,["modelValue"]),e(p,{onClick:A},{default:a(()=>t[16]||(t[16]=[i("搜索",-1)])),_:1,__:[16]}),e(p,{onClick:j},{default:a(()=>t[17]||(t[17]=[i("重置",-1)])),_:1,__:[17]})])]),ce((g(),z(se,{data:S.value,style:{width:"100%"},onSelectionChange:G,onRowClick:Q},{default:a(()=>[e(f,{type:"selection",width:"55"}),e(f,{prop:"title",label:"消息标题","min-width":"200"},{default:a(({row:o})=>[s("div",{class:_e(["message-title",{unread:!o.is_read}])},[o.is_read?k("",!0):(g(),z(r,{key:0,class:"unread-dot"},{default:a(()=>[e(ae)]),_:1})),i(" "+d(o.title),1)],2)]),_:1}),e(f,{prop:"content",label:"消息内容","min-width":"300","show-overflow-tooltip":""}),e(f,{prop:"type",label:"消息类型",width:"100"},{default:a(({row:o})=>[e(w,{type:P(o.type)},{default:a(()=>[i(d(N(o.type)),1)]),_:2},1032,["type"])]),_:1}),e(f,{prop:"level",label:"重要程度",width:"100"},{default:a(({row:o})=>[e(w,{type:$(o.level)},{default:a(()=>[i(d(U(o.level)),1)]),_:2},1032,["type"])]),_:1}),e(f,{prop:"is_read",label:"状态",width:"80"},{default:a(({row:o})=>[e(w,{type:o.is_read?"success":"warning"},{default:a(()=>[i(d(o.is_read?"已读":"未读"),1)]),_:2},1032,["type"])]),_:1}),e(f,{prop:"created_at",label:"发送时间",width:"180"},{default:a(({row:o})=>[i(d(O(o.created_at)),1)]),_:1}),e(f,{label:"操作",width:"150",fixed:"right"},{default:a(({row:o})=>[o.is_read?k("",!0):(g(),z(p,{key:0,type:"text",size:"small",onClick:F(L=>B(o),["stop"])},{default:a(()=>t[18]||(t[18]=[i(" 标记已读 ",-1)])),_:2,__:[18]},1032,["onClick"])),e(p,{type:"text",size:"small",onClick:F(L=>H(o),["stop"])},{default:a(()=>t[19]||(t[19]=[i(" 查看 ",-1)])),_:2,__:[19]},1032,["onClick"]),e(p,{type:"text",size:"small",onClick:F(L=>W(o),["stop"]),style:{color:"#f56c6c"}},{default:a(()=>t[20]||(t[20]=[i(" 删除 ",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ne,T.value]]),s("div",nt,[e(le,{"current-page":u.page,"onUpdate:currentPage":t[3]||(t[3]=o=>u.page=o),"page-size":u.pageSize,"onUpdate:pageSize":t[4]||(t[4]=o=>u.pageSize=o),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:J,onCurrentChange:K},null,8,["current-page","page-size","total"])])]),e(oe,{modelValue:b.value,"onUpdate:modelValue":t[6]||(t[6]=o=>b.value=o),title:"消息详情",width:"600px"},{footer:a(()=>[s("span",pt,[e(p,{onClick:t[5]||(t[5]=o=>b.value=!1)},{default:a(()=>t[22]||(t[22]=[i("关闭",-1)])),_:1,__:[22]}),n.value&&!n.value.is_read?(g(),z(p,{key:0,type:"primary",onClick:X},{default:a(()=>t[23]||(t[23]=[i(" 标记已读并关闭 ",-1)])),_:1,__:[23]})):k("",!0)])]),default:a(()=>[n.value?(g(),R("div",it,[s("div",dt,[s("h3",null,d(n.value.title),1),s("div",rt,[e(w,{type:P(n.value.type)},{default:a(()=>[i(d(N(n.value.type)),1)]),_:1},8,["type"]),e(w,{type:$(n.value.level)},{default:a(()=>[i(d(U(n.value.level)),1)]),_:1},8,["type"]),s("span",ut,d(O(n.value.created_at)),1)])]),s("div",ct,[s("p",null,d(n.value.content),1),n.value.extra_data?(g(),R("div",_t,[t[21]||(t[21]=s("h4",null,"附加信息：",-1)),s("pre",null,d(JSON.stringify(n.value.extra_data,null,2)),1)])):k("",!0)])])):k("",!0)]),_:1},8,["modelValue"])])}}},Dt=ie(ft,[["__scopeId","data-v-ac650d0f"]]);export{Dt as default};
