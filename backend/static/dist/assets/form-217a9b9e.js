import{_ as A}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                     *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  */import{aB as Q,aC as X,r as f,c as Y,X as J,k as W,y as R,z as F,A as r,Q as l,I as t,u as i,O as x,M as u,P as Z,a4 as ee}from"./vendor-ad470fc0.js";import{p as h}from"./project-3c35c2de.js";import{E as j,c as le,h as ae,G as te,H as oe,u as re,s as se,U as de,v as ne,D as ue,V as ie,F as pe,x as me,W as E,X as _e,L as fe,Y as be,N as ce,R as ge,Z as Ve,_ as ve,$ as ye,a0 as ke,a1 as xe,Q as he,P as je}from"./elementPlus-ef333120.js";import"./index-bb3d8812.js";const we={class:"project-form-page"},Ue={class:"page-header"},Re={class:"header-content"},Fe={class:"header-left"},Ee={class:"header-text"},Te={class:"page-content"},Oe={class:"form-container"},Ce={class:"card-header"},De={class:"tab-label"},Le={class:"tab-label"},Be={class:"form-section"},Ie={class:"tdk-buttons"},Se={class:"tab-label"},Pe={class:"tab-label"},qe={class:"tab-label"},Ne={class:"tab-label"},Ke={class:"tab-label"},Me={class:"tab-label"},$e={class:"form-actions"},ze={__name:"form",setup(Ge){const y=Q(),T=X(),w=f("basic"),k=f(!1),U=f(),O=f(),C=f(),D=f(),L=f(),B=f(),I=f(),S=f(),V=Y(()=>!!y.params.id),P=[{label:"Title",value:"title"},{label:"Keyword",value:"keyword"},{label:"Description",value:"description"}],a=J({basic:{project_type:"",project_name:"新的项目",project_url:"http://www.example.com/",description:"",monitoring:!1,encoding:"utf-8"},seo:{title_template:"",keywords_template:"",description_template:"",tdk_method:"",tdk_range:[],article_library:"",title_library:"",image_library:""},internal:{quick_rule:"",url_rule:""},external:{external_type:"",custom_external:""},cache:{cache_enabled:!0,cache_method:"tkdb",cache_period:0},ads:{region_block:!1,blocked_regions:"",ads_js:"",error_page:""},push:{shenma_push:!1,shenma_token:"",baidu_push:!1,baidu_token:""},other:{redirect_error:!1,simulate_error:!1,website_error:!1}}),q={project_type:[{required:!0,message:"请选择项目类型",trigger:"change"}],project_name:[{required:!0,message:"请输入项目标识",trigger:"blur"}],project_url:[{required:!0,message:"请输入项目地址",trigger:"blur"}]},N=p=>{const e=a.seo.tdk_range.indexOf(p);e>-1?a.seo.tdk_range.splice(e,1):a.seo.tdk_range.push(p)},K=()=>{j.info("测试URL功能开发中...")},M=async()=>{var p;try{await((p=U.value)==null?void 0:p.validate()),k.value=!0;const e={project_name:a.basic.project_name,project_url:a.basic.project_url,project_type:a.basic.project_type,description:a.basic.description,monitoring:a.basic.monitoring,encoding:a.basic.encoding,seo_config:a.seo,internal_config:a.internal,external_config:a.external,cache_config:a.cache,ads_config:a.ads,push_config:a.push,other_config:a.other};let n;V.value?n=await h.update(y.params.id,e):n=await h.create(e),n.code===200&&(j.success(V.value?"更新成功":"创建成功"),T.push("/project"))}catch(e){console.error("Submit failed:",e),j.error("提交失败，请检查表单数据")}finally{k.value=!1}},$=async()=>{if(V.value)try{const p=await h.get(y.params.id);if(p.code===200){const e=p.data;Object.assign(a.basic,{project_name:e.project_name,project_url:e.project_url,project_type:e.project_type,description:e.description,monitoring:e.monitoring,encoding:e.encoding}),e.seo_config&&Object.assign(a.seo,e.seo_config),e.internal_config&&Object.assign(a.internal,e.internal_config),e.external_config&&Object.assign(a.external,e.external_config),e.cache_config&&Object.assign(a.cache,e.cache_config),e.ads_config&&Object.assign(a.ads,e.ads_config),e.push_config&&Object.assign(a.push,e.push_config),e.other_config&&Object.assign(a.other,e.other_config)}}catch(p){console.error("Failed to fetch project:",p)}};return W(()=>{$()}),(p,e)=>{const n=le,v=ae,d=te,m=oe,s=re,_=se,b=de,c=ne,g=ue,z=ie,G=pe,H=me;return R(),F("div",we,[r("div",Ue,[r("div",Re,[r("div",Fe,[l(n,{class:"header-icon"},{default:t(()=>[l(i(E))]),_:1}),r("div",Ee,[r("h1",null,x(V.value?"编辑项目":"创建单例项目"),1),e[33]||(e[33]=r("p",null,"你只管做，其他的交给我",-1))])]),l(v,{class:"back-btn",onClick:e[0]||(e[0]=o=>p.$router.go(-1))},{default:t(()=>[l(n,null,{default:t(()=>[l(i(_e))]),_:1}),e[34]||(e[34]=u(" 返回 ",-1))]),_:1,__:[34]})])]),r("div",Te,[r("div",Oe,[l(H,{class:"form-card"},{header:t(()=>[r("div",Ce,[l(n,{class:"card-icon"},{default:t(()=>[l(i(fe))]),_:1}),e[35]||(e[35]=u(" 项目配置 ",-1))])]),default:t(()=>[l(G,{modelValue:w.value,"onUpdate:modelValue":e[32]||(e[32]=o=>w.value=o),class:"project-tabs"},{default:t(()=>[l(g,{label:"基础信息",name:"basic"},{label:t(()=>[r("span",De,[l(n,null,{default:t(()=>[l(i(be))]),_:1}),e[36]||(e[36]=u(" 基础信息 ",-1))])]),default:t(()=>[l(c,{ref_key:"basicFormRef",ref:U,model:a.basic,rules:q,"label-width":"120px",class:"tab-form"},{default:t(()=>[l(s,{label:"项目类型",prop:"project_type"},{default:t(()=>[l(m,{modelValue:a.basic.project_type,"onUpdate:modelValue":e[1]||(e[1]=o=>a.basic.project_type=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"泛目录",value:1}),l(d,{label:"寄生虫",value:2}),l(d,{label:"URL劫持",value:3}),l(d,{label:"404劫持",value:4})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"项目标识",prop:"project_name"},{default:t(()=>[l(_,{modelValue:a.basic.project_name,"onUpdate:modelValue":e[2]||(e[2]=o=>a.basic.project_name=o),placeholder:"新的项目"},null,8,["modelValue"])]),_:1}),l(s,{label:"项目地址",prop:"project_url"},{default:t(()=>[l(_,{modelValue:a.basic.project_url,"onUpdate:modelValue":e[3]||(e[3]=o=>a.basic.project_url=o),placeholder:"http://www.example.com/"},{append:t(()=>[l(v,{onClick:K},{default:t(()=>[l(n,null,{default:t(()=>[l(i(ce))]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"项目备注",prop:"description"},{default:t(()=>[l(_,{modelValue:a.basic.description,"onUpdate:modelValue":e[4]||(e[4]=o=>a.basic.description=o),type:"textarea",rows:3,placeholder:"请输入项目备注"},null,8,["modelValue"])]),_:1}),l(s,{label:"项目监控",prop:"monitoring"},{default:t(()=>[l(b,{modelValue:a.basic.monitoring,"onUpdate:modelValue":e[5]||(e[5]=o=>a.basic.monitoring=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),e[37]||(e[37]=r("div",{class:"form-tip"},' 开启项目监控后，在"其他设置"选项中配置监控的内容 ',-1))]),_:1,__:[37]}),l(s,{label:"网站编码",prop:"encoding"},{default:t(()=>[l(m,{modelValue:a.basic.encoding,"onUpdate:modelValue":e[6]||(e[6]=o=>a.basic.encoding=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"UTF-8",value:"utf-8"}),l(d,{label:"GBK",value:"gbk"}),l(d,{label:"GB2312",value:"gb2312"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),l(g,{label:"SEO选项",name:"seo"},{label:t(()=>[r("span",Le,[l(n,null,{default:t(()=>[l(i(ge))]),_:1}),e[38]||(e[38]=u(" SEO选项 ",-1))])]),default:t(()=>[l(c,{ref_key:"seoFormRef",ref:O,model:a.seo,"label-width":"120px",class:"tab-form"},{default:t(()=>[e[40]||(e[40]=r("div",{class:"form-section"},[r("h4",null,"词库")],-1)),r("div",Be,[e[39]||(e[39]=r("h4",null,"模板",-1)),l(s,{label:"标题",prop:"title_template"},{default:t(()=>[l(m,{modelValue:a.seo.title_template,"onUpdate:modelValue":e[7]||(e[7]=o=>a.seo.title_template=o),placeholder:"可选择多个标题样式 程序随机调用"},{default:t(()=>[l(d,{label:"标题模板1",value:"template1"}),l(d,{label:"标题模板2",value:"template2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"关键词",prop:"keywords_template"},{default:t(()=>[l(m,{modelValue:a.seo.keywords_template,"onUpdate:modelValue":e[8]||(e[8]=o=>a.seo.keywords_template=o),placeholder:"可选择多个关键词样式 程序随机调用"},{default:t(()=>[l(d,{label:"关键词模板1",value:"template1"}),l(d,{label:"关键词模板2",value:"template2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"描述",prop:"description_template"},{default:t(()=>[l(m,{modelValue:a.seo.description_template,"onUpdate:modelValue":e[9]||(e[9]=o=>a.seo.description_template=o),placeholder:"可选择多个描述样式 程序随机调用"},{default:t(()=>[l(d,{label:"描述模板1",value:"template1"}),l(d,{label:"描述模板2",value:"template2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"TDK方式",prop:"tdk_method"},{default:t(()=>[l(m,{modelValue:a.seo.tdk_method,"onUpdate:modelValue":e[10]||(e[10]=o=>a.seo.tdk_method=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"方式1",value:"method1"}),l(d,{label:"方式2",value:"method2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"TDK范围",prop:"tdk_range"},{default:t(()=>[r("div",Ie,[(R(),F(Z,null,ee(P,o=>l(v,{key:o.value,type:a.seo.tdk_range.includes(o.value)?"primary":"default",onClick:He=>N(o.value)},{default:t(()=>[u(x(o.label),1)]),_:2},1032,["type","onClick"])),64))])]),_:1}),l(s,{label:"文章库",prop:"article_library"},{default:t(()=>[l(m,{modelValue:a.seo.article_library,"onUpdate:modelValue":e[11]||(e[11]=o=>a.seo.article_library=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"文章库1",value:"library1"}),l(d,{label:"文章库2",value:"library2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"标题库",prop:"title_library"},{default:t(()=>[l(m,{modelValue:a.seo.title_library,"onUpdate:modelValue":e[12]||(e[12]=o=>a.seo.title_library=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"标题库1",value:"library1"}),l(d,{label:"标题库2",value:"library2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"图片",prop:"image_library"},{default:t(()=>[l(m,{modelValue:a.seo.image_library,"onUpdate:modelValue":e[13]||(e[13]=o=>a.seo.image_library=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"图片库1",value:"library1"}),l(d,{label:"图片库2",value:"library2"})]),_:1},8,["modelValue"])]),_:1})])]),_:1,__:[40]},8,["model"])]),_:1}),l(g,{label:"内链设置",name:"internal"},{label:t(()=>[r("span",Se,[l(n,null,{default:t(()=>[l(i(Ve))]),_:1}),e[41]||(e[41]=u(" 内链设置 ",-1))])]),default:t(()=>[l(c,{ref_key:"internalFormRef",ref:C,model:a.internal,"label-width":"120px",class:"tab-form"},{default:t(()=>[l(s,{label:"快速规则",prop:"quick_rule"},{default:t(()=>[l(m,{modelValue:a.internal.quick_rule,"onUpdate:modelValue":e[14]||(e[14]=o=>a.internal.quick_rule=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"规则1",value:"rule1"}),l(d,{label:"规则2",value:"rule2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"URL规则",prop:"url_rule"},{default:t(()=>[l(_,{modelValue:a.internal.url_rule,"onUpdate:modelValue":e[15]||(e[15]=o=>a.internal.url_rule=o),type:"textarea",rows:8,placeholder:"一行代表一个规则 标签参照下方提示"},null,8,["modelValue"])]),_:1}),l(s,{label:"标签提示"},{default:t(()=>e[42]||(e[42]=[r("div",{class:"tag-tips"},[r("p",null,[r("strong",null,"字符标签："),u("{数字}、{字母}、{大写字母}、{大小写字母}、{大写字母数字}、{大小写字母数字}、{数字字母}、{随机字符}")]),r("p",null,[r("strong",null,"时间标签："),u("{日期}、{年}、{月}、{日}、{时}、{分}、{秒}")]),r("p",null,[r("strong",null,"动态标签："),u("数字和字母标签后面加数字表位数，如：{数字8}表示8个数字、{数字1-8}范围1-8个数字")])],-1)])),_:1,__:[42]})]),_:1},8,["model"])]),_:1}),l(g,{label:"外链设置",name:"external"},{label:t(()=>[r("span",Pe,[l(n,null,{default:t(()=>[l(i(ve))]),_:1}),e[43]||(e[43]=u(" 外链设置 ",-1))])]),default:t(()=>[l(c,{ref_key:"externalFormRef",ref:D,model:a.external,"label-width":"120px",class:"tab-form"},{default:t(()=>[l(s,{label:"外链类别",prop:"external_type"},{default:t(()=>[l(m,{modelValue:a.external.external_type,"onUpdate:modelValue":e[16]||(e[16]=o=>a.external.external_type=o),placeholder:"请选择"},{default:t(()=>[l(d,{label:"类别1",value:"type1"}),l(d,{label:"类别2",value:"type2"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"自定义外链",prop:"custom_external"},{default:t(()=>[l(_,{modelValue:a.external.custom_external,"onUpdate:modelValue":e[17]||(e[17]=o=>a.external.custom_external=o),type:"textarea",rows:8,placeholder:"一行代表一个规则"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),l(g,{label:"缓存设置",name:"cache"},{label:t(()=>[r("span",qe,[l(n,null,{default:t(()=>[l(i(ye))]),_:1}),e[44]||(e[44]=u(" 缓存设置 ",-1))])]),default:t(()=>[l(c,{ref_key:"cacheFormRef",ref:L,model:a.cache,"label-width":"120px",class:"tab-form"},{default:t(()=>[l(s,{label:"缓存开关",prop:"cache_enabled"},{default:t(()=>[l(b,{modelValue:a.cache.cache_enabled,"onUpdate:modelValue":e[18]||(e[18]=o=>a.cache.cache_enabled=o),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),e[45]||(e[45]=r("div",{class:"form-tip"}," 缓存关闭后页面将动态更新 ",-1))]),_:1,__:[45]}),l(s,{label:"缓存方法",prop:"cache_method"},{default:t(()=>[l(m,{modelValue:a.cache.cache_method,"onUpdate:modelValue":e[19]||(e[19]=o=>a.cache.cache_method=o),placeholder:"请选择缓存方法"},{default:t(()=>[l(d,{label:"缓存TKDB",value:"tkdb"}),l(d,{label:"文件缓存",value:"file"}),l(d,{label:"内存缓存",value:"memory"})]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"缓存周期",prop:"cache_period"},{default:t(()=>[l(z,{modelValue:a.cache.cache_period,"onUpdate:modelValue":e[20]||(e[20]=o=>a.cache.cache_period=o),min:0,style:{width:"200px"}},null,8,["modelValue"]),e[46]||(e[46]=r("span",{class:"form-tip",style:{"margin-left":"10px"}}," 单位：天 如果大于缓存有效期则会强制删除缓存 ",-1))]),_:1,__:[46]})]),_:1},8,["model"])]),_:1}),l(g,{label:"广告设置",name:"ads"},{label:t(()=>[r("span",Ne,[l(n,null,{default:t(()=>[l(i(ke))]),_:1}),e[47]||(e[47]=u(" 广告设置 ",-1))])]),default:t(()=>[l(c,{ref_key:"adsFormRef",ref:B,model:a.ads,"label-width":"120px",class:"tab-form"},{default:t(()=>[l(s,{label:"地区屏蔽",prop:"region_block"},{default:t(()=>[l(b,{modelValue:a.ads.region_block,"onUpdate:modelValue":e[21]||(e[21]=o=>a.ads.region_block=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),e[48]||(e[48]=r("div",{class:"form-tip"}," 开启后会在跳转的页面进行地区判断 ",-1))]),_:1,__:[48]}),l(s,{label:"地区",prop:"blocked_regions"},{default:t(()=>[l(_,{modelValue:a.ads.blocked_regions,"onUpdate:modelValue":e[22]||(e[22]=o=>a.ads.blocked_regions=o),placeholder:"请输入被屏蔽的地区 如北京|上海等"},null,8,["modelValue"]),e[49]||(e[49]=r("div",{class:"form-tip"}," 被屏蔽的地区会显示404错误页面 ",-1))]),_:1,__:[49]}),l(s,{label:"广告JS",prop:"ads_js"},{default:t(()=>[l(_,{modelValue:a.ads.ads_js,"onUpdate:modelValue":e[23]||(e[23]=o=>a.ads.ads_js=o),placeholder:"https://www.example.com/advertise.js"},null,8,["modelValue"])]),_:1}),l(s,{label:"错误页",prop:"error_page"},{default:t(()=>[l(_,{modelValue:a.ads.error_page,"onUpdate:modelValue":e[24]||(e[24]=o=>a.ads.error_page=o),type:"textarea",rows:8,placeholder:"<!DOCTYPE HTML PUBLIC '-//IETF//DTD HTML 2.0//EN'>..."},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),l(g,{label:"推送设置",name:"push"},{label:t(()=>[r("span",Ke,[l(n,null,{default:t(()=>[l(i(xe))]),_:1}),e[50]||(e[50]=u(" 推送设置 ",-1))])]),default:t(()=>[l(c,{ref_key:"pushFormRef",ref:I,model:a.push,"label-width":"120px",class:"tab-form"},{default:t(()=>[l(s,{label:"神马推送",prop:"shenma_push"},{default:t(()=>[l(b,{modelValue:a.push.shenma_push,"onUpdate:modelValue":e[25]||(e[25]=o=>a.push.shenma_push=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),e[51]||(e[51]=r("div",{class:"form-tip"}," 把URL提交给神马搜索引擎，加快收录 ",-1))]),_:1,__:[51]}),l(s,{label:"神马Token",prop:"shenma_token"},{default:t(()=>[l(_,{modelValue:a.push.shenma_token,"onUpdate:modelValue":e[26]||(e[26]=o=>a.push.shenma_token=o),placeholder:"神马推送token 需在神马站长平台获取"},null,8,["modelValue"])]),_:1}),l(s,{label:"百度推送",prop:"baidu_push"},{default:t(()=>[l(b,{modelValue:a.push.baidu_push,"onUpdate:modelValue":e[27]||(e[27]=o=>a.push.baidu_push=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),e[52]||(e[52]=r("div",{class:"form-tip"}," 把URL提交给百度搜索引擎，加快收录 ",-1))]),_:1,__:[52]}),l(s,{label:"百度Token",prop:"baidu_token"},{default:t(()=>[l(_,{modelValue:a.push.baidu_token,"onUpdate:modelValue":e[28]||(e[28]=o=>a.push.baidu_token=o),placeholder:"百度推送token 需在百度站长平台获取"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),l(g,{label:"其他设置",name:"other"},{label:t(()=>[r("span",Me,[l(n,null,{default:t(()=>[l(i(E))]),_:1}),e[53]||(e[53]=u(" 其他设置 ",-1))])]),default:t(()=>[l(c,{ref_key:"otherFormRef",ref:S,model:a.other,"label-width":"120px",class:"tab-form"},{default:t(()=>[l(s,{label:"跳转异常",prop:"redirect_error"},{default:t(()=>[l(b,{modelValue:a.other.redirect_error,"onUpdate:modelValue":e[29]||(e[29]=o=>a.other.redirect_error=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),e[54]||(e[54]=r("div",{class:"form-tip"},' 模拟跳转，获取返回结果 如有异常将会推送已配置好的"消息通知" ',-1))]),_:1,__:[54]}),l(s,{label:"模拟异常",prop:"simulate_error"},{default:t(()=>[l(b,{modelValue:a.other.simulate_error,"onUpdate:modelValue":e[30]||(e[30]=o=>a.other.simulate_error=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),e[55]||(e[55]=r("div",{class:"form-tip"},' 模拟抓取，获取返回结果 如有异常将会推送已配置好的"消息通知" ',-1))]),_:1,__:[55]}),l(s,{label:"网站异常",prop:"website_error"},{default:t(()=>[l(b,{modelValue:a.other.website_error,"onUpdate:modelValue":e[31]||(e[31]=o=>a.other.website_error=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),e[56]||(e[56]=r("div",{class:"form-tip"},' 网站异常，如 502 404 或域名错误将会推送已配置好的"消息通知" ',-1))]),_:1,__:[56]})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"]),r("div",$e,[l(v,{class:"cancel-btn",size:"large",onClick:p.handleCancel},{default:t(()=>[l(n,null,{default:t(()=>[l(i(he))]),_:1}),e[57]||(e[57]=u(" 取消 ",-1))]),_:1,__:[57]},8,["onClick"]),l(v,{class:"submit-btn",type:"primary",size:"large",onClick:M,loading:k.value},{default:t(()=>[l(n,null,{default:t(()=>[l(i(je))]),_:1}),u(" "+x(V.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),_:1})])])])}}},dl=A(ze,[["__scopeId","data-v-8b3e074b"]]);export{dl as default};
