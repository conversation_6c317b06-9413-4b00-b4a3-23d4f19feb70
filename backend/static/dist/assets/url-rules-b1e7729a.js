import{_ as ae}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  */import{r as m,X as U,k as oe,y as D,z as ne,A as u,Q as e,I as l,a5 as re,J as se,H as ue,u as V,M as r,O as h}from"./vendor-ad470fc0.js";import{a as I,E as x,c as ie,h as de,s as pe,J as _e,C as me,L as ge,a8 as fe,M as ce,w as ye,I as ve,B as be,A as we,u as Ve,W as he,ab as xe,ac as Ce,v as ke,N as Ee,O as Ue,P as Re,S as ze}from"./elementPlus-05e8f3ef.js";const Se={class:"page-container"},Te={class:"page-header"},Be={class:"breadcrumb"},Le={class:"page-content"},De={class:"table-toolbar"},Ie={class:"toolbar-left"},Me={class:"toolbar-right"},Ne={class:"pagination-container"},$e={class:"dialog-footer"},Fe={__name:"url-rules",setup(Oe){const C=m(!1),R=m([]),c=m([]),g=m(!1),y=m(!1),k=m(!1),E=m(),f=U({keyword:"",status:""}),s=U({page:1,pageSize:10,total:0}),n=U({rule_name:"",rule_pattern:"",target_path:"",rule_type:1,priority:10,status:1,description:""}),M={rule_name:[{required:!0,message:"请输入规则名称",trigger:"blur"}],rule_pattern:[{required:!0,message:"请输入URL模式",trigger:"blur"}],target_path:[{required:!0,message:"请输入目标路径",trigger:"blur"}],rule_type:[{required:!0,message:"请选择规则类型",trigger:"change"}]},N=o=>({1:"重写",2:"重定向",3:"代理"})[o]||"未知",$=o=>ye(o).format("YYYY-MM-DD HH:mm:ss"),p=async()=>{C.value=!0;try{await new Promise(o=>setTimeout(o,500)),R.value=[{id:1,rule_name:"分类页面重写",rule_pattern:"/category/{id}",target_path:"/category.php?id={id}",rule_type:1,priority:10,status:1,description:"分类页面URL重写规则",created_at:new Date}],s.total=1}catch(o){console.error("Failed to fetch URL rules:",o)}finally{C.value=!1}},z=()=>{s.page=1,p()},F=()=>{f.keyword="",f.status="",s.page=1,p()},O=o=>{s.pageSize=o,s.page=1,p()},P=o=>{s.page=o,p()},j=o=>{c.value=o},q=()=>{y.value=!1,G(),g.value=!0},Y=o=>{y.value=!0,Object.assign(n,o),g.value=!0},H=o=>{I.confirm(`确定要删除规则 "${o.rule_name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{x.success("删除成功"),p()})},A=()=>{c.value.length&&I.confirm(`确定要删除选中的 ${c.value.length} 个规则吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{x.success("批量删除成功"),p(),c.value=[]})},J=o=>{x.info("测试功能开发中...")},K=async()=>{if(E.value)try{await E.value.validate(),k.value=!0,await new Promise(o=>setTimeout(o,1e3)),x.success(y.value?"更新成功":"创建成功"),g.value=!1,p()}catch(o){console.error("Submit failed:",o)}finally{k.value=!1}},G=()=>{Object.assign(n,{rule_name:"",rule_pattern:"",target_path:"",rule_type:1,priority:10,status:1,description:""})};return oe(()=>{p()}),(o,t)=>{const w=ie,i=de,v=pe,b=ve,S=_e,d=be,T=we,Q=me,W=ge,_=Ve,X=he,B=xe,Z=Ce,ee=ke,te=fe,le=ce;return D(),ne("div",Se,[u("div",Te,[t[15]||(t[15]=u("h2",null,"URL规则管理",-1)),u("div",Be,[t[13]||(t[13]=u("span",null,"项目管理",-1)),e(w,null,{default:l(()=>[e(V(Ee))]),_:1}),t[14]||(t[14]=u("span",null,"URL规则管理",-1))])]),u("div",Le,[u("div",De,[u("div",Ie,[e(i,{type:"primary",onClick:q},{default:l(()=>[e(w,null,{default:l(()=>[e(V(Ue))]),_:1}),t[16]||(t[16]=r(" 新建规则 ",-1))]),_:1,__:[16]}),e(i,{onClick:A,disabled:!c.value.length},{default:l(()=>[e(w,null,{default:l(()=>[e(V(Re))]),_:1}),t[17]||(t[17]=r(" 批量删除 ",-1))]),_:1,__:[17]},8,["disabled"])]),u("div",Me,[e(v,{modelValue:f.keyword,"onUpdate:modelValue":t[0]||(t[0]=a=>f.keyword=a),placeholder:"搜索规则名称或路径",style:{width:"300px","margin-right":"10px"},clearable:"",onKeyup:re(z,["enter"])},{prefix:l(()=>[e(w,null,{default:l(()=>[e(V(ze))]),_:1})]),_:1},8,["modelValue"]),e(S,{modelValue:f.status,"onUpdate:modelValue":t[1]||(t[1]=a=>f.status=a),placeholder:"规则状态",style:{width:"120px","margin-right":"10px"},clearable:""},{default:l(()=>[e(b,{label:"启用",value:1}),e(b,{label:"禁用",value:0})]),_:1},8,["modelValue"]),e(i,{onClick:z},{default:l(()=>t[18]||(t[18]=[r("搜索",-1)])),_:1,__:[18]}),e(i,{onClick:F},{default:l(()=>t[19]||(t[19]=[r("重置",-1)])),_:1,__:[19]})])]),se((D(),ue(Q,{data:R.value,style:{width:"100%"},onSelectionChange:j},{default:l(()=>[e(d,{type:"selection",width:"55"}),e(d,{prop:"rule_name",label:"规则名称","min-width":"150"}),e(d,{prop:"rule_pattern",label:"URL模式","min-width":"200","show-overflow-tooltip":""}),e(d,{prop:"target_path",label:"目标路径","min-width":"200","show-overflow-tooltip":""}),e(d,{prop:"rule_type",label:"规则类型",width:"100"},{default:l(({row:a})=>[e(T,null,{default:l(()=>[r(h(N(a.rule_type)),1)]),_:2},1024)]),_:1}),e(d,{prop:"priority",label:"优先级",width:"80"}),e(d,{prop:"status",label:"状态",width:"80"},{default:l(({row:a})=>[e(T,{type:a.status===1?"success":"danger"},{default:l(()=>[r(h(a.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"created_at",label:"创建时间",width:"180"},{default:l(({row:a})=>[r(h($(a.created_at)),1)]),_:1}),e(d,{label:"操作",width:"200",fixed:"right"},{default:l(({row:a})=>[e(i,{type:"text",size:"small",onClick:L=>Y(a)},{default:l(()=>t[20]||(t[20]=[r(" 编辑 ",-1)])),_:2,__:[20]},1032,["onClick"]),e(i,{type:"text",size:"small",onClick:L=>J(a)},{default:l(()=>t[21]||(t[21]=[r(" 测试 ",-1)])),_:2,__:[21]},1032,["onClick"]),e(i,{type:"text",size:"small",onClick:L=>H(a),style:{color:"#f56c6c"}},{default:l(()=>t[22]||(t[22]=[r(" 删除 ",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[le,C.value]]),u("div",Ne,[e(W,{"current-page":s.page,"onUpdate:currentPage":t[2]||(t[2]=a=>s.page=a),"page-size":s.pageSize,"onUpdate:pageSize":t[3]||(t[3]=a=>s.pageSize=a),"page-sizes":[10,20,50,100],total:s.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:O,onCurrentChange:P},null,8,["current-page","page-size","total"])])]),e(te,{modelValue:g.value,"onUpdate:modelValue":t[12]||(t[12]=a=>g.value=a),title:y.value?"编辑URL规则":"创建URL规则",width:"600px"},{footer:l(()=>[u("span",$e,[e(i,{onClick:t[11]||(t[11]=a=>g.value=!1)},{default:l(()=>t[25]||(t[25]=[r("取消",-1)])),_:1,__:[25]}),e(i,{type:"primary",onClick:K,loading:k.value},{default:l(()=>[r(h(y.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:l(()=>[e(ee,{ref_key:"formRef",ref:E,model:n,rules:M,"label-width":"120px"},{default:l(()=>[e(_,{label:"规则名称",prop:"rule_name"},{default:l(()=>[e(v,{modelValue:n.rule_name,"onUpdate:modelValue":t[4]||(t[4]=a=>n.rule_name=a),placeholder:"请输入规则名称"},null,8,["modelValue"])]),_:1}),e(_,{label:"URL模式",prop:"rule_pattern"},{default:l(()=>[e(v,{modelValue:n.rule_pattern,"onUpdate:modelValue":t[5]||(t[5]=a=>n.rule_pattern=a),placeholder:"如: /category/{id}"},null,8,["modelValue"])]),_:1}),e(_,{label:"目标路径",prop:"target_path"},{default:l(()=>[e(v,{modelValue:n.target_path,"onUpdate:modelValue":t[6]||(t[6]=a=>n.target_path=a),placeholder:"如: /page.php?id={id}"},null,8,["modelValue"])]),_:1}),e(_,{label:"规则类型",prop:"rule_type"},{default:l(()=>[e(S,{modelValue:n.rule_type,"onUpdate:modelValue":t[7]||(t[7]=a=>n.rule_type=a),placeholder:"请选择规则类型"},{default:l(()=>[e(b,{label:"重写",value:1}),e(b,{label:"重定向",value:2}),e(b,{label:"代理",value:3})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"优先级",prop:"priority"},{default:l(()=>[e(X,{modelValue:n.priority,"onUpdate:modelValue":t[8]||(t[8]=a=>n.priority=a),min:1,max:100},null,8,["modelValue"])]),_:1}),e(_,{label:"状态",prop:"status"},{default:l(()=>[e(Z,{modelValue:n.status,"onUpdate:modelValue":t[9]||(t[9]=a=>n.status=a)},{default:l(()=>[e(B,{label:1},{default:l(()=>t[23]||(t[23]=[r("启用",-1)])),_:1,__:[23]}),e(B,{label:0},{default:l(()=>t[24]||(t[24]=[r("禁用",-1)])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"描述",prop:"description"},{default:l(()=>[e(v,{modelValue:n.description,"onUpdate:modelValue":t[10]||(t[10]=a=>n.description=a),type:"textarea",rows:3,placeholder:"请输入规则描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},ot=ae(Fe,[["__scopeId","data-v-a06d479f"]]);export{ot as default};
