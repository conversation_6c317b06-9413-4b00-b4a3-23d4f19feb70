import{_ as L}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                *//* empty css                  */import{aC as j,r as x,X as S,c as K,k as O,y as $,z as R,A as o,Q as e,I as a,u as g,O as f,M as u,a5 as J,J as Q,H as X,D as Z}from"./vendor-ad470fc0.js";import{s as p}from"./index-24913185.js";import{w as q,E,a as G,c as W,h as tt,s as et,x as st,B as at,A as ot,C as lt,L as nt,M as rt,Z as it,N as dt,O as ut,S as B,T as ct}from"./elementPlus-05e8f3ef.js";const C={list(n){return p({url:"/api/users",method:"get",params:n})},detail(n){return p({url:`/api/users/${n}`,method:"get"})},create(n){return p({url:"/api/users",method:"post",data:n})},update(n,c){return p({url:`/api/users/${n}`,method:"put",data:c})},delete(n){return p({url:`/api/users/${n}`,method:"delete"})},resetPassword(n,c){return p({url:`/api/users/${n}/reset-password`,method:"post",data:c})},updateStatus(n,c){return p({url:`/api/users/${n}/status`,method:"put",data:{status:c}})}};const pt={class:"page-container"},_t={class:"page-header"},mt={class:"header-content"},gt={class:"header-left"},ft={class:"header-text"},ht={class:"breadcrumb"},vt={class:"header-stats"},yt={class:"stat-item"},bt={class:"stat-value"},Ct={class:"stat-item"},kt={class:"stat-value"},zt={class:"page-content"},wt={class:"table-toolbar"},xt={class:"toolbar-left"},St={class:"toolbar-right"},$t={class:"search-group"},Et={class:"pagination-container"},Bt={__name:"list",setup(n){const c=j(),y=x(!1),h=x([]),v=S({keyword:""}),r=S({page:1,pageSize:10,total:0}),T=K(()=>h.value.filter(s=>s.status===1).length),k=s=>q(s).format("YYYY-MM-DD HH:mm:ss"),_=async()=>{y.value=!0;try{const s={page:r.page,page_size:r.pageSize,keyword:v.keyword},t=await C.getList(s);t.code===200&&(h.value=t.data.list,r.total=t.data.total)}catch(s){console.error("Failed to fetch users:",s)}finally{y.value=!1}},z=()=>{r.page=1,_()},D=()=>{v.keyword="",r.page=1,_()},M=s=>{r.pageSize=s,r.page=1,_()},V=s=>{r.page=s,_()},I=s=>{c.push(`/user/edit/${s.id}`)},F=async s=>{const t=s.status===1?0:1,i=t===1?"启用":"禁用";try{(await C.update(s.id,{status:t})).code===200&&(E.success(`${i}成功`),s.status=t)}catch(d){console.error(`Failed to ${i} user:`,d)}},N=s=>{G.confirm(`确定要删除用户 "${s.username}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{(await C.delete(s.id)).code===200&&(E.success("删除成功"),_())}catch(t){console.error("Failed to delete user:",t)}})};return O(()=>{_()}),(s,t)=>{const i=W,d=tt,P=et,b=st,m=at,U=ot,Y=lt,A=nt,H=rt;return $(),R("div",pt,[o("div",_t,[o("div",mt,[o("div",gt,[e(i,{class:"header-icon"},{default:a(()=>[e(g(it))]),_:1}),o("div",ft,[t[6]||(t[6]=o("h1",{class:"page-title"},"用户管理",-1)),o("div",ht,[t[4]||(t[4]=o("span",null,"系统管理",-1)),e(i,null,{default:a(()=>[e(g(dt))]),_:1}),t[5]||(t[5]=o("span",{class:"current"},"用户管理",-1))])])]),o("div",vt,[o("div",yt,[o("div",bt,f(h.value.length),1),t[7]||(t[7]=o("div",{class:"stat-label"},"总用户数",-1))]),o("div",Ct,[o("div",kt,f(T.value),1),t[8]||(t[8]=o("div",{class:"stat-label"},"活跃用户",-1))])])])]),o("div",zt,[e(b,{class:"toolbar-card"},{default:a(()=>[o("div",wt,[o("div",xt,[e(d,{type:"primary",class:"action-btn",onClick:t[0]||(t[0]=l=>s.$router.push("/user/create"))},{default:a(()=>[e(i,null,{default:a(()=>[e(g(ut))]),_:1}),t[9]||(t[9]=u(" 新建用户 ",-1))]),_:1,__:[9]})]),o("div",St,[o("div",$t,[e(P,{modelValue:v.keyword,"onUpdate:modelValue":t[1]||(t[1]=l=>v.keyword=l),placeholder:"搜索用户名或邮箱",clearable:"",onKeyup:J(z,["enter"])},{prefix:a(()=>[e(i,null,{default:a(()=>[e(g(B))]),_:1})]),_:1},8,["modelValue"]),e(d,{type:"primary",class:"search-btn",onClick:z},{default:a(()=>[e(i,null,{default:a(()=>[e(g(B))]),_:1}),t[10]||(t[10]=u(" 搜索 ",-1))]),_:1,__:[10]}),e(d,{class:"reset-btn",onClick:D},{default:a(()=>[e(i,null,{default:a(()=>[e(g(ct))]),_:1}),t[11]||(t[11]=u(" 重置 ",-1))]),_:1,__:[11]})])])])]),_:1}),e(b,{class:"table-card"},{default:a(()=>[Q(($(),X(Y,{data:h.value,style:{width:"100%"}},{default:a(()=>[e(m,{prop:"username",label:"用户名",width:"150"}),e(m,{prop:"email",label:"邮箱","min-width":"200"}),e(m,{prop:"status",label:"状态",width:"80"},{default:a(({row:l})=>[e(U,{type:l.status===1?"success":"danger"},{default:a(()=>[u(f(l.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"last_login_at",label:"最后登录",width:"180"},{default:a(({row:l})=>[u(f(l.last_login_at?k(l.last_login_at):"从未登录"),1)]),_:1}),e(m,{prop:"created_at",label:"创建时间",width:"180"},{default:a(({row:l})=>[u(f(k(l.created_at)),1)]),_:1}),e(m,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[e(d,{type:"text",size:"small",onClick:w=>I(l)},{default:a(()=>t[12]||(t[12]=[u(" 编辑 ",-1)])),_:2,__:[12]},1032,["onClick"]),e(d,{type:"text",size:"small",onClick:w=>F(l),style:Z({color:l.status===1?"#f56c6c":"#67c23a"})},{default:a(()=>[u(f(l.status===1?"禁用":"启用"),1)]),_:2},1032,["onClick","style"]),e(d,{type:"text",size:"small",onClick:w=>N(l),style:{color:"#f56c6c"}},{default:a(()=>t[13]||(t[13]=[u(" 删除 ",-1)])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[H,y.value]])]),_:1}),e(b,{class:"pagination-card"},{default:a(()=>[o("div",Et,[e(A,{"current-page":r.page,"onUpdate:currentPage":t[2]||(t[2]=l=>r.page=l),"page-size":r.pageSize,"onUpdate:pageSize":t[3]||(t[3]=l=>r.pageSize=l),"page-sizes":[10,20,50,100],total:r.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:M,onCurrentChange:V},null,8,["current-page","page-size","total"])])]),_:1})])])}}},Ot=L(Bt,[["__scopeId","data-v-ffb324e4"]]);export{Ot as default};
