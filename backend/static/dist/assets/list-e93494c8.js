import{_ as <PERSON>}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                    *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                *//* empty css                 *//* empty css                  */import{c as L,x as O,E as _,h as G,s as H,B as P,A as Q,C as R,G as j,H as q,$ as F,N as J,D as x,O as E,am as W,a4 as X,S as V}from"./elementPlus-05e8f3ef.js";import{r as c,k as Y,y as Z,z as tt,A as a,Q as t,I as e,O as m,u as p,M as n}from"./vendor-ad470fc0.js";const et={class:"page-container"},lt={class:"page-header"},at={class:"header-content"},st={class:"header-left"},ot={class:"header-icon"},nt={class:"header-text"},dt={class:"breadcrumb"},it={class:"header-stats"},pt={class:"stat-item"},ut={class:"stat-value"},_t={class:"stat-item"},rt={class:"stat-value"},ct={class:"page-content"},mt={class:"tab-label"},ft={class:"table-toolbar"},bt={class:"toolbar-left"},vt={class:"toolbar-right"},yt={class:"tab-label"},ht={class:"table-toolbar"},wt={class:"toolbar-left"},kt={class:"toolbar-right"},gt={__name:"list",setup(Ct){const v=c("sites"),y=c(""),h=c(""),w=c([{id:1,name:"主站点",domain:"example.com",template:"模板A",type:"泛目录",status:"运行中",updateTime:"2024-01-15 10:30:00"},{id:2,name:"分站点1",domain:"sub1.example.com",template:"模板B",type:"本地测试",status:"已停止",updateTime:"2024-01-14 15:20:00"}]),k=c([{name:"默认模板",type:"关键词",template:"{keyword} - 专业服务",keywords:"100",content:"优质内容模板",url:"/template/default"},{name:"产品模板",type:"随机生成",template:"产品介绍 - {title}",keywords:"50",content:"产品详情模板",url:"/template/product"}]),$=()=>{_.success("新增站点功能")},z=()=>{_.info("批量导入功能")},D=()=>{_.info("导出数据功能")},B=d=>{_.success(`编辑站点: ${d.name}`)},I=d=>{_.info(`查看站点: ${d.name}`)},K=d=>{_.warning(`删除站点: ${d.name}`)},A=()=>{_.success("新增TDK模板功能")},N=d=>{_.success(`编辑模板: ${d.name}`)},S=d=>{_.warning(`删除模板: ${d.name}`)};return Y(()=>{}),(d,l)=>{const i=L,u=G,g=H,r=O,o=P,b=Q,C=R,T=j,U=q;return Z(),tt("div",et,[a("div",lt,[a("div",at,[a("div",st,[a("div",ot,[t(i,null,{default:e(()=>[t(p(F))]),_:1})]),a("div",nt,[l[5]||(l[5]=a("h2",null,"站群管理",-1)),a("div",dt,[l[3]||(l[3]=a("span",null,"站群管理",-1)),t(i,null,{default:e(()=>[t(p(J))]),_:1}),l[4]||(l[4]=a("span",null,"站点列表",-1))])])]),a("div",it,[a("div",pt,[a("div",ut,m(w.value.length),1),l[6]||(l[6]=a("div",{class:"stat-label"},"站点数量",-1))]),a("div",_t,[a("div",rt,m(k.value.length),1),l[7]||(l[7]=a("div",{class:"stat-label"},"TDK模板",-1))])])])]),a("div",ct,[t(r,{class:"tabs-card"},{default:e(()=>[t(U,{modelValue:v.value,"onUpdate:modelValue":l[2]||(l[2]=s=>v.value=s),class:"site-tabs"},{default:e(()=>[t(T,{label:"站点列表",name:"sites"},{label:e(()=>[a("span",mt,[t(i,null,{default:e(()=>[t(p(x))]),_:1}),l[8]||(l[8]=n(" 站点列表 ",-1))])]),default:e(()=>[t(r,{class:"toolbar-card"},{default:e(()=>[a("div",ft,[a("div",bt,[t(u,{type:"primary",onClick:$,class:"action-btn"},{default:e(()=>[t(i,null,{default:e(()=>[t(p(E))]),_:1}),l[9]||(l[9]=n(" 新增站点 ",-1))]),_:1,__:[9]}),t(u,{onClick:z,class:"action-btn"},{default:e(()=>[t(i,null,{default:e(()=>[t(p(W))]),_:1}),l[10]||(l[10]=n(" 批量导入 ",-1))]),_:1,__:[10]}),t(u,{onClick:D,class:"action-btn"},{default:e(()=>[t(i,null,{default:e(()=>[t(p(X))]),_:1}),l[11]||(l[11]=n(" 导出数据 ",-1))]),_:1,__:[11]})]),a("div",vt,[t(g,{modelValue:y.value,"onUpdate:modelValue":l[0]||(l[0]=s=>y.value=s),placeholder:"搜索站点名称或域名",class:"search-input",clearable:""},{prefix:e(()=>[t(i,null,{default:e(()=>[t(p(V))]),_:1})]),_:1},8,["modelValue"])])])]),_:1}),t(r,{class:"table-card"},{default:e(()=>[t(C,{data:w.value,style:{width:"100%"},class:"enhanced-table"},{default:e(()=>[t(o,{type:"selection",width:"55"}),t(o,{prop:"id",label:"站点ID",width:"80"}),t(o,{prop:"name",label:"站点名称","min-width":"150"}),t(o,{prop:"domain",label:"域名","min-width":"200"}),t(o,{prop:"template",label:"模板",width:"120"}),t(o,{prop:"type",label:"类型",width:"100"},{default:e(({row:s})=>[t(b,{type:s.type==="泛目录"?"primary":"success",size:"small"},{default:e(()=>[n(m(s.type),1)]),_:2},1032,["type"])]),_:1}),t(o,{prop:"status",label:"状态",width:"100"},{default:e(({row:s})=>[t(b,{type:s.status==="运行中"?"success":"danger",size:"small"},{default:e(()=>[n(m(s.status),1)]),_:2},1032,["type"])]),_:1}),t(o,{prop:"updateTime",label:"更新时间",width:"180"}),t(o,{label:"操作",width:"200",fixed:"right"},{default:e(({row:s})=>[t(u,{type:"primary",size:"small",onClick:f=>B(s)},{default:e(()=>l[12]||(l[12]=[n(" 编辑 ",-1)])),_:2,__:[12]},1032,["onClick"]),t(u,{type:"success",size:"small",onClick:f=>I(s)},{default:e(()=>l[13]||(l[13]=[n(" 查看 ",-1)])),_:2,__:[13]},1032,["onClick"]),t(u,{type:"danger",size:"small",onClick:f=>K(s)},{default:e(()=>l[14]||(l[14]=[n(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),t(T,{label:"TDK调用模板",name:"tdk"},{label:e(()=>[a("span",yt,[t(i,null,{default:e(()=>[t(p(x))]),_:1}),l[15]||(l[15]=n(" TDK调用模板 ",-1))])]),default:e(()=>[t(r,{class:"toolbar-card"},{default:e(()=>[a("div",ht,[a("div",wt,[t(u,{type:"primary",onClick:A,class:"action-btn"},{default:e(()=>[t(i,null,{default:e(()=>[t(p(E))]),_:1}),l[16]||(l[16]=n(" 新增模板 ",-1))]),_:1,__:[16]})]),a("div",kt,[t(g,{modelValue:h.value,"onUpdate:modelValue":l[1]||(l[1]=s=>h.value=s),placeholder:"搜索模板名称",class:"search-input",clearable:""},{prefix:e(()=>[t(i,null,{default:e(()=>[t(p(V))]),_:1})]),_:1},8,["modelValue"])])])]),_:1}),t(r,{class:"table-card"},{default:e(()=>[t(C,{data:k.value,style:{width:"100%"},class:"enhanced-table"},{default:e(()=>[t(o,{prop:"name",label:"模板名称","min-width":"150"}),t(o,{prop:"type",label:"类型",width:"120"},{default:e(({row:s})=>[t(b,{type:s.type==="关键词"?"primary":"warning",size:"small"},{default:e(()=>[n(m(s.type),1)]),_:2},1032,["type"])]),_:1}),t(o,{prop:"template",label:"模板内容","min-width":"200","show-overflow-tooltip":""}),t(o,{prop:"keywords",label:"关键词",width:"120"}),t(o,{prop:"content",label:"内容","min-width":"150","show-overflow-tooltip":""}),t(o,{prop:"url",label:"URL","min-width":"150","show-overflow-tooltip":""}),t(o,{label:"操作",width:"150",fixed:"right"},{default:e(({row:s})=>[t(u,{type:"primary",size:"small",onClick:f=>N(s)},{default:e(()=>l[17]||(l[17]=[n(" 编辑 ",-1)])),_:2,__:[17]},1032,["onClick"]),t(u,{type:"danger",size:"small",onClick:f=>S(s)},{default:e(()=>l[18]||(l[18]=[n(" 删除 ",-1)])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})])])}}},St=M(gt,[["__scopeId","data-v-d4bfc8b4"]]);export{St as default};
