import{_ as R}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                    *//* empty css               *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                 */import{E as g,c as I,s as N,J as O,h as F,A as j,H as A,I as G,y as H,z as J,G as D,X as K,a5 as Q,a3 as X,_ as T,S as $,ad as q}from"./elementPlus-05e8f3ef.js";import{r as u,y as h,z as y,A as e,Q as a,I as t,P as W,a4 as Y,L as Z,u as r,C as ee,O as d,M as x}from"./vendor-ad470fc0.js";const se={class:"spider-simulator page-container"},ae={class:"breadcrumb page-header"},te={class:"simulator-container page-content"},le={class:"spider-list enhanced-card"},oe={class:"spider-list-header"},ne=["onClick"],ie={class:"spider-item-text"},de={class:"operation-area"},re={class:"input-section enhanced-card"},ue={class:"input-header"},ce={class:"controls"},pe={key:0,class:"result-area enhanced-card fade-in-up"},_e={class:"result-header"},me={class:"result-title"},ve={class:"result-status"},fe={class:"result-content"},be={class:"response-info"},ge={class:"info-item"},he={class:"info-value"},ye={class:"info-item"},Ve={class:"info-value"},Ee={class:"info-item"},Te={class:"info-value"},xe={class:"info-item"},Se={class:"info-value"},ke={class:"headers-content"},we={class:"page-content-area"},Ce={__name:"spider-simulator",setup(ze){const m=u(!1),v=u("360pc"),p=u(""),V=u("standard"),E=u("response"),o=u(null),S=[{label:"360PC蜘蛛模拟",value:"360pc"},{label:"360移动蜘蛛模拟",value:"360mobile"},{label:"百度PC蜘蛛模拟",value:"baidupc"},{label:"百度移动蜘蛛模拟",value:"baidumobile"},{label:"搜狗PC蜘蛛模拟",value:"sogoupc"},{label:"搜狗移动蜘蛛模拟",value:"sogoumobile"},{label:"神马移动蜘蛛",value:"shenma"},{label:"正常访问",value:"normal"}],k=l=>{v.value=l},w=async()=>{if(!p.value.trim()){g.warning("请输入要抓取的URL");return}m.value=!0;try{const l=await fetch("/api/tools/spider-simulator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:p.value,spider:v.value})});if(l.ok)o.value=await l.json(),g.success("蜘蛛模拟完成");else throw new Error("模拟失败")}catch{g.error("模拟抓取失败，请检查URL是否正确")}finally{m.value=!1}},C=l=>l>=200&&l<300?"success":l>=300&&l<400?"warning":l>=400?"danger":"info",z=l=>{if(!l)return"0 B";const s=1024,i=["B","KB","MB","GB"],c=Math.floor(Math.log(l)/Math.log(s));return parseFloat((l/Math.pow(s,c)).toFixed(2))+" "+i[c]};return(l,s)=>{const i=I,c=N,f=G,B=O,U=F,M=j,_=H,L=J,b=D,P=A;return h(),y("div",se,[e("div",ae,[a(i,{class:"breadcrumb-icon"},{default:t(()=>[a(r(K))]),_:1}),s[4]||(s[4]=e("span",{class:"breadcrumb-text"},"辅助工具",-1)),s[5]||(s[5]=e("span",{class:"breadcrumb-separator"},">",-1)),s[6]||(s[6]=e("span",{class:"breadcrumb-current"},"在线蜘蛛模拟",-1))]),e("div",te,[e("div",le,[e("div",oe,[a(i,{class:"spider-list-icon"},{default:t(()=>[a(r(Q))]),_:1}),s[7]||(s[7]=e("span",{class:"spider-list-title"},"蜘蛛类型",-1))]),(h(),y(W,null,Y(S,n=>e("div",{key:n.value,class:ee(["spider-item",{active:v.value===n.value}]),onClick:Be=>k(n.value)},[a(i,{class:"spider-item-icon"},{default:t(()=>[a(r(X))]),_:1}),e("span",ie,d(n.label),1)],10,ne)),64))]),e("div",de,[e("div",re,[e("div",ue,[a(i,{class:"input-icon"},{default:t(()=>[a(r(T))]),_:1}),s[8]||(s[8]=e("span",{class:"input-title"},"URL 配置",-1))]),a(c,{modelValue:p.value,"onUpdate:modelValue":s[0]||(s[0]=n=>p.value=n),placeholder:"请输入待抓取的URL...",class:"url-input input-enhanced",size:"large"},{prefix:t(()=>[a(i,null,{default:t(()=>[a(r(T))]),_:1})]),_:1},8,["modelValue"]),e("div",ce,[a(B,{modelValue:V.value,"onUpdate:modelValue":s[1]||(s[1]=n=>V.value=n),placeholder:"蜘蛛模式",class:"mode-select input-enhanced",size:"large"},{default:t(()=>[a(f,{label:"标准模式",value:"standard"}),a(f,{label:"快速模式",value:"fast"}),a(f,{label:"详细模式",value:"detailed"})]),_:1},8,["modelValue"]),a(U,{onClick:w,loading:m.value,class:"simulate-btn btn-gradient",size:"large"},{default:t(()=>[a(i,null,{default:t(()=>[a(r($))]),_:1}),s[9]||(s[9]=x(" 模拟抓取 ",-1))]),_:1,__:[9]},8,["loading"])])]),o.value?(h(),y("div",pe,[e("div",_e,[e("div",me,[a(i,{class:"result-icon"},{default:t(()=>[a(r(q))]),_:1}),s[10]||(s[10]=e("h3",null,"抓取结果",-1))]),e("div",ve,[a(M,{type:C(o.value.statusCode),size:"large",class:"status-tag"},{default:t(()=>[x(d(o.value.statusCode)+" "+d(o.value.statusText),1)]),_:1},8,["type"])])]),e("div",fe,[a(P,{modelValue:E.value,"onUpdate:modelValue":s[3]||(s[3]=n=>E.value=n),class:"result-tabs"},{default:t(()=>[a(b,{label:"响应信息",name:"response"},{default:t(()=>[e("div",be,[a(L,{gutter:20},{default:t(()=>[a(_,{span:12},{default:t(()=>[e("div",ge,[s[11]||(s[11]=e("span",{class:"info-label"},"响应时间:",-1)),e("span",he,d(o.value.responseTime)+"ms",1)])]),_:1}),a(_,{span:12},{default:t(()=>[e("div",ye,[s[12]||(s[12]=e("span",{class:"info-label"},"内容大小:",-1)),e("span",Ve,d(z(o.value.contentLength)),1)])]),_:1}),a(_,{span:12},{default:t(()=>[e("div",Ee,[s[13]||(s[13]=e("span",{class:"info-label"},"内容类型:",-1)),e("span",Te,d(o.value.contentType),1)])]),_:1}),a(_,{span:12},{default:t(()=>[e("div",xe,[s[14]||(s[14]=e("span",{class:"info-label"},"服务器:",-1)),e("span",Se,d(o.value.server),1)])]),_:1})]),_:1})])]),_:1}),a(b,{label:"HTTP头",name:"headers"},{default:t(()=>[e("div",ke,[e("pre",null,d(o.value.headers),1)])]),_:1}),a(b,{label:"页面内容",name:"content"},{default:t(()=>[e("div",we,[a(c,{modelValue:o.value.content,"onUpdate:modelValue":s[2]||(s[2]=n=>o.value.content=n),type:"textarea",rows:20,readonly:"",class:"content-textarea"},null,8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])])])):Z("",!0)])])])}}},Ae=R(Ce,[["__scopeId","data-v-58accc2c"]]);export{Ae as default};
