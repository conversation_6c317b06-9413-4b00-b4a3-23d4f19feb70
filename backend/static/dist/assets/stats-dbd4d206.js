import{_ as U}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css               *//* empty css                *//* empty css                  */import{u as W,i as j,a as q,b as G,c as K,d as X,e as Y,f as Z,H as T}from"./charts-d5c9e496.js";import{s as C}from"./spider-fa0c41a6.js";import{c as tt,h as st,x as at,y as et,z as ot,I as lt,J as nt,A as it,B as dt,C as rt}from"./elementPlus-05e8f3ef.js";import{r as c,c as F,k as ct,y as ut,z as _t,A as a,Q as s,I as e,M as v,O as i,C as pt,u as V,D as vt,al as f}from"./vendor-ad470fc0.js";import"./index-24913185.js";const ft={class:"page-container"},mt={class:"page-header"},yt={class:"header-actions"},ht={class:"page-content"},gt={class:"stats-content"},bt={class:"stats-icon"},wt={class:"stats-info"},Ct={class:"stats-number"},xt={class:"stats-content"},St={class:"stats-icon"},Et={class:"stats-info"},$t={class:"stats-number"},Dt={class:"stats-change"},kt={class:"stats-content"},At={class:"stats-icon"},Tt={class:"stats-info"},Ft={class:"stats-number"},Vt={class:"stats-content"},zt={class:"stats-icon"},Bt={class:"stats-info"},It={class:"stats-number"},Mt={class:"chart-header"},Ot={class:"chart-container"},Lt={class:"chart-container"},Nt={class:"trend-bar"},Rt={__name:"stats",setup(Ht){W([j,q,G,K,X,Y,Z]);const z=c(!1),n=c({}),_=c([]),B=c([]),y=c(7),x=F(()=>_.value.reduce((o,t)=>o+t.count,0)),I=F(()=>_.value.filter(o=>o.count>0).length),h=c({tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"访问量",type:"line",smooth:!0,data:[],itemStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}}]}),S=c({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"蜘蛛访问",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),M=o=>({1:"primary",2:"success",3:"warning",4:"info",5:"danger"})[o]||"info",O=()=>{const o=n.value.today_count||0,t=n.value.yesterday_count||0,p=o-t;return p>0?`+${p}`:`${p}`},L=()=>{E(),$(),g()},E=async()=>{try{const o=await C.getTodayStats();o.code===200&&(n.value=o.data)}catch(o){console.error("Failed to fetch today stats:",o)}},$=async()=>{try{const o=await C.getStats();o.code===200&&(_.value=o.data,S.value.series[0].data=o.data.map(t=>({name:t.name,value:t.count})))}catch(o){console.error("Failed to fetch spider stats:",o)}},g=async()=>{try{const o=await C.getChartData({days:y.value});o.code===200&&(B.value=o.data,h.value.xAxis.data=o.data.map(t=>t.date),h.value.series[0].data=o.data.map(t=>t.count))}catch(o){console.error("Failed to fetch chart data:",o)}};return ct(()=>{E(),$(),g()}),(o,t)=>{const p=f("Refresh"),d=tt,b=st,D=f("Calendar"),k=f("Clock"),r=at,u=et,N=f("DataAnalysis"),R=f("Monitor"),A=ot,w=lt,H=nt,J=it,m=dt,P=rt;return ut(),_t("div",ft,[a("div",mt,[t[6]||(t[6]=a("h2",null,"蜘蛛数据看板",-1)),a("div",yt,[s(b,{onClick:L,loading:z.value},{default:e(()=>[s(d,null,{default:e(()=>[s(p)]),_:1}),t[3]||(t[3]=v(" 刷新数据 ",-1))]),_:1,__:[3]},8,["loading"]),s(b,{onClick:t[0]||(t[0]=l=>o.$router.push("/spider/today"))},{default:e(()=>[s(d,null,{default:e(()=>[s(D)]),_:1}),t[4]||(t[4]=v(" 今日统计 ",-1))]),_:1,__:[4]}),s(b,{onClick:t[1]||(t[1]=l=>o.$router.push("/spider/yesterday"))},{default:e(()=>[s(d,null,{default:e(()=>[s(k)]),_:1}),t[5]||(t[5]=v(" 昨日统计 ",-1))]),_:1,__:[5]})])]),a("div",ht,[s(A,{gutter:20,class:"stats-row"},{default:e(()=>[s(u,{span:6},{default:e(()=>[s(r,{class:"stats-card today"},{default:e(()=>{var l;return[a("div",gt,[a("div",bt,[s(d,null,{default:e(()=>[s(D)]),_:1})]),a("div",wt,[a("div",Ct,i(n.value.today_count||0),1),t[7]||(t[7]=a("div",{class:"stats-label"},"今日访问",-1)),a("div",{class:pt(["stats-growth",{positive:n.value.growth_rate>0,negative:n.value.growth_rate<0}])},i(n.value.growth_rate>0?"+":"")+i(((l=n.value.growth_rate)==null?void 0:l.toFixed(1))||0)+"% ",3)])])]}),_:1})]),_:1}),s(u,{span:6},{default:e(()=>[s(r,{class:"stats-card yesterday"},{default:e(()=>[a("div",xt,[a("div",St,[s(d,null,{default:e(()=>[s(k)]),_:1})]),a("div",Et,[a("div",$t,i(n.value.yesterday_count||0),1),t[8]||(t[8]=a("div",{class:"stats-label"},"昨日访问",-1)),a("div",Dt," 对比前日: "+i(O()),1)])])]),_:1})]),_:1}),s(u,{span:6},{default:e(()=>[s(r,{class:"stats-card total"},{default:e(()=>[a("div",kt,[a("div",At,[s(d,null,{default:e(()=>[s(N)]),_:1})]),a("div",Tt,[a("div",Ft,i(x.value),1),t[9]||(t[9]=a("div",{class:"stats-label"},"总访问量",-1)),t[10]||(t[10]=a("div",{class:"stats-period"},"本月累计",-1))])])]),_:1})]),_:1}),s(u,{span:6},{default:e(()=>[s(r,{class:"stats-card active"},{default:e(()=>[a("div",Vt,[a("div",zt,[s(d,null,{default:e(()=>[s(R)]),_:1})]),a("div",Bt,[a("div",It,i(I.value),1),t[11]||(t[11]=a("div",{class:"stats-label"},"活跃蜘蛛",-1)),t[12]||(t[12]=a("div",{class:"stats-period"},"过去24小时",-1))])])]),_:1})]),_:1})]),_:1}),s(A,{gutter:20,class:"charts-row"},{default:e(()=>[s(u,{span:16},{default:e(()=>[s(r,null,{header:e(()=>[a("div",Mt,[t[13]||(t[13]=a("span",null,"访问趋势",-1)),s(H,{modelValue:y.value,"onUpdate:modelValue":t[2]||(t[2]=l=>y.value=l),onChange:g,style:{width:"120px"}},{default:e(()=>[s(w,{label:"最近7天",value:7}),s(w,{label:"最近15天",value:15}),s(w,{label:"最近30天",value:30})]),_:1},8,["modelValue"])])]),default:e(()=>[a("div",Ot,[s(V(T),{option:h.value,style:{height:"350px"}},null,8,["option"])])]),_:1})]),_:1}),s(u,{span:8},{default:e(()=>[s(r,null,{header:e(()=>t[14]||(t[14]=[a("span",null,"蜘蛛类型分布",-1)])),default:e(()=>[a("div",Lt,[s(V(T),{option:S.value,style:{height:"350px"}},null,8,["option"])])]),_:1})]),_:1})]),_:1}),s(r,null,{header:e(()=>t[15]||(t[15]=[a("span",null,"蜘蛛类型详细统计",-1)])),default:e(()=>[s(P,{data:_.value,style:{width:"100%"}},{default:e(()=>[s(m,{prop:"name",label:"蜘蛛名称",width:"120"},{default:e(({row:l})=>[s(J,{type:M(l.spider_type)},{default:e(()=>[v(i(l.name),1)]),_:2},1032,["type"])]),_:1}),s(m,{prop:"count",label:"访问次数",width:"120"}),s(m,{label:"占比",width:"120"},{default:e(({row:l})=>[v(i((l.count/x.value*100).toFixed(1))+"% ",1)]),_:1}),s(m,{label:"访问趋势"},{default:e(({row:l})=>[a("div",Nt,[a("div",{class:"trend-fill",style:vt({width:l.count/Math.max(..._.value.map(Q=>Q.count))*100+"%"})},null,4)])]),_:1})]),_:1},8,["data"])]),_:1})])])}}},es=U(Rt,[["__scopeId","data-v-55d8ca14"]]);export{es as default};
