import{_ as dt}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css               *//* empty css                *//* empty css                  */import{u as ct,i as it,b as ut,g as _t,a as pt,c as yt,d as ft,e as mt,f as vt,H as A}from"./charts-5a62b0c7.js";import{s as T}from"./spider-e2082f6b.js";import{w as N,E as V,c as ht,h as gt,x as bt,y as Ct,z as xt,A as wt,B as Dt,C as kt,K as At}from"./elementPlus-ef333120.js";import{r as f,c as E,k as Tt,al as u,y as _,z as Et,A as o,Q as t,I as a,M as m,O as l,u as C,C as M,H as v,J as Mt}from"./vendor-ad470fc0.js";import"./index-bb3d8812.js";const St={class:"page-container"},Yt={class:"page-header"},$t={class:"header-actions"},Bt={class:"page-content"},Ft={class:"stats-content"},zt={class:"stats-icon"},Ot={class:"stats-info"},Pt={class:"stats-number"},Rt={class:"stats-date"},Ht={class:"stats-content"},It={class:"stats-icon"},Lt={class:"stats-info"},Nt={class:"stats-number"},Vt={class:"stats-date"},Ut={class:"stats-content"},Wt={class:"stats-icon"},jt={class:"stats-info"},qt={class:"stats-trend"},Jt={class:"chart-container"},Kt={class:"chart-container"},Qt={class:"table-header"},Gt={class:"table-actions"},Xt={class:"count-number"},Zt={class:"count-number"},te={class:"trend-indicator"},ee={class:"chart-container"},ae={__name:"yesterday",setup(se){ct([it,ut,_t,pt,yt,ft,mt,vt]);const x=f(!1),h=f({}),d=f([]),U=f([]),W=N(),S=N().subtract(1,"day"),c=E(()=>{const e=h.value.today_count||0,s=h.value.yesterday_count||0;return s===0?0:(e-s)/s*100}),Y=E(()=>d.value.reduce((e,s)=>e+(s.yesterday_count||0),0)),$=E(()=>d.value.reduce((e,s)=>e+(s.today_count||0),0)),B=f({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"昨日访问",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),w=f({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["昨日","今日"]},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"昨日",type:"bar",data:[],itemStyle:{color:"#91cc75"}},{name:"今日",type:"bar",data:[],itemStyle:{color:"#5470c6"}}]}),D=f({tooltip:{trigger:"axis"},xAxis:{type:"category",data:Array.from({length:24},(e,s)=>`${s}:00`)},yAxis:{type:"value"},series:[{name:"访问量",type:"line",data:[],smooth:!0,areaStyle:{opacity:.3}}]}),F=e=>e.format("YYYY-MM-DD"),j=()=>c.value>0?"上升":c.value<0?"下降":"持平",q=e=>({1:"primary",2:"success",3:"warning",4:"info",5:"danger",6:"",7:"success",8:"warning",9:"info"})[e]||"info",g=e=>(e.today_count||0)-(e.yesterday_count||0),p=e=>{const s=e.yesterday_count||0;return s===0?0:((e.today_count||0)-s)/s*100},J=e=>Y.value>0?((e.yesterday_count||0)/Y.value*100).toFixed(1):0,K=e=>$.value>0?((e.today_count||0)/$.value*100).toFixed(1):0,z=()=>{X(),Z(),tt()},Q=()=>{V.info("跳转到今日统计页面")},G=()=>{V.info("导出对比数据功能开发中...")},X=async()=>{try{const e=await T.getTodayStats();e.code===200&&(h.value=e.data)}catch(e){console.error("Failed to fetch yesterday stats:",e)}},Z=async()=>{x.value=!0;try{const e=await T.getComparisonStats();e.code===200&&(d.value=e.data,O())}catch(e){console.error("Failed to fetch comparison data:",e),d.value=[{spider_type:1,spider_name:"百度",yesterday_count:1200,today_count:1350},{spider_type:5,spider_name:"谷歌",yesterday_count:800,today_count:750},{spider_type:2,spider_name:"搜狗",yesterday_count:600,today_count:680},{spider_type:3,spider_name:"360",yesterday_count:400,today_count:420},{spider_type:4,spider_name:"必应",yesterday_count:300,today_count:280}],O()}finally{x.value=!1}},tt=async()=>{try{const e=await T.getHourlyStats({date:S.format("YYYY-MM-DD")});e.code===200&&(U.value=e.data,D.value.series[0].data=e.data)}catch(e){console.error("Failed to fetch hourly data:",e);const s=Array.from({length:24},()=>Math.floor(Math.random()*100));D.value.series[0].data=s}},O=()=>{B.value.series[0].data=d.value.map(e=>({name:e.spider_name,value:e.yesterday_count||0})),w.value.xAxis.data=d.value.map(e=>e.spider_name),w.value.series[0].data=d.value.map(e=>e.yesterday_count||0),w.value.series[1].data=d.value.map(e=>e.today_count||0)};return Tt(()=>{z()}),(e,s)=>{const et=u("Refresh"),r=ht,k=gt,P=u("TrendCharts"),at=u("Calendar"),y=bt,b=Ct,st=u("Clock"),R=u("ArrowUp"),H=u("ArrowDown"),I=u("Minus"),L=xt,ot=u("Download"),nt=wt,i=Dt,lt=kt,rt=At;return _(),Et("div",St,[o("div",Yt,[s[2]||(s[2]=o("h2",null,"昨日蜘蛛统计",-1)),o("div",$t,[t(k,{onClick:z,loading:x.value},{default:a(()=>[t(r,null,{default:a(()=>[t(et)]),_:1}),s[0]||(s[0]=m(" 刷新数据 ",-1))]),_:1,__:[0]},8,["loading"]),t(k,{onClick:Q},{default:a(()=>[t(r,null,{default:a(()=>[t(P)]),_:1}),s[1]||(s[1]=m(" 对比今日 ",-1))]),_:1,__:[1]})])]),o("div",Bt,[t(L,{gutter:20,class:"stats-cards"},{default:a(()=>[t(b,{span:8},{default:a(()=>[t(y,{class:"stats-card yesterday"},{default:a(()=>[o("div",Ft,[o("div",zt,[t(r,null,{default:a(()=>[t(at)]),_:1})]),o("div",Ot,[o("div",Pt,l(h.value.yesterday_count||0),1),s[3]||(s[3]=o("div",{class:"stats-label"},"昨日总访问",-1)),o("div",Rt,l(F(C(S))),1)])])]),_:1})]),_:1}),t(b,{span:8},{default:a(()=>[t(y,{class:"stats-card today"},{default:a(()=>[o("div",Ht,[o("div",It,[t(r,null,{default:a(()=>[t(st)]),_:1})]),o("div",Lt,[o("div",Nt,l(h.value.today_count||0),1),s[4]||(s[4]=o("div",{class:"stats-label"},"今日总访问",-1)),o("div",Vt,l(F(C(W))),1)])])]),_:1})]),_:1}),t(b,{span:8},{default:a(()=>[t(y,{class:"stats-card comparison"},{default:a(()=>[o("div",Ut,[o("div",Wt,[t(r,null,{default:a(()=>[t(P)]),_:1})]),o("div",jt,[o("div",{class:M(["stats-number",{positive:c.value>0,negative:c.value<0}])},l(c.value>0?"+":"")+l(c.value.toFixed(1))+"% ",3),s[5]||(s[5]=o("div",{class:"stats-label"},"增长率",-1)),o("div",qt,[c.value>0?(_(),v(r,{key:0,class:"trend-up"},{default:a(()=>[t(R)]),_:1})):c.value<0?(_(),v(r,{key:1,class:"trend-down"},{default:a(()=>[t(H)]),_:1})):(_(),v(r,{key:2,class:"trend-equal"},{default:a(()=>[t(I)]),_:1})),m(" "+l(j()),1)])])])]),_:1})]),_:1})]),_:1}),t(L,{gutter:20,class:"charts-row"},{default:a(()=>[t(b,{span:12},{default:a(()=>[t(y,null,{header:a(()=>s[6]||(s[6]=[o("span",null,"昨日蜘蛛类型分布",-1)])),default:a(()=>[o("div",Jt,[t(C(A),{option:B.value,style:{height:"300px"}},null,8,["option"])])]),_:1})]),_:1}),t(b,{span:12},{default:a(()=>[t(y,null,{header:a(()=>s[7]||(s[7]=[o("span",null,"今日vs昨日对比",-1)])),default:a(()=>[o("div",Kt,[t(C(A),{option:w.value,style:{height:"300px"}},null,8,["option"])])]),_:1})]),_:1})]),_:1}),t(y,{class:"table-card"},{header:a(()=>[o("div",Qt,[s[9]||(s[9]=o("span",null,"蜘蛛类型详细对比",-1)),o("div",Gt,[t(k,{size:"small",onClick:G},{default:a(()=>[t(r,null,{default:a(()=>[t(ot)]),_:1}),s[8]||(s[8]=m(" 导出对比数据 ",-1))]),_:1,__:[8]})])])]),default:a(()=>[Mt((_(),v(lt,{data:d.value,style:{width:"100%"},"default-sort":{prop:"yesterday_count",order:"descending"}},{default:a(()=>[t(i,{prop:"spider_name",label:"蜘蛛类型",width:"120"},{default:a(({row:n})=>[t(nt,{type:q(n.spider_type)},{default:a(()=>[m(l(n.spider_name),1)]),_:2},1032,["type"])]),_:1}),t(i,{prop:"yesterday_count",label:"昨日访问",width:"120",sortable:""},{default:a(({row:n})=>[o("span",Xt,l(n.yesterday_count||0),1)]),_:1}),t(i,{prop:"today_count",label:"今日访问",width:"120",sortable:""},{default:a(({row:n})=>[o("span",Zt,l(n.today_count||0),1)]),_:1}),t(i,{label:"变化量",width:"120",sortable:""},{default:a(({row:n})=>[o("span",{class:M({"change-positive":g(n)>0,"change-negative":g(n)<0,"change-neutral":g(n)===0})},l(g(n)>0?"+":"")+l(g(n)),3)]),_:1}),t(i,{label:"变化率",width:"120",sortable:""},{default:a(({row:n})=>[o("span",{class:M({"change-positive":p(n)>0,"change-negative":p(n)<0,"change-neutral":p(n)===0})},l(p(n)>0?"+":"")+l(p(n).toFixed(1))+"% ",3)]),_:1}),t(i,{label:"昨日占比",width:"100"},{default:a(({row:n})=>[m(l(J(n))+"% ",1)]),_:1}),t(i,{label:"今日占比",width:"100"},{default:a(({row:n})=>[m(l(K(n))+"% ",1)]),_:1}),t(i,{label:"趋势",width:"100"},{default:a(({row:n})=>[o("div",te,[p(n)>5?(_(),v(r,{key:0,class:"trend-up"},{default:a(()=>[t(R)]),_:1})):p(n)<-5?(_(),v(r,{key:1,class:"trend-down"},{default:a(()=>[t(H)]),_:1})):(_(),v(r,{key:2,class:"trend-stable"},{default:a(()=>[t(I)]),_:1}))])]),_:1})]),_:1},8,["data"])),[[rt,x.value]])]),_:1}),t(y,{class:"hourly-card"},{header:a(()=>s[10]||(s[10]=[o("span",null,"昨日小时访问分布",-1)])),default:a(()=>[o("div",ee,[t(C(A),{option:D.value,style:{height:"250px"}},null,8,["option"])])]),_:1})])])}}},ge=dt(ae,[["__scopeId","data-v-09f5e0ae"]]);export{ge as default};
