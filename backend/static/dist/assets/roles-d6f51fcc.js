import{_ as C}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                  */import{x as E,E as _,c as g,h as k,B as x,A as B,C as h,O as T}from"./elementPlus-05e8f3ef.js";import{r as $,k as z,y as I,z as M,A as o,Q as e,I as a,u as N,M as i,O as V}from"./vendor-ad470fc0.js";const A={class:"roles-container"},D={class:"card-header"},O={__name:"roles",setup(w){const d=$([{id:1,name:"超级管理员",description:"拥有所有权限",status:"active"},{id:2,name:"管理员",description:"拥有大部分权限",status:"active"},{id:3,name:"普通用户",description:"基础权限",status:"active"}]),c=()=>{_.info("创建角色功能开发中...")},p=n=>{_.info(`编辑角色: ${n.name}`)},u=n=>{_.info(`删除角色: ${n.name}`)};return z(()=>{}),(n,t)=>{const m=g,r=k,l=x,f=B,v=h,b=E;return I(),M("div",A,[t[4]||(t[4]=o("div",{class:"page-header"},[o("h2",null,"角色管理"),o("p",null,"管理系统角色和权限分配")],-1)),e(b,{class:"content-card"},{header:a(()=>[o("div",D,[t[1]||(t[1]=o("span",null,"角色列表",-1)),e(r,{type:"primary",onClick:c},{default:a(()=>[e(m,null,{default:a(()=>[e(N(T))]),_:1}),t[0]||(t[0]=i(" 新增角色 ",-1))]),_:1,__:[0]})])]),default:a(()=>[e(v,{data:d.value,style:{width:"100%"}},{default:a(()=>[e(l,{prop:"name",label:"角色名称"}),e(l,{prop:"description",label:"描述"}),e(l,{prop:"status",label:"状态"},{default:a(({row:s})=>[e(f,{type:s.status==="active"?"success":"danger"},{default:a(()=>[i(V(s.status==="active"?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(l,{label:"操作",width:"200"},{default:a(({row:s})=>[e(r,{size:"small",onClick:y=>p(s)},{default:a(()=>t[2]||(t[2]=[i("编辑",-1)])),_:2,__:[2]},1032,["onClick"]),e(r,{size:"small",type:"danger",onClick:y=>u(s)},{default:a(()=>t[3]||(t[3]=[i("删除",-1)])),_:2,__:[3]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},P=C(O,[["__scopeId","data-v-e0d8c00b"]]);export{P as default};
