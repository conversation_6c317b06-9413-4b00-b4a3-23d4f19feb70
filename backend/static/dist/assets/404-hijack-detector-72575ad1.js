import{_ as $}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                    *//* empty css               *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                 */import{E as h,c as D,s as I,H,h as K,A as N,F as P,G as W,y as O,z as q,D as B,W as G}from"./elementPlus-ef333120.js";import{r as i,y as m,z as p,A as t,Q as a,I as n,P as j,a4 as F,L as C,u as Q,C as X,O as r,M as y}from"./vendor-ad470fc0.js";const Y={class:"referer-simulator"},J={class:"breadcrumb"},Z={class:"simulator-container"},ee={class:"search-engine-list"},te=["onClick"],se={class:"operation-area"},ae={class:"input-section"},ne={class:"controls"},le={key:0,class:"waiting-status"},oe={key:1,class:"result-area"},re={class:"result-header"},ie={class:"result-status"},ue={class:"result-content"},ce={class:"request-info"},de={class:"info-item"},me={class:"info-value"},pe={class:"info-item"},_e={class:"info-value"},ve={class:"info-item"},fe={class:"info-value"},ge={class:"info-item"},be={class:"info-value"},he={class:"headers-content"},we={class:"page-content-area"},ke={__name:"404-hijack-detector",setup(Ce){const u=i(!1),_=i("sogou"),c=i(""),v=i("desktop"),w=i("request"),l=i(null),T=[{label:"搜狗搜索",value:"sogou"},{label:"百度搜索",value:"baidu"},{label:"神马SM",value:"shenma"},{label:"360搜索",value:"360"}],E=s=>{_.value=s},M=async()=>{if(!c.value.trim()){h.warning("请输入要模拟的URL");return}u.value=!0;try{await new Promise(f=>setTimeout(f,2e3));const s=U(_.value),e=A(v.value);l.value={statusCode:200,statusText:"OK",targetUrl:c.value,referer:s.referer,userAgent:e.userAgent,responseTime:Math.floor(Math.random()*500)+100,headers:`HTTP/1.1 200 OK
Server: nginx/1.18.0
Date: ${new Date().toUTCString()}
Content-Type: text/html; charset=utf-8
Content-Length: ${Math.floor(Math.random()*5e4)+1e4}
Connection: keep-alive
Cache-Control: max-age=3600
Referer: ${s.referer}
User-Agent: ${e.userAgent}`,content:`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referer模拟访问 - ${s.name}</title>
    <meta name="description" content="通过${s.name}的Referer模拟访问页面">
</head>
<body>
    <h1>Referer模拟访问成功</h1>
    <p>来源搜索引擎: ${s.name}</p>
    <p>设备类型: ${e.name}</p>
    <p>访问时间: ${new Date().toLocaleString()}</p>
    <p>Referer: ${s.referer}</p>
    <div class="content">
        <h2>页面内容</h2>
        <p>这里是通过${s.name}Referer模拟访问的页面内容...</p>
    </div>
</body>
</html>`},h.success(`${s.name}Referer模拟完成`)}catch{h.error("Referer模拟失败，请检查URL是否正确")}finally{u.value=!1}},U=s=>{const e={sogou:{name:"搜狗搜索",referer:"https://www.sogou.com/web?query=test"},baidu:{name:"百度搜索",referer:"https://www.baidu.com/s?wd=test"},shenma:{name:"神马SM",referer:"https://m.sm.cn/s?q=test"},360:{name:"360搜索",referer:"https://www.so.com/s?q=test"}};return e[s]||e.sogou},A=s=>{const e={desktop:{name:"桌面端",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"},mobile:{name:"移动端",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"},uc:{name:"UC浏览器",userAgent:"Mozilla/5.0 (Linux; U; Android 8.1.0; zh-CN; EML-AL00 Build/HUAWEIEML-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/11.9.4.974 UWS/********* Mobile Safari/537.36 AliApp(DingTalk/4.5.11) com.alibaba.android.rimet/10487439 Channel/227200 language/zh-CN"}};return e[s]||e.desktop},V=s=>s>=200&&s<300?"success":s>=300&&s<400?"warning":s>=400?"danger":"info";return(s,e)=>{const f=D,k=I,g=W,R=H,x=K,L=N,d=O,S=q,b=B,z=P;return m(),p("div",Y,[t("div",J,[a(f,{class:"breadcrumb-icon"},{default:n(()=>[a(Q(G))]),_:1}),e[4]||(e[4]=t("span",{class:"breadcrumb-text"},"辅助工具",-1)),e[5]||(e[5]=t("span",{class:"breadcrumb-separator"},">",-1)),e[6]||(e[6]=t("span",{class:"breadcrumb-current"},"Referer模拟",-1))]),t("div",Z,[t("div",ee,[(m(),p(j,null,F(T,o=>t("div",{key:o.value,class:X(["engine-item",{active:_.value===o.value}]),onClick:ye=>E(o.value)},r(o.label),11,te)),64))]),t("div",se,[t("div",ae,[a(k,{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=o=>c.value=o),placeholder:"Referer 模拟请求地址...",class:"url-input",size:"large"},null,8,["modelValue"]),t("div",ne,[a(R,{modelValue:v.value,"onUpdate:modelValue":e[1]||(e[1]=o=>v.value=o),class:"device-select",size:"large"},{default:n(()=>[a(g,{label:"Desktop",value:"desktop"}),a(g,{label:"Mobile",value:"mobile"}),a(g,{label:"UC浏览器",value:"uc"})]),_:1},8,["modelValue"]),a(x,{type:"primary",onClick:M,loading:u.value,class:"simulate-btn",size:"large"},{default:n(()=>e[7]||(e[7]=[y(" 转到一下 ",-1)])),_:1,__:[7]},8,["loading"])])]),u.value?(m(),p("div",le," 等待模拟... ")):C("",!0),l.value?(m(),p("div",oe,[t("div",re,[e[8]||(e[8]=t("h3",null,"模拟结果",-1)),t("div",ie,[a(L,{type:V(l.value.statusCode),size:"large"},{default:n(()=>[y(r(l.value.statusCode)+" "+r(l.value.statusText),1)]),_:1},8,["type"])])]),t("div",ue,[a(z,{modelValue:w.value,"onUpdate:modelValue":e[3]||(e[3]=o=>w.value=o),class:"result-tabs"},{default:n(()=>[a(b,{label:"请求信息",name:"request"},{default:n(()=>[t("div",ce,[a(S,{gutter:20},{default:n(()=>[a(d,{span:12},{default:n(()=>[t("div",de,[e[9]||(e[9]=t("span",{class:"info-label"},"目标URL:",-1)),t("span",me,r(l.value.targetUrl),1)])]),_:1}),a(d,{span:12},{default:n(()=>[t("div",pe,[e[10]||(e[10]=t("span",{class:"info-label"},"Referer:",-1)),t("span",_e,r(l.value.referer),1)])]),_:1}),a(d,{span:12},{default:n(()=>[t("div",ve,[e[11]||(e[11]=t("span",{class:"info-label"},"User-Agent:",-1)),t("span",fe,r(l.value.userAgent),1)])]),_:1}),a(d,{span:12},{default:n(()=>[t("div",ge,[e[12]||(e[12]=t("span",{class:"info-label"},"响应时间:",-1)),t("span",be,r(l.value.responseTime)+"ms",1)])]),_:1})]),_:1})])]),_:1}),a(b,{label:"响应头",name:"headers"},{default:n(()=>[t("div",he,[t("pre",null,r(l.value.headers),1)])]),_:1}),a(b,{label:"页面内容",name:"content"},{default:n(()=>[t("div",we,[a(k,{modelValue:l.value.content,"onUpdate:modelValue":e[2]||(e[2]=o=>l.value.content=o),type:"textarea",rows:20,readonly:"",class:"content-textarea"},null,8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])])])):C("",!0)])])])}}},ze=$(ke,[["__scopeId","data-v-5de269f2"]]);export{ze as default};
