import{_ as U}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                *//* empty css                     *//* empty css               *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                  */import{aB as C,aC as S,r as E,c as O,X as B,k as L,y as V,z as R,A as l,Q as e,I as t,u as d,O as P,M as g,H as N,L as G}from"./vendor-ad470fc0.js";import{a as h}from"./api-c21033af.js";import{E as D,c as $,h as q,s as F,u as M,y as j,G as H,H as J,z as Q,v as X,x as W,_ as Z,X as K,W as Y,L as ee,Z as ae,P as te,Q as le}from"./elementPlus-ef333120.js";import"./index-bb3d8812.js";const oe={class:"api-form-page"},se={class:"page-header"},re={class:"header-content"},ne={class:"header-left"},de={class:"header-text"},ie={class:"header-actions"},ue={class:"page-content"},pe={class:"form-container"},me={class:"card-header"},ce={class:"form-actions"},_e={__name:"form",setup(fe){const _=C(),w=S(),f=E(),m=E(!1),u=O(()=>!!_.params.id),o=B({name:"",method:"GET",url:"",headers:"",params:"",description:""}),y={name:[{required:!0,message:"请输入API名称",trigger:"blur"}],method:[{required:!0,message:"请选择请求方法",trigger:"change"}],url:[{required:!0,message:"请输入API URL",trigger:"blur"}]},I=async()=>{if(f.value)try{await f.value.validate(),m.value=!0;let s;u.value?s=await h.update(_.params.id,o):s=await h.create(o),s.code===200&&(D.success(u.value?"更新成功":"创建成功"),w.push("/api"))}catch(s){console.error("Submit failed:",s)}finally{m.value=!1}},k=async()=>{if(u.value)try{const s=await h.get(_.params.id);s.code===200&&Object.assign(o,s.data)}catch(s){console.error("Failed to fetch API:",s)}};return L(()=>{k()}),(s,a)=>{const n=$,v=q,p=F,i=M,b=j,c=H,x=J,A=Q,T=X,z=W;return V(),R("div",oe,[l("div",se,[l("div",re,[l("div",ne,[e(n,{class:"header-icon"},{default:t(()=>[e(d(Z))]),_:1}),l("div",de,[l("h1",null,P(u.value?"编辑API接口":"创建API接口"),1),a[8]||(a[8]=l("p",null,"配置和管理API接口，实现数据交互",-1))])]),l("div",ie,[e(v,{onClick:a[0]||(a[0]=r=>s.$router.back()),class:"back-btn"},{default:t(()=>[e(n,null,{default:t(()=>[e(d(K))]),_:1}),a[9]||(a[9]=g(" 返回列表 ",-1))]),_:1,__:[9]})])])]),l("div",ue,[l("div",pe,[e(z,{class:"form-card",shadow:"hover"},{header:t(()=>[l("div",me,[e(n,{class:"card-icon"},{default:t(()=>[e(d(Y))]),_:1}),a[10]||(a[10]=l("span",{class:"card-title"},"接口配置",-1))])]),default:t(()=>[e(T,{ref_key:"formRef",ref:f,model:o,rules:y,"label-width":"120px",class:"api-form"},{default:t(()=>[e(A,{gutter:24},{default:t(()=>[e(b,{span:12},{default:t(()=>[e(i,{label:"API名称",prop:"name"},{default:t(()=>[e(p,{modelValue:o.name,"onUpdate:modelValue":a[1]||(a[1]=r=>o.name=r),placeholder:"请输入API名称",size:"large",clearable:""},{prefix:t(()=>[e(n,null,{default:t(()=>[e(d(ee))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:12},{default:t(()=>[e(i,{label:"请求方法",prop:"method"},{default:t(()=>[e(x,{modelValue:o.method,"onUpdate:modelValue":a[2]||(a[2]=r=>o.method=r),placeholder:"请选择请求方法",size:"large",clearable:""},{default:t(()=>[e(c,{label:"GET",value:"GET"},{default:t(()=>a[11]||(a[11]=[l("div",{class:"method-option get"},[l("span",{class:"method-tag"},"GET"),l("span",{class:"method-desc"},"获取数据")],-1)])),_:1,__:[11]}),e(c,{label:"POST",value:"POST"},{default:t(()=>a[12]||(a[12]=[l("div",{class:"method-option post"},[l("span",{class:"method-tag"},"POST"),l("span",{class:"method-desc"},"创建数据")],-1)])),_:1,__:[12]}),e(c,{label:"PUT",value:"PUT"},{default:t(()=>a[13]||(a[13]=[l("div",{class:"method-option put"},[l("span",{class:"method-tag"},"PUT"),l("span",{class:"method-desc"},"更新数据")],-1)])),_:1,__:[13]}),e(c,{label:"DELETE",value:"DELETE"},{default:t(()=>a[14]||(a[14]=[l("div",{class:"method-option delete"},[l("span",{class:"method-tag"},"DELETE"),l("span",{class:"method-desc"},"删除数据")],-1)])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{label:"API URL",prop:"url"},{default:t(()=>[e(p,{modelValue:o.url,"onUpdate:modelValue":a[3]||(a[3]=r=>o.url=r),placeholder:"https://api.example.com/v1/endpoint",size:"large",clearable:""},{prefix:t(()=>[e(n,null,{default:t(()=>[e(d(ae))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"请求头",prop:"headers"},{default:t(()=>[e(p,{modelValue:o.headers,"onUpdate:modelValue":a[4]||(a[4]=r=>o.headers=r),type:"textarea",rows:4,placeholder:`请输入请求头（JSON格式）
例如：
{
  'Content-Type': 'application/json',
  'Authorization': 'Bearer token'
}`,maxlength:"1000","show-word-limit":"",resize:"none"},null,8,["modelValue"])]),_:1}),e(i,{label:"请求参数",prop:"params"},{default:t(()=>[e(p,{modelValue:o.params,"onUpdate:modelValue":a[5]||(a[5]=r=>o.params=r),type:"textarea",rows:4,placeholder:`请输入请求参数（JSON格式）
例如：
{
  'page': 1,
  'limit': 10,
  'keyword': 'search'
}`,maxlength:"1000","show-word-limit":"",resize:"none"},null,8,["modelValue"])]),_:1}),e(i,{label:"描述",prop:"description"},{default:t(()=>[e(p,{modelValue:o.description,"onUpdate:modelValue":a[6]||(a[6]=r=>o.description=r),type:"textarea",rows:3,placeholder:"请输入API接口描述，说明接口的功能和用途",maxlength:"500","show-word-limit":"",resize:"none"},null,8,["modelValue"])]),_:1}),l("div",ce,[e(v,{type:"primary",onClick:I,loading:m.value,size:"large",class:"submit-btn"},{default:t(()=>[m.value?G("",!0):(V(),N(n,{key:0},{default:t(()=>[e(d(te))]),_:1})),g(" "+P(u.value?"更新API接口":"创建API接口"),1)]),_:1},8,["loading"]),e(v,{onClick:a[7]||(a[7]=r=>s.$router.back()),size:"large",class:"cancel-btn"},{default:t(()=>[e(n,null,{default:t(()=>[e(d(le))]),_:1}),a[15]||(a[15]=g(" 取消 ",-1))]),_:1,__:[15]})])]),_:1},8,["model"])]),_:1})])])])}}},Te=U(_e,[["__scopeId","data-v-4bc82024"]]);export{Te as default};
