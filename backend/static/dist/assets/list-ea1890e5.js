import{_ as q}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css               *//* empty css                */import{r as b,X as A,k as G,y as S,z as W,A as e,Q as s,I as a,u as i,O as d,M as _,a5 as Y,H as V,L as Z,J as tt}from"./vendor-ad470fc0.js";import{s as v}from"./index-bb3d8812.js";import{E as w,c as st,x as et,y as at,z as ot,h as lt,s as nt,B as rt,A as it,C as dt,J as ct,K as ut,a7 as C,M as _t,af as pt,S as O,a8 as ft,R}from"./elementPlus-ef333120.js";const x={getList(p){return v({url:"/user-agents",method:"get",params:p})},getRandom(p){return v({url:"/user-agents/random",method:"get",params:p})},getStats(){return v({url:"/user-agents/stats",method:"get"})},reload(){return v({url:"/user-agents/reload",method:"post"})},getByIndex(p){return v({url:`/user-agents/${p}`,method:"get"})}};const gt={class:"page-container"},mt={class:"page-header"},vt={class:"header-content"},ht={class:"header-left"},yt={class:"header-icon"},bt={class:"breadcrumb"},wt={class:"header-stats"},Ct={class:"stat-item"},xt={class:"stat-value"},kt={class:"stat-item"},Et={class:"stat-value"},St={class:"page-content"},zt={class:"stats-content"},Ut={class:"stats-icon total"},It={class:"stats-info"},Ft={class:"stats-number"},Bt={class:"stats-content"},Tt={class:"stats-icon chrome"},At={class:"stats-info"},Vt={class:"stats-number"},Ot={class:"stats-content"},Rt={class:"stats-icon firefox"},Mt={class:"stats-info"},Nt={class:"stats-number"},Dt={class:"stats-content"},Lt={class:"stats-icon ie"},$t={class:"stats-info"},Kt={class:"stats-number"},Pt={class:"table-toolbar"},Jt={class:"toolbar-left"},jt={class:"search-group"},Ht={class:"random-ua-content"},Qt={class:"ua-cell"},Xt={__name:"list",setup(p){const k=b(!1),z=b([]),u=b({}),h=b(""),f=A({keyword:""}),n=A({page:1,pageSize:20,total:0}),g=async()=>{k.value=!0;try{const o={page:n.page,page_size:n.pageSize,keyword:f.keyword},t=await x.getList(o);t.code===200&&(z.value=t.data.list,n.total=t.data.total)}catch(o){console.error("Failed to fetch user agents:",o)}finally{k.value=!1}},U=async()=>{try{const o=await x.getStats();o.code===200&&(u.value=o.data)}catch(o){console.error("Failed to fetch stats:",o)}},M=async()=>{try{const o=f.keyword?{keyword:f.keyword}:{},t=await x.getRandom(o);t.code===200&&(h.value=t.data.user_agent,w.success("获取随机User-Agent成功"))}catch(o){console.error("Failed to get random user agent:",o)}},N=async()=>{try{(await x.reload()).code===200&&(w.success("重新加载成功"),await U(),await g())}catch(o){console.error("Failed to reload user agents:",o)}},I=()=>{n.page=1,g()},D=()=>{f.keyword="",n.page=1,g()},L=o=>{n.pageSize=o,n.page=1,g()},$=o=>{n.page=o,g()},F=async o=>{try{await navigator.clipboard.writeText(o),w.success("复制成功")}catch{w.error("复制失败")}},B=o=>o.includes("Chrome")?"Chrome":o.includes("Firefox")?"Firefox":o.includes("Safari")&&!o.includes("Chrome")?"Safari":o.includes("Edge")?"Edge":o.includes("MSIE")||o.includes("Trident")?"IE":o.includes("Opera")?"Opera":"Other",K=o=>{const t=B(o);return{Chrome:"primary",Firefox:"warning",Safari:"success",Edge:"info",IE:"danger",Opera:"warning",Other:""}[t]||""};return G(()=>{U(),g()}),(o,t)=>{var T;const r=st,c=et,y=at,P=ot,m=lt,J=nt,E=rt,j=it,H=dt,Q=ct,X=ut;return S(),W("div",gt,[e("div",mt,[e("div",vt,[e("div",ht,[e("div",yt,[s(r,null,{default:a(()=>[s(i(C))]),_:1})]),e("div",null,[t[6]||(t[6]=e("h1",{class:"page-title"},"User-Agent 管理",-1)),e("div",bt,[t[4]||(t[4]=e("span",null,"系统管理",-1)),s(r,null,{default:a(()=>[s(i(_t))]),_:1}),t[5]||(t[5]=e("span",{class:"current"},"User-Agent 管理",-1))])])]),e("div",wt,[e("div",Ct,[e("div",xt,d(u.value.total||0),1),t[7]||(t[7]=e("div",{class:"stat-label"},"总数量",-1))]),e("div",kt,[e("div",Et,d(((T=u.value.browser_stats)==null?void 0:T.Chrome)||0),1),t[8]||(t[8]=e("div",{class:"stat-label"},"Chrome",-1))])])])]),e("div",St,[s(P,{gutter:20,class:"stats-row"},{default:a(()=>[s(y,{span:6},{default:a(()=>[s(c,{class:"stats-card"},{default:a(()=>[e("div",zt,[e("div",Ut,[s(r,null,{default:a(()=>[s(i(pt))]),_:1})]),e("div",It,[e("div",Ft,d(u.value.total||0),1),t[9]||(t[9]=e("div",{class:"stats-label"},"总数量",-1))])])]),_:1})]),_:1}),s(y,{span:6},{default:a(()=>[s(c,{class:"stats-card"},{default:a(()=>{var l;return[e("div",Bt,[e("div",Tt,[s(r,null,{default:a(()=>[s(i(C))]),_:1})]),e("div",At,[e("div",Vt,d(((l=u.value.browser_stats)==null?void 0:l.Chrome)||0),1),t[10]||(t[10]=e("div",{class:"stats-label"},"Chrome",-1))])])]}),_:1})]),_:1}),s(y,{span:6},{default:a(()=>[s(c,{class:"stats-card"},{default:a(()=>{var l;return[e("div",Ot,[e("div",Rt,[s(r,null,{default:a(()=>[s(i(C))]),_:1})]),e("div",Mt,[e("div",Nt,d(((l=u.value.browser_stats)==null?void 0:l.Firefox)||0),1),t[11]||(t[11]=e("div",{class:"stats-label"},"Firefox",-1))])])]}),_:1})]),_:1}),s(y,{span:6},{default:a(()=>[s(c,{class:"stats-card"},{default:a(()=>{var l;return[e("div",Dt,[e("div",Lt,[s(r,null,{default:a(()=>[s(i(C))]),_:1})]),e("div",$t,[e("div",Kt,d(((l=u.value.browser_stats)==null?void 0:l["Internet Explorer"])||0),1),t[12]||(t[12]=e("div",{class:"stats-label"},"IE",-1))])])]}),_:1})]),_:1})]),_:1}),s(c,{class:"toolbar-card"},{default:a(()=>[e("div",Pt,[e("div",Jt,[s(m,{type:"primary",class:"action-btn",onClick:M},{default:a(()=>[s(r,null,{default:a(()=>[s(i(O))]),_:1}),t[13]||(t[13]=_(" 获取随机UA ",-1))]),_:1,__:[13]}),s(m,{type:"success",class:"action-btn",onClick:N},{default:a(()=>[s(r,null,{default:a(()=>[s(i(ft))]),_:1}),t[14]||(t[14]=_(" 重新加载 ",-1))]),_:1,__:[14]})]),e("div",jt,[s(J,{modelValue:f.keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>f.keyword=l),placeholder:"搜索User-Agent",clearable:"",onKeyup:Y(I,["enter"])},{prefix:a(()=>[s(r,null,{default:a(()=>[s(i(R))]),_:1})]),_:1},8,["modelValue"]),s(m,{class:"search-btn",onClick:I},{default:a(()=>[s(r,null,{default:a(()=>[s(i(R))]),_:1}),t[15]||(t[15]=_(" 搜索 ",-1))]),_:1,__:[15]}),s(m,{class:"reset-btn",onClick:D},{default:a(()=>[s(r,null,{default:a(()=>[s(i(O))]),_:1}),t[16]||(t[16]=_(" 重置 ",-1))]),_:1,__:[16]})])])]),_:1}),h.value?(S(),V(c,{key:0,class:"random-ua-card"},{header:a(()=>[t[18]||(t[18]=e("span",null,"随机 User-Agent",-1)),s(m,{type:"text",style:{float:"right"},onClick:t[1]||(t[1]=l=>F(h.value))},{default:a(()=>t[17]||(t[17]=[_(" 复制 ",-1)])),_:1,__:[17]})]),default:a(()=>[e("div",Ht,d(h.value),1)]),_:1})):Z("",!0),s(c,{class:"table-card"},{default:a(()=>[tt((S(),V(H,{data:z.value,style:{width:"100%"},height:"400"},{default:a(()=>[s(E,{type:"index",label:"#",width:"60"}),s(E,{prop:"user_agent",label:"User-Agent","show-overflow-tooltip":""},{default:a(({row:l,$index:qt})=>[e("div",Qt,[e("span",null,d(l),1),s(m,{type:"text",size:"small",onClick:Gt=>F(l),style:{"margin-left":"10px"}},{default:a(()=>t[19]||(t[19]=[_(" 复制 ",-1)])),_:2,__:[19]},1032,["onClick"])])]),_:1}),s(E,{label:"浏览器",width:"120"},{default:a(({row:l})=>[s(j,{type:K(l)},{default:a(()=>[_(d(B(l)),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])),[[X,k.value]])]),_:1}),s(c,{class:"pagination-card"},{default:a(()=>[s(Q,{"current-page":n.page,"onUpdate:currentPage":t[2]||(t[2]=l=>n.page=l),"page-size":n.pageSize,"onUpdate:pageSize":t[3]||(t[3]=l=>n.pageSize=l),"page-sizes":[20,50,100,200],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:L,onCurrentChange:$},null,8,["current-page","page-size","total"])]),_:1})])])}}},ps=q(Xt,[["__scopeId","data-v-f247b34c"]]);export{ps as default};
