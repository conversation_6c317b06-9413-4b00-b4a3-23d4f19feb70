import{_ as te}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                */import{s as x}from"./spider-fa0c41a6.js";import{w as v,E as z,c as ae,h as le,x as oe,I as se,J as ne,s as de,a7 as re,A as ie,B as pe,C as _e,L as ce,W as ue,u as me,v as fe,a8 as ge,M as ye,a3 as ve,N as be,P as he,a4 as we,_ as Ce,S as Ye,T as De}from"./elementPlus-05e8f3ef.js";import{r as b,X as E,c as Ee,k as Ve,y as V,z as S,A as s,Q as e,I as o,u,O as p,P as ke,a4 as xe,M as r,J as ze,H as Se}from"./vendor-ad470fc0.js";import"./index-24913185.js";const Me={class:"page-container"},Te={class:"page-header"},Le={class:"header-content"},Ie={class:"header-left"},Ue={class:"header-icon"},Ne={class:"breadcrumb"},Pe={class:"header-stats"},Fe={class:"stat-item"},Be={class:"stat-value"},Ae={class:"stat-item"},He={class:"stat-value"},Re={class:"page-content"},$e={class:"filter-toolbar"},je={class:"filter-buttons"},Je={class:"table-toolbar"},Oe={class:"toolbar-left"},Ge={class:"search-group"},Qe={class:"pagination-container"},We={class:"form-tip"},Xe={class:"dialog-footer"},qe={__name:"logs",setup(Ke){const h=b(!1),w=b([]),C=b([]),g=b(!1),n=E({spider_type:"",domain:"",ip:"",spider_first:"",start_date:"",end_date:""}),M=[{type:"",name:"全部蜘蛛"},{type:1,name:"百度蜘蛛"},{type:2,name:"搜狗蜘蛛"},{type:3,name:"360蜘蛛"},{type:4,name:"必应蜘蛛"},{type:5,name:"谷歌蜘蛛"},{type:6,name:"神马蜘蛛"},{type:7,name:"Yandex蜘蛛"},{type:8,name:"Coccoc蜘蛛"},{type:9,name:"Naver蜘蛛"}],d=E({page:1,pageSize:20,total:0}),y=E({days:30}),T=Ee(()=>{const a=v().format("YYYY-MM-DD");return w.value.filter(t=>v(t.created_at).format("YYYY-MM-DD")===a).length}),L=a=>({1:"百度",2:"搜狗",3:"360",4:"必应",5:"谷歌",6:"神马",7:"Yandex",8:"Coccoc",9:"Naver"})[a]||"未知",I=a=>({1:"primary",2:"success",3:"warning",4:"info",5:"danger"})[a]||"info",U=a=>a>=200&&a<300?"success":a>=300&&a<400?"warning":a>=400?"danger":"info",N=a=>v(a).format("YYYY-MM-DD HH:mm:ss"),P=a=>{a&&a.length===2?(n.start_date=v(a[0]).format("YYYY-MM-DD"),n.end_date=v(a[1]).format("YYYY-MM-DD")):(n.start_date="",n.end_date="")},m=async()=>{h.value=!0;try{const a={page:d.page,page_size:d.pageSize,spider_type:n.spider_type,domain:n.domain,ip:n.ip,spider_first:n.spider_first,start_date:n.start_date,end_date:n.end_date},t=await x.getLogs(a);t.code===200&&(w.value=t.data.list,d.total=t.data.total)}catch(a){console.error("Failed to fetch spider logs:",a)}finally{h.value=!1}},k=()=>{d.page=1,m()},F=a=>{n.spider_type=a,k()},B=()=>{z.info("导出日志功能开发中...")},A=()=>{n.spider_type="",n.domain="",n.ip="",n.spider_first="",n.start_date="",n.end_date="",C.value=[],d.page=1,m()},H=a=>{d.pageSize=a,d.page=1,m()},R=a=>{d.page=a,m()},$=()=>{g.value=!0},j=async()=>{try{const a=await x.clearLogs({days:y.days});a.code===200&&(z.success(`成功清理了 ${a.data.deleted_count} 条日志`),g.value=!1,m())}catch(a){console.error("Failed to clear logs:",a)}};return Ve(()=>{m()}),(a,t)=>{const _=ae,c=le,Y=oe,f=se,J=ne,O=de,G=re,D=ie,i=pe,Q=_e,W=ce,X=ue,q=me,K=fe,Z=ge,ee=ye;return V(),S("div",Me,[s("div",Te,[s("div",Le,[s("div",Ie,[s("div",Ue,[e(_,null,{default:o(()=>[e(u(ve))]),_:1})]),s("div",null,[t[10]||(t[10]=s("h1",{class:"page-title"},"蜘蛛日志",-1)),s("div",Ne,[t[8]||(t[8]=s("span",null,"蜘蛛管理",-1)),e(_,null,{default:o(()=>[e(u(be))]),_:1}),t[9]||(t[9]=s("span",{class:"current"},"蜘蛛日志",-1))])])]),s("div",Pe,[s("div",Fe,[s("div",Be,p(d.total),1),t[11]||(t[11]=s("div",{class:"stat-label"},"总日志数",-1))]),s("div",Ae,[s("div",He,p(T.value),1),t[12]||(t[12]=s("div",{class:"stat-label"},"今日访问",-1))])])])]),s("div",Re,[e(Y,{class:"filter-card"},{default:o(()=>[s("div",$e,[s("div",je,[(V(),S(ke,null,xe(M,l=>e(c,{key:l.type,type:n.spider_type===l.type?"primary":"",onClick:Ze=>F(l.type),class:"spider-filter-btn",size:"small"},{default:o(()=>[r(p(l.name),1)]),_:2},1032,["type","onClick"])),64))])])]),_:1}),e(Y,{class:"toolbar-card"},{default:o(()=>[s("div",Je,[s("div",Oe,[e(c,{type:"danger",class:"action-btn danger",onClick:$},{default:o(()=>[e(_,null,{default:o(()=>[e(u(he))]),_:1}),t[13]||(t[13]=r(" 清理日志 ",-1))]),_:1,__:[13]}),e(c,{class:"action-btn",onClick:B},{default:o(()=>[e(_,null,{default:o(()=>[e(u(we))]),_:1}),t[14]||(t[14]=r(" 导出日志 ",-1))]),_:1,__:[14]})]),s("div",Ge,[e(J,{modelValue:n.spider_type,"onUpdate:modelValue":t[0]||(t[0]=l=>n.spider_type=l),placeholder:"蜘蛛类型",style:{width:"120px"},clearable:""},{default:o(()=>[e(f,{label:"百度",value:1}),e(f,{label:"搜狗",value:2}),e(f,{label:"360",value:3}),e(f,{label:"必应",value:4}),e(f,{label:"谷歌",value:5}),e(f,{label:"神马",value:6})]),_:1},8,["modelValue"]),e(O,{modelValue:n.domain,"onUpdate:modelValue":t[1]||(t[1]=l=>n.domain=l),placeholder:"域名",style:{width:"150px"},clearable:""},{prefix:o(()=>[e(_,null,{default:o(()=>[e(u(Ce))]),_:1})]),_:1},8,["modelValue"]),e(G,{modelValue:C.value,"onUpdate:modelValue":t[2]||(t[2]=l=>C.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:P},null,8,["modelValue"]),e(c,{class:"search-btn",onClick:k},{default:o(()=>[e(_,null,{default:o(()=>[e(u(Ye))]),_:1}),t[15]||(t[15]=r(" 搜索 ",-1))]),_:1,__:[15]}),e(c,{class:"reset-btn",onClick:A},{default:o(()=>[e(_,null,{default:o(()=>[e(u(De))]),_:1}),t[16]||(t[16]=r(" 重置 ",-1))]),_:1,__:[16]})])])]),_:1}),e(Y,{class:"table-card"},{default:o(()=>[ze((V(),Se(Q,{data:w.value,style:{width:"100%"}},{default:o(()=>[e(i,{prop:"spider_type",label:"蜘蛛类型",width:"100"},{default:o(({row:l})=>[e(D,{type:I(l.spider_type)},{default:o(()=>[r(p(L(l.spider_type)),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"ip",label:"IP地址",width:"140"}),e(i,{prop:"domain",label:"域名",width:"150","show-overflow-tooltip":""}),e(i,{prop:"url",label:"访问URL","min-width":"200","show-overflow-tooltip":""}),e(i,{prop:"method",label:"方法",width:"80"},{default:o(({row:l})=>[e(D,{size:"small",type:l.method==="GET"?"success":"warning"},{default:o(()=>[r(p(l.method),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"status_code",label:"状态码",width:"80"},{default:o(({row:l})=>[e(D,{size:"small",type:U(l.status_code)},{default:o(()=>[r(p(l.status_code),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"response_time",label:"响应时间",width:"100"},{default:o(({row:l})=>[r(p(l.response_time)+"ms ",1)]),_:1}),e(i,{prop:"user_agent",label:"User Agent","min-width":"300","show-overflow-tooltip":""}),e(i,{prop:"created_at",label:"访问时间",width:"180"},{default:o(({row:l})=>[r(p(N(l.created_at)),1)]),_:1})]),_:1},8,["data"])),[[ee,h.value]]),s("div",Qe,[e(W,{"current-page":d.page,"onUpdate:currentPage":t[3]||(t[3]=l=>d.page=l),"page-size":d.pageSize,"onUpdate:pageSize":t[4]||(t[4]=l=>d.pageSize=l),"page-sizes":[20,50,100,200],total:d.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:H,onCurrentChange:R},null,8,["current-page","page-size","total"])])]),_:1})]),e(Z,{modelValue:g.value,"onUpdate:modelValue":t[7]||(t[7]=l=>g.value=l),title:"清理日志",width:"400px"},{footer:o(()=>[s("span",Xe,[e(c,{onClick:t[6]||(t[6]=l=>g.value=!1)},{default:o(()=>t[17]||(t[17]=[r("取消",-1)])),_:1,__:[17]}),e(c,{type:"danger",onClick:j},{default:o(()=>t[18]||(t[18]=[r("确定清理",-1)])),_:1,__:[18]})])]),default:o(()=>[e(K,{model:y,"label-width":"100px"},{default:o(()=>[e(q,{label:"保留天数"},{default:o(()=>[e(X,{modelValue:y.days,"onUpdate:modelValue":t[5]||(t[5]=l=>y.days=l),min:1,max:365,placeholder:"请输入保留天数"},null,8,["modelValue"]),s("div",We,"将删除 "+p(y.days)+" 天前的所有日志",1)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},bt=te(qe,[["__scopeId","data-v-394d6b8c"]]);export{bt as default};
