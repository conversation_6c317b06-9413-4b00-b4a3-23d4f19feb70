import{al as wt,y as bt,z as At,Q as Rt,ax as St,r as ce,c as Tt,ay as Ot,az as Pt,au as Dt,aA as Ft}from"./vendor-ad470fc0.js";import{E as x,a as Lt,b as Ct,i as Bt}from"./elementPlus-ef333120.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();/*! Element Plus v2.10.4 */var xt={name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 Delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}};const It={id:"app"},Nt={__name:"App",setup(e){return(t,n)=>{const r=wt("router-view");return bt(),At("div",It,[Rt(r)])}}},kt="modulepreload",Ut=function(e){return"/"+e},Pe={},y=function(t,n,r){if(!n||n.length===0)return t();const o=document.getElementsByTagName("link");return Promise.all(n.map(s=>{if(s=Ut(s),s in Pe)return;Pe[s]=!0;const i=s.endsWith(".css"),u=i?'[rel="stylesheet"]':"";if(!!r)for(let l=o.length-1;l>=0;l--){const f=o[l];if(f.href===s&&(!i||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${u}`))return;const c=document.createElement("link");if(c.rel=i?"stylesheet":kt,i||(c.as="script",c.crossOrigin=""),c.href=s,document.head.appendChild(c),i)return new Promise((l,f)=>{c.addEventListener("load",l),c.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${s}`)))})})).then(()=>t()).catch(s=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=s,window.dispatchEvent(i),!i.defaultPrevented)throw s})};function Ge(e,t){return function(){return e.apply(t,arguments)}}const{toString:jt}=Object.prototype,{getPrototypeOf:we}=Object,{iterator:ne,toStringTag:Xe}=Symbol,re=(e=>t=>{const n=jt.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),F=e=>(e=e.toLowerCase(),t=>re(t)===e),oe=e=>t=>typeof t===e,{isArray:j}=Array,V=oe("undefined");function M(e){return e!==null&&!V(e)&&e.constructor!==null&&!V(e.constructor)&&O(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ye=F("ArrayBuffer");function vt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ye(e.buffer),t}const qt=oe("string"),O=oe("function"),Qe=oe("number"),H=e=>e!==null&&typeof e=="object",Vt=e=>e===!0||e===!1,W=e=>{if(re(e)!=="object")return!1;const t=we(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Xe in e)&&!(ne in e)},Mt=e=>{if(!H(e)||M(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Ht=F("Date"),$t=F("File"),zt=F("Blob"),Jt=F("FileList"),Kt=e=>H(e)&&O(e.pipe),Wt=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||O(e.append)&&((t=re(e))==="formdata"||t==="object"&&O(e.toString)&&e.toString()==="[object FormData]"))},Gt=F("URLSearchParams"),[Xt,Yt,Qt,Zt]=["ReadableStream","Request","Response","Headers"].map(F),en=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function $(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),j(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{if(M(e))return;const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let u;for(r=0;r<i;r++)u=s[r],t.call(null,e[u],u,e)}}function Ze(e,t){if(M(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const k=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),et=e=>!V(e)&&e!==k;function pe(){const{caseless:e}=et(this)&&this||{},t={},n=(r,o)=>{const s=e&&Ze(t,o)||o;W(t[s])&&W(r)?t[s]=pe(t[s],r):W(r)?t[s]=pe({},r):j(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&$(arguments[r],n);return t}const tn=(e,t,n,{allOwnKeys:r}={})=>($(t,(o,s)=>{n&&O(o)?e[s]=Ge(o,n):e[s]=o},{allOwnKeys:r}),e),nn=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),rn=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},on=(e,t,n,r)=>{let o,s,i;const u={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!u[i]&&(t[i]=e[i],u[i]=!0);e=n!==!1&&we(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},sn=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},an=e=>{if(!e)return null;if(j(e))return e;let t=e.length;if(!Qe(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},un=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&we(Uint8Array)),cn=(e,t)=>{const r=(e&&e[ne]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},ln=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},dn=F("HTMLFormElement"),fn=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),De=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),pn=F("RegExp"),tt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};$(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},mn=e=>{tt(e,(t,n)=>{if(O(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(O(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},hn=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return j(e)?r(e):r(String(e).split(t)),n},En=()=>{},yn=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function _n(e){return!!(e&&O(e.append)&&e[Xe]==="FormData"&&e[ne])}const gn=e=>{const t=new Array(10),n=(r,o)=>{if(H(r)){if(t.indexOf(r)>=0)return;if(M(r))return r;if(!("toJSON"in r)){t[o]=r;const s=j(r)?[]:{};return $(r,(i,u)=>{const d=n(i,o+1);!V(d)&&(s[u]=d)}),t[o]=void 0,s}}return r};return n(e,0)},wn=F("AsyncFunction"),bn=e=>e&&(H(e)||O(e))&&O(e.then)&&O(e.catch),nt=((e,t)=>e?setImmediate:t?((n,r)=>(k.addEventListener("message",({source:o,data:s})=>{o===k&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),k.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",O(k.postMessage)),An=typeof queueMicrotask<"u"?queueMicrotask.bind(k):typeof process<"u"&&process.nextTick||nt,Rn=e=>e!=null&&O(e[ne]),a={isArray:j,isArrayBuffer:Ye,isBuffer:M,isFormData:Wt,isArrayBufferView:vt,isString:qt,isNumber:Qe,isBoolean:Vt,isObject:H,isPlainObject:W,isEmptyObject:Mt,isReadableStream:Xt,isRequest:Yt,isResponse:Qt,isHeaders:Zt,isUndefined:V,isDate:Ht,isFile:$t,isBlob:zt,isRegExp:pn,isFunction:O,isStream:Kt,isURLSearchParams:Gt,isTypedArray:un,isFileList:Jt,forEach:$,merge:pe,extend:tn,trim:en,stripBOM:nn,inherits:rn,toFlatObject:on,kindOf:re,kindOfTest:F,endsWith:sn,toArray:an,forEachEntry:cn,matchAll:ln,isHTMLForm:dn,hasOwnProperty:De,hasOwnProp:De,reduceDescriptors:tt,freezeMethods:mn,toObjectSet:hn,toCamelCase:fn,noop:En,toFiniteNumber:yn,findKey:Ze,global:k,isContextDefined:et,isSpecCompliantForm:_n,toJSONObject:gn,isAsyncFn:wn,isThenable:bn,setImmediate:nt,asap:An,isIterable:Rn};function h(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}a.inherits(h,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const rt=h.prototype,ot={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ot[e]={value:e}});Object.defineProperties(h,ot);Object.defineProperty(rt,"isAxiosError",{value:!0});h.from=(e,t,n,r,o,s)=>{const i=Object.create(rt);return a.toFlatObject(e,i,function(d){return d!==Error.prototype},u=>u!=="isAxiosError"),h.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const Sn=null;function me(e){return a.isPlainObject(e)||a.isArray(e)}function st(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function Fe(e,t,n){return e?e.concat(t).map(function(o,s){return o=st(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function Tn(e){return a.isArray(e)&&!e.some(me)}const On=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function se(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,m){return!a.isUndefined(m[E])});const r=n.metaTokens,o=n.visitor||l,s=n.dots,i=n.indexes,d=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(o))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(a.isDate(p))return p.toISOString();if(a.isBoolean(p))return p.toString();if(!d&&a.isBlob(p))throw new h("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(p)||a.isTypedArray(p)?d&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function l(p,E,m){let g=p;if(p&&!m&&typeof p=="object"){if(a.endsWith(E,"{}"))E=r?E:E.slice(0,-2),p=JSON.stringify(p);else if(a.isArray(p)&&Tn(p)||(a.isFileList(p)||a.endsWith(E,"[]"))&&(g=a.toArray(p)))return E=st(E),g.forEach(function(R,B){!(a.isUndefined(R)||R===null)&&t.append(i===!0?Fe([E],B,s):i===null?E:E+"[]",c(R))}),!1}return me(p)?!0:(t.append(Fe(m,E,s),c(p)),!1)}const f=[],_=Object.assign(On,{defaultVisitor:l,convertValue:c,isVisitable:me});function b(p,E){if(!a.isUndefined(p)){if(f.indexOf(p)!==-1)throw Error("Circular reference detected in "+E.join("."));f.push(p),a.forEach(p,function(g,A){(!(a.isUndefined(g)||g===null)&&o.call(t,g,a.isString(A)?A.trim():A,E,_))===!0&&b(g,E?E.concat(A):[A])}),f.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return b(e),t}function Le(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function be(e,t){this._pairs=[],e&&se(e,this,t)}const it=be.prototype;it.append=function(t,n){this._pairs.push([t,n])};it.toString=function(t){const n=t?function(r){return t.call(this,r,Le)}:Le;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function Pn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function at(e,t,n){if(!t)return e;const r=n&&n.encode||Pn;a.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=a.isURLSearchParams(t)?t.toString():new be(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Dn{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ce=Dn,ut={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Fn=typeof URLSearchParams<"u"?URLSearchParams:be,Ln=typeof FormData<"u"?FormData:null,Cn=typeof Blob<"u"?Blob:null,Bn={isBrowser:!0,classes:{URLSearchParams:Fn,FormData:Ln,Blob:Cn},protocols:["http","https","file","blob","url","data"]},Ae=typeof window<"u"&&typeof document<"u",he=typeof navigator=="object"&&navigator||void 0,xn=Ae&&(!he||["ReactNative","NativeScript","NS"].indexOf(he.product)<0),In=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Nn=Ae&&window.location.href||"http://localhost",kn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ae,hasStandardBrowserEnv:xn,hasStandardBrowserWebWorkerEnv:In,navigator:he,origin:Nn},Symbol.toStringTag,{value:"Module"})),S={...kn,...Bn};function Un(e,t){return se(e,new S.classes.URLSearchParams,{visitor:function(n,r,o,s){return S.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)},...t})}function jn(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function vn(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function ct(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const u=Number.isFinite(+i),d=s>=n.length;return i=!i&&a.isArray(o)?o.length:i,d?(a.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!u):((!o[i]||!a.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&a.isArray(o[i])&&(o[i]=vn(o[i])),!u)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,o)=>{t(jn(r),o,n,0)}),n}return null}function qn(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Re={transitional:ut,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=a.isObject(t);if(s&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return o?JSON.stringify(ct(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let u;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Un(t,this.formSerializer).toString();if((u=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const d=this.env&&this.env.FormData;return se(u?{"files[]":t}:t,d&&new d,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),qn(t)):t}],transformResponse:[function(t){const n=this.transitional||Re.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(u){if(i)throw u.name==="SyntaxError"?h.from(u,h.ERR_BAD_RESPONSE,this,null,this.response):u}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:S.classes.FormData,Blob:S.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{Re.headers[e]={}});const Se=Re,Vn=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Mn=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&Vn[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Be=Symbol("internals");function q(e){return e&&String(e).trim().toLowerCase()}function G(e){return e===!1||e==null?e:a.isArray(e)?e.map(G):String(e)}function Hn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const $n=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function le(e,t,n,r,o){if(a.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function zn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Jn(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}class ie{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(u,d,c){const l=q(d);if(!l)throw new Error("header name must be a non-empty string");const f=a.findKey(o,l);(!f||o[f]===void 0||c===!0||c===void 0&&o[f]!==!1)&&(o[f||d]=G(u))}const i=(u,d)=>a.forEach(u,(c,l)=>s(c,l,d));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!$n(t))i(Mn(t),n);else if(a.isObject(t)&&a.isIterable(t)){let u={},d,c;for(const l of t){if(!a.isArray(l))throw TypeError("Object iterator must return a key-value pair");u[c=l[0]]=(d=u[c])?a.isArray(d)?[...d,l[1]]:[d,l[1]]:l[1]}i(u,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=q(t),t){const r=a.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return Hn(o);if(a.isFunction(n))return n.call(this,o,r);if(a.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=q(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||le(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=q(i),i){const u=a.findKey(r,i);u&&(!n||le(r,r[u],u,n))&&(delete r[u],o=!0)}}return a.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||le(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return a.forEach(this,(o,s)=>{const i=a.findKey(r,s);if(i){n[i]=G(o),delete n[s];return}const u=t?zn(s):String(s).trim();u!==s&&delete n[s],n[u]=G(o),r[u]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Be]=this[Be]={accessors:{}}).accessors,o=this.prototype;function s(i){const u=q(i);r[u]||(Jn(o,i),r[u]=!0)}return a.isArray(t)?t.forEach(s):s(t),this}}ie.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(ie.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(ie);const D=ie;function de(e,t){const n=this||Se,r=t||n,o=D.from(r.headers);let s=r.data;return a.forEach(e,function(u){s=u.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function lt(e){return!!(e&&e.__CANCEL__)}function v(e,t,n){h.call(this,e??"canceled",h.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(v,h,{__CANCEL__:!0});function dt(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new h("Request failed with status code "+n.status,[h.ERR_BAD_REQUEST,h.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Kn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Wn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(d){const c=Date.now(),l=r[s];i||(i=c),n[o]=d,r[o]=c;let f=s,_=0;for(;f!==o;)_+=n[f++],f=f%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<t)return;const b=l&&c-l;return b?Math.round(_*1e3/b):void 0}}function Gn(e,t){let n=0,r=1e3/t,o,s;const i=(c,l=Date.now())=>{n=l,o=null,s&&(clearTimeout(s),s=null),e(...c)};return[(...c)=>{const l=Date.now(),f=l-n;f>=r?i(c,l):(o=c,s||(s=setTimeout(()=>{s=null,i(o)},r-f)))},()=>o&&i(o)]}const Q=(e,t,n=3)=>{let r=0;const o=Wn(50,250);return Gn(s=>{const i=s.loaded,u=s.lengthComputable?s.total:void 0,d=i-r,c=o(d),l=i<=u;r=i;const f={loaded:i,total:u,progress:u?i/u:void 0,bytes:d,rate:c||void 0,estimated:c&&u&&l?(u-i)/c:void 0,event:s,lengthComputable:u!=null,[t?"download":"upload"]:!0};e(f)},n)},xe=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ie=e=>(...t)=>a.asap(()=>e(...t)),Xn=S.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,S.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(S.origin),S.navigator&&/(msie|trident)/i.test(S.navigator.userAgent)):()=>!0,Yn=S.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Qn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Zn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ft(e,t,n){let r=!Qn(t);return e&&(r||n==!1)?Zn(e,t):t}const Ne=e=>e instanceof D?{...e}:e;function U(e,t){t=t||{};const n={};function r(c,l,f,_){return a.isPlainObject(c)&&a.isPlainObject(l)?a.merge.call({caseless:_},c,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function o(c,l,f,_){if(a.isUndefined(l)){if(!a.isUndefined(c))return r(void 0,c,f,_)}else return r(c,l,f,_)}function s(c,l){if(!a.isUndefined(l))return r(void 0,l)}function i(c,l){if(a.isUndefined(l)){if(!a.isUndefined(c))return r(void 0,c)}else return r(void 0,l)}function u(c,l,f){if(f in t)return r(c,l);if(f in e)return r(void 0,c)}const d={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:u,headers:(c,l,f)=>o(Ne(c),Ne(l),f,!0)};return a.forEach(Object.keys({...e,...t}),function(l){const f=d[l]||o,_=f(e[l],t[l],l);a.isUndefined(_)&&f!==u||(n[l]=_)}),n}const pt=e=>{const t=U({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:u}=t;t.headers=i=D.from(i),t.url=at(ft(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),u&&i.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):"")));let d;if(a.isFormData(n)){if(S.hasStandardBrowserEnv||S.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((d=i.getContentType())!==!1){const[c,...l]=d?d.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...l].join("; "))}}if(S.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&Xn(t.url))){const c=o&&s&&Yn.read(s);c&&i.set(o,c)}return t},er=typeof XMLHttpRequest<"u",tr=er&&function(e){return new Promise(function(n,r){const o=pt(e);let s=o.data;const i=D.from(o.headers).normalize();let{responseType:u,onUploadProgress:d,onDownloadProgress:c}=o,l,f,_,b,p;function E(){b&&b(),p&&p(),o.cancelToken&&o.cancelToken.unsubscribe(l),o.signal&&o.signal.removeEventListener("abort",l)}let m=new XMLHttpRequest;m.open(o.method.toUpperCase(),o.url,!0),m.timeout=o.timeout;function g(){if(!m)return;const R=D.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),T={data:!u||u==="text"||u==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:R,config:e,request:m};dt(function(N){n(N),E()},function(N){r(N),E()},T),m=null}"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(g)},m.onabort=function(){m&&(r(new h("Request aborted",h.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new h("Network Error",h.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let B=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const T=o.transitional||ut;o.timeoutErrorMessage&&(B=o.timeoutErrorMessage),r(new h(B,T.clarifyTimeoutError?h.ETIMEDOUT:h.ECONNABORTED,e,m)),m=null},s===void 0&&i.setContentType(null),"setRequestHeader"in m&&a.forEach(i.toJSON(),function(B,T){m.setRequestHeader(T,B)}),a.isUndefined(o.withCredentials)||(m.withCredentials=!!o.withCredentials),u&&u!=="json"&&(m.responseType=o.responseType),c&&([_,p]=Q(c,!0),m.addEventListener("progress",_)),d&&m.upload&&([f,b]=Q(d),m.upload.addEventListener("progress",f),m.upload.addEventListener("loadend",b)),(o.cancelToken||o.signal)&&(l=R=>{m&&(r(!R||R.type?new v(null,e,m):R),m.abort(),m=null)},o.cancelToken&&o.cancelToken.subscribe(l),o.signal&&(o.signal.aborted?l():o.signal.addEventListener("abort",l)));const A=Kn(o.url);if(A&&S.protocols.indexOf(A)===-1){r(new h("Unsupported protocol "+A+":",h.ERR_BAD_REQUEST,e));return}m.send(s||null)})},nr=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(c){if(!o){o=!0,u();const l=c instanceof Error?c:this.reason;r.abort(l instanceof h?l:new v(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,s(new h(`timeout ${t} of ms exceeded`,h.ETIMEDOUT))},t);const u=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(s):c.removeEventListener("abort",s)}),e=null)};e.forEach(c=>c.addEventListener("abort",s));const{signal:d}=r;return d.unsubscribe=()=>a.asap(u),d}},rr=nr,or=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},sr=async function*(e,t){for await(const n of ir(e))yield*or(n,t)},ir=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},ke=(e,t,n,r)=>{const o=sr(e,t);let s=0,i,u=d=>{i||(i=!0,r&&r(d))};return new ReadableStream({async pull(d){try{const{done:c,value:l}=await o.next();if(c){u(),d.close();return}let f=l.byteLength;if(n){let _=s+=f;n(_)}d.enqueue(new Uint8Array(l))}catch(c){throw u(c),c}},cancel(d){return u(d),o.return()}},{highWaterMark:2})},ae=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",mt=ae&&typeof ReadableStream=="function",ar=ae&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ht=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ur=mt&&ht(()=>{let e=!1;const t=new Request(S.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ue=64*1024,Ee=mt&&ht(()=>a.isReadableStream(new Response("").body)),Z={stream:Ee&&(e=>e.body)};ae&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Z[t]&&(Z[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new h(`Response type '${t}' is not supported`,h.ERR_NOT_SUPPORT,r)})})})(new Response);const cr=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(S.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await ar(e)).byteLength},lr=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??cr(t)},dr=ae&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:u,onUploadProgress:d,responseType:c,headers:l,withCredentials:f="same-origin",fetchOptions:_}=pt(e);c=c?(c+"").toLowerCase():"text";let b=rr([o,s&&s.toAbortSignal()],i),p;const E=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let m;try{if(d&&ur&&n!=="get"&&n!=="head"&&(m=await lr(l,r))!==0){let T=new Request(t,{method:"POST",body:r,duplex:"half"}),I;if(a.isFormData(r)&&(I=T.headers.get("content-type"))&&l.setContentType(I),T.body){const[N,J]=xe(m,Q(Ie(d)));r=ke(T.body,Ue,N,J)}}a.isString(f)||(f=f?"include":"omit");const g="credentials"in Request.prototype;p=new Request(t,{..._,signal:b,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:g?f:void 0});let A=await fetch(p,_);const R=Ee&&(c==="stream"||c==="response");if(Ee&&(u||R&&E)){const T={};["status","statusText","headers"].forEach(Oe=>{T[Oe]=A[Oe]});const I=a.toFiniteNumber(A.headers.get("content-length")),[N,J]=u&&xe(I,Q(Ie(u),!0))||[];A=new Response(ke(A.body,Ue,N,()=>{J&&J(),E&&E()}),T)}c=c||"text";let B=await Z[a.findKey(Z,c)||"text"](A,e);return!R&&E&&E(),await new Promise((T,I)=>{dt(T,I,{data:B,headers:D.from(A.headers),status:A.status,statusText:A.statusText,config:e,request:p})})}catch(g){throw E&&E(),g&&g.name==="TypeError"&&/Load failed|fetch/i.test(g.message)?Object.assign(new h("Network Error",h.ERR_NETWORK,e,p),{cause:g.cause||g}):h.from(g,g&&g.code,e,p)}}),ye={http:Sn,xhr:tr,fetch:dr};a.forEach(ye,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const je=e=>`- ${e}`,fr=e=>a.isFunction(e)||e===null||e===!1,Et={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!fr(n)&&(r=ye[(i=String(n)).toLowerCase()],r===void 0))throw new h(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([u,d])=>`adapter ${u} `+(d===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(je).join(`
`):" "+je(s[0]):"as no adapter specified";throw new h("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:ye};function fe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new v(null,e)}function ve(e){return fe(e),e.headers=D.from(e.headers),e.data=de.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Et.getAdapter(e.adapter||Se.adapter)(e).then(function(r){return fe(e),r.data=de.call(e,e.transformResponse,r),r.headers=D.from(r.headers),r},function(r){return lt(r)||(fe(e),r&&r.response&&(r.response.data=de.call(e,e.transformResponse,r.response),r.response.headers=D.from(r.response.headers))),Promise.reject(r)})}const yt="1.11.0",ue={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ue[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const qe={};ue.transitional=function(t,n,r){function o(s,i){return"[Axios v"+yt+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,u)=>{if(t===!1)throw new h(o(i," has been removed"+(n?" in "+n:"")),h.ERR_DEPRECATED);return n&&!qe[i]&&(qe[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,u):!0}};ue.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function pr(e,t,n){if(typeof e!="object")throw new h("options must be an object",h.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const u=e[s],d=u===void 0||i(u,s,e);if(d!==!0)throw new h("option "+s+" must be "+d,h.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new h("Unknown option "+s,h.ERR_BAD_OPTION)}}const X={assertOptions:pr,validators:ue},L=X.validators;class ee{constructor(t){this.defaults=t||{},this.interceptors={request:new Ce,response:new Ce}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=U(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&X.assertOptions(r,{silentJSONParsing:L.transitional(L.boolean),forcedJSONParsing:L.transitional(L.boolean),clarifyTimeoutError:L.transitional(L.boolean)},!1),o!=null&&(a.isFunction(o)?n.paramsSerializer={serialize:o}:X.assertOptions(o,{encode:L.function,serialize:L.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),X.assertOptions(n,{baseUrl:L.spelling("baseURL"),withXsrfToken:L.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&a.merge(s.common,s[n.method]);s&&a.forEach(["delete","get","head","post","put","patch","common"],p=>{delete s[p]}),n.headers=D.concat(i,s);const u=[];let d=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(n)===!1||(d=d&&E.synchronous,u.unshift(E.fulfilled,E.rejected))});const c=[];this.interceptors.response.forEach(function(E){c.push(E.fulfilled,E.rejected)});let l,f=0,_;if(!d){const p=[ve.bind(this),void 0];for(p.unshift(...u),p.push(...c),_=p.length,l=Promise.resolve(n);f<_;)l=l.then(p[f++],p[f++]);return l}_=u.length;let b=n;for(f=0;f<_;){const p=u[f++],E=u[f++];try{b=p(b)}catch(m){E.call(this,m);break}}try{l=ve.call(this,b)}catch(p){return Promise.reject(p)}for(f=0,_=c.length;f<_;)l=l.then(c[f++],c[f++]);return l}getUri(t){t=U(this.defaults,t);const n=ft(t.baseURL,t.url,t.allowAbsoluteUrls);return at(n,t.params,t.paramsSerializer)}}a.forEach(["delete","get","head","options"],function(t){ee.prototype[t]=function(n,r){return this.request(U(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,u){return this.request(U(u||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}ee.prototype[t]=n(),ee.prototype[t+"Form"]=n(!0)});const Y=ee;class Te{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(u=>{r.subscribe(u),s=u}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,u){r.reason||(r.reason=new v(s,i,u),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Te(function(o){t=o}),cancel:t}}}const mr=Te;function hr(e){return function(n){return e.apply(null,n)}}function Er(e){return a.isObject(e)&&e.isAxiosError===!0}const _e={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(_e).forEach(([e,t])=>{_e[t]=e});const yr=_e;function _t(e){const t=new Y(e),n=Ge(Y.prototype.request,t);return a.extend(n,Y.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return _t(U(e,o))},n}const w=_t(Se);w.Axios=Y;w.CanceledError=v;w.CancelToken=mr;w.isCancel=lt;w.VERSION=yt;w.toFormData=se;w.AxiosError=h;w.Cancel=w.CanceledError;w.all=function(t){return Promise.all(t)};w.spread=hr;w.isAxiosError=Er;w.mergeConfig=U;w.AxiosHeaders=D;w.formToJSON=e=>ct(a.isHTMLForm(e)?new FormData(e):e);w.getAdapter=Et.getAdapter;w.HttpStatusCode=yr;w.default=w;const _r=w,P=_r.create({baseURL:"/api",timeout:1e4});P.interceptors.request.use(e=>{const t=te();return t.token&&(e.headers.Authorization=`Bearer ${t.token}`),e},e=>(console.error("Request error:",e),Promise.reject(e)));P.interceptors.response.use(e=>{const t=e.data;return t.code!==200?(x.error(t.message||"请求失败"),t.code===401&&Lt.confirm("登录状态已过期，请重新登录","系统提示",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{te().logout().then(()=>{ge.push("/login")})}),Promise.reject(new Error(t.message||"请求失败"))):t},e=>{console.error("Response error:",e);let t="请求失败";if(e.response){const{status:n,data:r}=e.response;switch(n){case 400:t=r.message||"请求参数错误";break;case 401:t="未授权，请重新登录",te().logout().then(()=>{ge.push("/login")});break;case 403:t="拒绝访问";break;case 404:t="请求的资源不存在";break;case 500:t="服务器内部错误";break;default:t=r.message||`请求失败 (${n})`}}else e.code==="ECONNABORTED"?t="请求超时":e.message&&(t=e.message);return x.error(t),Promise.reject(e)});const K={login(e){return P({url:"/auth/login",method:"post",data:e})},logout(){return P({url:"/auth/logout",method:"post"})},getProfile(){return P({url:"/auth/profile",method:"get"})},updateProfile(e){return P({url:"/auth/profile",method:"put",data:e})},getUserList(e){return P({url:"/users",method:"get",params:e})},createUser(e){return P({url:"/users",method:"post",data:e})},getUser(e){return P({url:`/users/${e}`,method:"get"})},updateUser(e,t){return P({url:`/users/${e}`,method:"put",data:t})},deleteUser(e){return P({url:`/users/${e}`,method:"delete"})}},te=St("auth",()=>{const e=ce(localStorage.getItem("token")||""),t=ce(JSON.parse(localStorage.getItem("user")||"null")),n=ce(!1),r=Tt(()=>!!e.value&&!!t.value),o=async d=>{n.value=!0;try{const c=await K.login(d);if(c.code===200){const{token:l,user:f}=c.data;return e.value=l,t.value=f,localStorage.setItem("token",l),localStorage.setItem("user",JSON.stringify(f)),x.success("登录成功"),!0}else return x.error(c.message||"登录失败"),!1}catch(c){return x.error(c.message||"登录失败"),!1}finally{n.value=!1}},s=async()=>{try{await K.logout()}catch(d){console.error("Logout error:",d)}finally{e.value="",t.value=null,localStorage.removeItem("token"),localStorage.removeItem("user"),x.success("已退出登录")}},i=async()=>{try{const d=await K.getProfile();d.code===200&&(t.value=d.data,localStorage.setItem("user",JSON.stringify(d.data)))}catch(d){console.error("Fetch profile error:",d)}};return{token:e,user:t,loading:n,isLoggedIn:r,login:o,logout:s,fetchProfile:i,updateProfile:async d=>{try{const c=await K.updateProfile(d);return c.code===200?(await i(),x.success("更新成功"),!0):(x.error(c.message||"更新失败"),!1)}catch(c){return x.error(c.message||"更新失败"),!1}}}}),C=()=>y(()=>import("./index-37f36ce3.js"),["assets/index-37f36ce3.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/index-07e621f1.css","assets/el-button-f2767652.css","assets/el-dropdown-item-b7fb1426.css","assets/el-scrollbar-d2ed595a.css"]),gr=()=>y(()=>import("./index-c3e72397.js"),["assets/index-c3e72397.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/index-887dd392.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css"]),wr=()=>y(()=>import("./index-ec987f15.js"),["assets/index-ec987f15.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/charts-5a62b0c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/index-669f016b.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css"]),Ve=()=>y(()=>import("./list-2db10fe2.js"),["assets/list-2db10fe2.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/project-3c35c2de.js","assets/elementPlus-ef333120.js","assets/form-217a9b9e.js","assets/form-d8a1b312.css","assets/el-card-7155ea48.css","assets/el-tab-pane-3f3f12a2.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-form-item-a926de16.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-button-f2767652.css","assets/list-ed2e3a43.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-dropdown-item-b7fb1426.css","assets/el-progress-d6a46dc4.css"]),Me=()=>y(()=>import("./form-217a9b9e.js"),["assets/form-217a9b9e.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/project-3c35c2de.js","assets/elementPlus-ef333120.js","assets/form-d8a1b312.css","assets/el-card-7155ea48.css","assets/el-tab-pane-3f3f12a2.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-form-item-a926de16.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-button-f2767652.css"]),He=()=>y(()=>import("./list-9e23776e.js"),["assets/list-9e23776e.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/keyword-dca4d9f3.js","assets/elementPlus-ef333120.js","assets/list-fc10ca82.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),$e=()=>y(()=>import("./form-25f70921.js"),["assets/form-25f70921.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/keyword-dca4d9f3.js","assets/elementPlus-ef333120.js","assets/form-b0373fcc.css","assets/el-card-7155ea48.css","assets/el-form-item-a926de16.css","assets/el-col-60e44389.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-button-f2767652.css"]),ze=()=>y(()=>import("./list-fa5b60a7.js"),["assets/list-fa5b60a7.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/list-b08f8fb4.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),Je=()=>y(()=>import("./form-8dd0eb1a.js"),["assets/form-8dd0eb1a.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/el-empty-06735b9d.css"]),br=()=>y(()=>import("./stats-a6988011.js"),["assets/stats-a6988011.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/charts-5a62b0c7.js","assets/vendor-ad470fc0.js","assets/spider-e2082f6b.js","assets/elementPlus-ef333120.js","assets/stats-32969ec9.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),Ar=()=>y(()=>import("./today-1909c2a8.js"),["assets/today-1909c2a8.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/spider-e2082f6b.js","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/today-34a74b20.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),Rr=()=>y(()=>import("./yesterday-970d8870.js"),["assets/yesterday-970d8870.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/charts-5a62b0c7.js","assets/vendor-ad470fc0.js","assets/spider-e2082f6b.js","assets/elementPlus-ef333120.js","assets/yesterday-ae0c86f7.css","assets/el-loading-de58975a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),Sr=()=>y(()=>import("./logs-17f75386.js"),["assets/logs-17f75386.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/spider-e2082f6b.js","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/logs-979ca32f.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css","assets/el-card-7155ea48.css"]),Tr=()=>y(()=>import("./list-50d13b7f.js"),["assets/list-50d13b7f.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/api-c21033af.js","assets/elementPlus-ef333120.js","assets/list-27a8bc90.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-descriptions-item-3a3080a1.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),Ke=()=>y(()=>import("./form-a51101ec.js"),["assets/form-a51101ec.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/api-c21033af.js","assets/elementPlus-ef333120.js","assets/form-964e9e4b.css","assets/el-card-7155ea48.css","assets/el-form-item-a926de16.css","assets/el-col-60e44389.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-button-f2767652.css"]),Or=()=>y(()=>import("./list-eb4e629a.js"),["assets/list-eb4e629a.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/list-2fa51596.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-card-7155ea48.css","assets/el-button-f2767652.css"]),We=()=>y(()=>import("./form-5015f488.js"),["assets/form-5015f488.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/el-empty-06735b9d.css"]),Pr=()=>y(()=>import("./list-ea1890e5.js"),["assets/list-ea1890e5.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/list-5c19e3ef.css","assets/el-loading-de58975a.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css"]),Dr=()=>y(()=>import("./index-6d5e19b3.js"),["assets/index-6d5e19b3.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/el-empty-06735b9d.css"]),Fr=()=>y(()=>import("./seo-health-511c03c2.js"),["assets/seo-health-511c03c2.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/seo-health-155eb2d3.css","assets/el-empty-06735b9d.css","assets/el-tab-pane-3f3f12a2.css","assets/el-col-60e44389.css","assets/el-statistic-cc518016.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-descriptions-item-3a3080a1.css","assets/el-progress-d6a46dc4.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-checkbox-group-987ef89c.css","assets/el-input-a97a1ae3.css","assets/el-card-7155ea48.css"]),Lr=()=>y(()=>import("./keyword-density-eddfbb10.js"),["assets/keyword-density-eddfbb10.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/charts-5a62b0c7.js","assets/keyword-density-cdf88283.css","assets/el-empty-06735b9d.css","assets/el-alert-c5e82332.css","assets/el-progress-d6a46dc4.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-scrollbar-d2ed595a.css","assets/el-tag-afac09bb.css","assets/el-divider-07810808.css","assets/el-col-60e44389.css","assets/el-statistic-cc518016.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-checkbox-group-987ef89c.css","assets/el-input-a97a1ae3.css","assets/el-radio-eac3aa84.css","assets/el-card-7155ea48.css"]),Cr=()=>y(()=>import("./meta-checker-0b3e3472.js"),["assets/meta-checker-0b3e3472.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/meta-checker-5a7da72a.css","assets/el-empty-06735b9d.css","assets/el-col-60e44389.css","assets/el-descriptions-item-3a3080a1.css","assets/el-divider-07810808.css","assets/el-tag-afac09bb.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-checkbox-group-987ef89c.css","assets/el-checkbox-4bf2f35b.css","assets/el-input-a97a1ae3.css","assets/el-card-7155ea48.css"]),Br=()=>y(()=>import("./title-generator-cbea4419.js"),["assets/title-generator-cbea4419.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/title-generator-3d786398.css","assets/el-overlay-f8194c50.css","assets/el-descriptions-item-3a3080a1.css","assets/el-col-60e44389.css","assets/el-empty-06735b9d.css","assets/el-divider-07810808.css","assets/el-tag-afac09bb.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-slider-700adbcf.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-scrollbar-d2ed595a.css","assets/el-checkbox-group-987ef89c.css","assets/el-checkbox-4bf2f35b.css","assets/el-select-80475228.css","assets/el-card-7155ea48.css"]),xr=()=>y(()=>import("./meta-generator-bd74f367.js"),["assets/meta-generator-bd74f367.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/meta-generator-8f16bb31.css","assets/el-overlay-f8194c50.css","assets/el-tab-pane-3f3f12a2.css","assets/el-empty-06735b9d.css","assets/el-col-60e44389.css","assets/el-statistic-cc518016.css","assets/el-progress-d6a46dc4.css","assets/el-tag-afac09bb.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-slider-700adbcf.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-scrollbar-d2ed595a.css","assets/el-checkbox-group-987ef89c.css","assets/el-checkbox-4bf2f35b.css","assets/el-select-80475228.css","assets/el-card-7155ea48.css"]),Ir=[{path:"/login",name:"Login",component:gr,meta:{title:"登录",requiresAuth:!1}},{path:"/",component:C,redirect:"/dashboard",meta:{requiresAuth:!0},children:[{path:"dashboard",name:"Dashboard",component:wr,meta:{title:"仪表盘",icon:"Dashboard"}}]},{path:"/project",component:C,meta:{title:"项目管理",icon:"Folder",requiresAuth:!0},children:[{path:"standalone",name:"ProjectStandalone",component:Ve,meta:{title:"项目列表(单例模式)"}},{path:"interface",name:"ProjectInterface",component:Ve,meta:{title:"项目列表(接口模式)"}},{path:"keywords",name:"ProjectKeywords",component:He,meta:{title:"词库管理"}},{path:"templates",name:"ProjectTemplates",component:ze,meta:{title:"模板管理"}},{path:"url-rules",name:"ProjectUrlRules",component:()=>y(()=>import("./url-rules-798cde36.js"),["assets/url-rules-798cde36.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/url-rules-90ac264d.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-radio-eac3aa84.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css"]),meta:{title:"URL规则管理"}},{path:"external-links",name:"ProjectExternalLinks",component:()=>y(()=>import("./external-links-69934ab6.js"),["assets/external-links-69934ab6.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/external-links-fa6230db.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css"]),meta:{title:"外链管理"}},{path:"notifications",name:"ProjectNotifications",component:()=>y(()=>import("./notifications-449351e3.js"),["assets/notifications-449351e3.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/notifications-1c165057.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css"]),meta:{title:"消息通知"}},{path:"create",name:"ProjectCreate",component:Me,meta:{title:"创建项目",hidden:!0}},{path:"interface/create",name:"ProjectInterfaceCreate",component:()=>y(()=>import("./interface-form-219c4999.js"),["assets/interface-form-219c4999.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/project-3c35c2de.js","assets/elementPlus-ef333120.js","assets/interface-form-51e007a3.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"创建接口项目",hidden:!0}},{path:"interface/edit/:id",name:"ProjectInterfaceEdit",component:()=>y(()=>import("./interface-form-219c4999.js"),["assets/interface-form-219c4999.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/vendor-ad470fc0.js","assets/project-3c35c2de.js","assets/elementPlus-ef333120.js","assets/interface-form-51e007a3.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"编辑接口项目",hidden:!0}},{path:"edit/:id",name:"ProjectEdit",component:Me,meta:{title:"编辑项目",hidden:!0}},{path:":id/keywords",name:"ProjectKeywordManage",component:()=>y(()=>import("./keyword-manage-27351aaf.js"),["assets/keyword-manage-27351aaf.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/keyword-manage-e2877ebd.css","assets/el-loading-de58975a.css","assets/el-progress-d6a46dc4.css","assets/el-alert-c5e82332.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-radio-eac3aa84.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css","assets/el-card-7155ea48.css"]),meta:{title:"项目词库管理",hidden:!0}},{path:":id/templates",name:"ProjectTemplateManage",component:()=>y(()=>import("./template-manage-4ade0f00.js"),["assets/template-manage-4ade0f00.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/template-manage-e6b91305.css","assets/el-card-7155ea48.css"]),meta:{title:"项目模板管理",hidden:!0}},{path:":id/urls",name:"ProjectUrlManage",component:()=>y(()=>import("./url-rules-798cde36.js"),["assets/url-rules-798cde36.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/el-tooltip-4ed993c7.js","assets/vendor-ad470fc0.js","assets/elementPlus-ef333120.js","assets/url-rules-90ac264d.css","assets/el-loading-de58975a.css","assets/el-overlay-f8194c50.css","assets/el-form-item-a926de16.css","assets/el-radio-eac3aa84.css","assets/el-input-a97a1ae3.css","assets/el-input-number-f614289c.css","assets/el-pagination-5496530f.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-f2767652.css"]),meta:{title:"项目URL管理",hidden:!0}}]},{path:"/keyword",component:C,meta:{title:"关键词管理",icon:"Key",requiresAuth:!0},children:[{path:"",name:"KeywordList",component:He,meta:{title:"关键词列表"}},{path:"create",name:"KeywordCreate",component:$e,meta:{title:"创建关键词库"}},{path:"edit/:id",name:"KeywordEdit",component:$e,meta:{title:"编辑关键词库"}}]},{path:"/template",component:C,meta:{title:"模板管理",icon:"Document",requiresAuth:!0},children:[{path:"",name:"TemplateList",component:ze,meta:{title:"模板列表"}},{path:"create",name:"TemplateCreate",component:Je,meta:{title:"创建模板"}},{path:"edit/:id",name:"TemplateEdit",component:Je,meta:{title:"编辑模板"}}]},{path:"/spider",component:C,meta:{title:"蜘蛛管理",icon:"DataAnalysis",requiresAuth:!0},children:[{path:"today",name:"SpiderToday",component:Ar,meta:{title:"今日蜘蛛统计"}},{path:"yesterday",name:"SpiderYesterday",component:Rr,meta:{title:"昨日蜘蛛统计"}},{path:"stats",name:"SpiderStats",component:br,meta:{title:"蜘蛛数据看板"}},{path:"logs",name:"SpiderLogs",component:Sr,meta:{title:"访问日志"}}]},{path:"/tools",component:C,meta:{title:"辅助工具",icon:"Setting",requiresAuth:!0},children:[{path:"spider-simulator",name:"SpiderSimulator",component:()=>y(()=>import("./spider-simulator-9b584332.js"),["assets/spider-simulator-9b584332.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/spider-simulator-a29390a0.css","assets/el-tab-pane-3f3f12a2.css","assets/el-col-60e44389.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"在线蜘蛛模拟"}},{path:"404-hijack-detector",name:"404HijackDetector",component:()=>y(()=>import("./404-hijack-detector-72575ad1.js"),["assets/404-hijack-detector-72575ad1.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/404-hijack-detector-b1a69edb.css","assets/el-tab-pane-3f3f12a2.css","assets/el-col-60e44389.css","assets/el-tag-afac09bb.css","assets/el-button-f2767652.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-input-a97a1ae3.css"]),meta:{title:"404劫持检测"}},{path:"htaccess-generator",name:"HtaccessGenerator",component:()=>y(()=>import("./htaccess-generator-4f1a5aa8.js"),["assets/htaccess-generator-4f1a5aa8.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/htaccess-generator-df4bbba2.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css"]),meta:{title:".htaccess全站劫持生成"}},{path:"iis6-generator",name:"IIS6Generator",component:()=>y(()=>import("./iis6-generator-1983b4f3.js"),["assets/iis6-generator-1983b4f3.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/iis6-generator-15a4ddcf.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css"]),meta:{title:"IIS 6 站群劫持生成"}},{path:"iis-domain-export",name:"IISDomainExport",component:()=>y(()=>import("./iis-domain-export-3261850e.js"),["assets/iis-domain-export-3261850e.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/iis-domain-export-90557033.css","assets/el-card-7155ea48.css"]),meta:{title:"IIS 域名导出"}},{path:"file-index-encrypt",name:"FileIndexEncrypt",component:()=>y(()=>import("./file-index-encrypt-a8943a8e.js"),["assets/file-index-encrypt-a8943a8e.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/file-index-encrypt-b98b7592.css","assets/el-descriptions-item-3a3080a1.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css","assets/el-checkbox-group-987ef89c.css","assets/el-checkbox-4bf2f35b.css","assets/el-tag-afac09bb.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css","assets/el-radio-eac3aa84.css"]),meta:{title:"文件索引加密"}},{path:"js-encrypt-decrypt",name:"JSEncryptDecrypt",component:()=>y(()=>import("./js-encrypt-decrypt-7ddf768c.js"),["assets/js-encrypt-decrypt-7ddf768c.js","assets/_plugin-vue_export-helper-183d9f20.js","assets/_plugin-vue_export-helper-7be526c9.css","assets/elementPlus-ef333120.js","assets/vendor-ad470fc0.js","assets/js-encrypt-decrypt-08c100c7.css","assets/el-tab-pane-3f3f12a2.css","assets/el-tag-afac09bb.css","assets/el-descriptions-item-3a3080a1.css","assets/el-form-item-a926de16.css","assets/el-button-f2767652.css","assets/el-input-a97a1ae3.css","assets/el-checkbox-group-987ef89c.css","assets/el-checkbox-4bf2f35b.css","assets/el-select-80475228.css","assets/el-scrollbar-d2ed595a.css"]),meta:{title:"JS 加密 or 解密"}},{path:"seo-health",name:"SeoHealth",component:Fr,meta:{title:"SEO健康度检测"}},{path:"keyword-density",name:"KeywordDensity",component:Lr,meta:{title:"关键词密度分析"}},{path:"meta-checker",name:"MetaChecker",component:Cr,meta:{title:"Meta标签检查"}},{path:"title-generator",name:"TitleGenerator",component:Br,meta:{title:"标题生成器"}},{path:"meta-generator",name:"MetaGenerator",component:xr,meta:{title:"Meta描述生成器"}}]},{path:"/api",component:C,meta:{title:"API管理",icon:"Connection",requiresAuth:!0},children:[{path:"",name:"APIList",component:Tr,meta:{title:"API列表"}},{path:"create",name:"APICreate",component:Ke,meta:{title:"创建API"}},{path:"edit/:id",name:"APIEdit",component:Ke,meta:{title:"编辑API"}}]},{path:"/user",component:C,meta:{title:"用户管理",icon:"User",requiresAuth:!0},children:[{path:"",name:"UserList",component:Or,meta:{title:"用户列表"}},{path:"create",name:"UserCreate",component:We,meta:{title:"创建用户"}},{path:"edit/:id",name:"UserEdit",component:We,meta:{title:"编辑用户"}}]},{path:"/useragent",component:C,meta:{requiresAuth:!0},children:[{path:"",name:"UserAgentList",component:Pr,meta:{title:"User-Agent管理",icon:"Monitor"}}]},{path:"/profile",component:C,meta:{requiresAuth:!0},children:[{path:"",name:"Profile",component:Dr,meta:{title:"个人资料"}}]}],gt=Ot({history:Pt(),routes:Ir});gt.beforeEach((e,t,n)=>{const r=te();if(e.meta.title&&(document.title=`${e.meta.title} - SEO Platform`),e.meta.requiresAuth!==!1&&!r.isLoggedIn){n("/login");return}if(e.path==="/login"&&r.isLoggedIn){n("/");return}n()});const ge=gt;const z=Dt(Nt);for(const[e,t]of Object.entries(Ct))z.component(e,t);z.use(Ft());z.use(ge);z.use(Bt,{locale:xt});z.mount("#app");export{P as s,te as u};
