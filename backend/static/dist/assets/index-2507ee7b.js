import{_ as V}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                     *//* empty css                  *//* empty css                 */import{aC as E,r as p,X as b,y as f,z as h,A as a,Q as s,I as l,H as k,L as z,M as B,a5 as C}from"./vendor-ad470fc0.js";import{u as F}from"./index-24913185.js";import{E as d,s as S,u as I,h as L,v as N}from"./elementPlus-05e8f3ef.js";const R={class:"login-container"},U={class:"login-box"},q={__name:"index",setup(A){const _=E(),v=F(),u=p(),i=p(!1),c=p(!1),o=b({username:"",password:"",code:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],code:[{required:c.value,message:"请输入验证码",trigger:"blur"}]},g=async()=>{if(u.value)try{await u.value.validate(),i.value=!0,await v.login({username:o.username,password:o.password,code:o.code})&&_.push("/")}catch(r){if(r.response)switch(r.response.status){case 401:d.error(r.response.data.message||"账号或密码输入错误");break;case 429:d.error(r.response.data.message||"账户已被锁定，请稍后重试");break;default:d.error("登录失败，请稍后重试")}else d.error("网络错误，请联系管理员"),console.error("Login validation failed:",r)}finally{i.value=!1}};return(r,e)=>{const m=S,n=I,y=L,x=N;return f(),h("div",R,[a("div",U,[e[4]||(e[4]=a("div",{class:"login-header"},[a("h2",null,"SEO Platform"),a("p",null,"SEO优化管理平台")],-1)),s(x,{ref_key:"loginFormRef",ref:u,model:o,rules:w,class:"login-form",onKeyup:C(g,["enter"])},{default:l(()=>[s(n,{prop:"username"},{default:l(()=>[s(m,{modelValue:o.username,"onUpdate:modelValue":e[0]||(e[0]=t=>o.username=t),placeholder:"用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),s(n,{prop:"password"},{default:l(()=>[s(m,{modelValue:o.password,"onUpdate:modelValue":e[1]||(e[1]=t=>o.password=t),type:"password",placeholder:"密码",size:"large","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),c.value?(f(),k(n,{key:0,prop:"code"},{default:l(()=>[s(m,{modelValue:o.code,"onUpdate:modelValue":e[2]||(e[2]=t=>o.code=t),placeholder:"验证码",size:"large","prefix-icon":"Key"},null,8,["modelValue"])]),_:1})):z("",!0),s(n,null,{default:l(()=>[s(y,{type:"primary",size:"large",loading:i.value,onClick:g,class:"login-btn"},{default:l(()=>e[3]||(e[3]=[B(" 登录 ",-1)])),_:1,__:[3]},8,["loading"])]),_:1})]),_:1},8,["model"]),e[5]||(e[5]=a("div",{class:"login-footer"},[a("p",null,"© 2024 SEO Platform. All rights reserved.")],-1))])])}}},X=V(q,[["__scopeId","data-v-e84d7540"]]);export{X as default};
