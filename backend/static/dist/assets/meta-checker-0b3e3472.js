import{_ as j}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                 *//* empty css               *//* empty css                             *//* empty css                   *//* empty css               *//* empty css                     *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css                 *//* empty css                */import{r as L,X as F,y as i,z as u,Q as e,I as s,A as a,u as y,M as o,a5 as A,O as n,H as k,P as C,a4 as T,L as p}from"./vendor-ad470fc0.js";import{E as b,c as P,x as W,s as $,u as H,ag as J,ah as Q,h as X,v as Y,y as Z,A as tt,al as et,ad as st,ae as lt,z as at,a6 as ot,L as I,R as M,aq as nt,ar as it,W as rt}from"./elementPlus-ef333120.js";const ut={class:"meta-checker-container"},dt={class:"header-content"},_t={class:"results-header"},pt={class:"meta-section"},ct={key:0,class:"meta-item"},mt={class:"meta-header"},vt={class:"meta-content"},ft={class:"meta-value"},gt={class:"meta-stats"},yt={key:0,class:"meta-suggestions"},kt={key:1,class:"meta-item"},ht={class:"meta-header"},wt={class:"meta-content"},bt={class:"meta-value"},Et={class:"meta-stats"},xt={key:0,class:"meta-suggestions"},Ct={key:2,class:"meta-item"},Tt={class:"meta-header"},Ot={class:"meta-content"},St={class:"meta-value"},zt={key:0,class:"meta-stats"},It={key:0,class:"social-section"},Vt={key:0,class:"meta-item"},Dt={class:"meta-header"},Rt={class:"meta-content"},Lt={key:1,class:"meta-item"},Mt={class:"meta-header"},Ut={class:"meta-content"},qt={class:"technical-section"},Bt={class:"tech-item"},Kt={class:"tech-item"},Nt={class:"tech-url"},Gt={__name:"meta-checker",setup(jt){const E=L(!1),l=L(null),c=F({url:"",checkItems:["title","description","keywords","robots","canonical","og"]}),O=async()=>{if(!c.url){b.warning("请输入要检查的网页URL");return}E.value=!0;try{const d=await fetch("/api/tools/meta-checker",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:c.url.startsWith("http")?c.url:`https://${c.url}`,check_items:c.checkItems})});if(d.ok)l.value=await d.json(),b.success("Meta标签检查完成");else throw new Error("检查失败")}catch(d){b.error("检查失败: "+d.message),l.value={overall_status:"warning",title:{status:"good",content:"示例网站 - 专业的SEO优化服务平台",length:20,suggestions:["标题长度适中，建议保持"]},description:{status:"warning",content:"这是一个示例网站",length:8,suggestions:["描述过短，建议扩展到120-160字符","添加更多关键词和价值主张"]},keywords:{status:"info",content:"SEO,优化,网站,排名",keywords_list:["SEO","优化","网站","排名"]},robots:{status:"good",content:"index, follow"},canonical:{status:"good",url:"https://example.com/"},open_graph:{status:"warning",title:"示例网站",description:"这是一个示例网站",image:"",url:"https://example.com/"},twitter_card:{status:"error",card:"",title:"",description:"",image:""},quick_fixes:[{type:"description",title:"优化Description标签",description:"当前描述过短，建议扩展内容",code:'<meta name="description" content="专业的SEO优化服务平台，提供网站排名优化、关键词分析、竞争对手分析等全方位SEO解决方案，帮助您的网站获得更好的搜索引擎排名。">'},{type:"og_image",title:"添加Open Graph图片",description:"设置社交分享图片",code:'<meta property="og:image" content="https://example.com/images/og-image.jpg">'}]}}finally{E.value=!1}},m=d=>({good:"success",warning:"warning",error:"danger",info:"info"})[d]||"info",h=d=>({good:"良好",warning:"需优化",error:"有问题",info:"一般"})[d]||"未知",U=d=>m(d),q=async d=>{try{await navigator.clipboard.writeText(d),b.success("代码已复制到剪贴板")}catch{b.error("复制失败")}};return(d,t)=>{const v=P,w=W,B=$,S=H,f=J,K=Q,z=X,N=Y,x=Z,_=tt,V=et,g=st,D=lt,R=at,G=ot;return i(),u("div",ut,[e(w,{class:"page-header"},{default:s(()=>[a("div",dt,[a("h2",null,[e(v,null,{default:s(()=>[e(y(I))]),_:1}),t[2]||(t[2]=o(" Meta标签检查工具",-1))]),t[3]||(t[3]=a("p",null,"检查网页的Meta标签是否符合SEO最佳实践，提供优化建议",-1))])]),_:1}),e(R,{gutter:20},{default:s(()=>[e(x,{span:8},{default:s(()=>[e(w,{class:"check-form"},{header:s(()=>[a("span",null,[e(v,null,{default:s(()=>[e(y(M))]),_:1}),t[4]||(t[4]=o(" 网页检查",-1))])]),default:s(()=>[e(N,{model:c,"label-width":"80px"},{default:s(()=>[e(S,{label:"网页URL"},{default:s(()=>[e(B,{modelValue:c.url,"onUpdate:modelValue":t[0]||(t[0]=r=>c.url=r),placeholder:"请输入要检查的网页URL",onKeyup:A(O,["enter"])},{prepend:s(()=>t[5]||(t[5]=[o("https://",-1)])),_:1},8,["modelValue"])]),_:1}),e(S,{label:"检查项目"},{default:s(()=>[e(K,{modelValue:c.checkItems,"onUpdate:modelValue":t[1]||(t[1]=r=>c.checkItems=r)},{default:s(()=>[e(f,{label:"title"},{default:s(()=>t[6]||(t[6]=[o("Title标签",-1)])),_:1,__:[6]}),e(f,{label:"description"},{default:s(()=>t[7]||(t[7]=[o("Description标签",-1)])),_:1,__:[7]}),e(f,{label:"keywords"},{default:s(()=>t[8]||(t[8]=[o("Keywords标签",-1)])),_:1,__:[8]}),e(f,{label:"robots"},{default:s(()=>t[9]||(t[9]=[o("Robots标签",-1)])),_:1,__:[9]}),e(f,{label:"canonical"},{default:s(()=>t[10]||(t[10]=[o("Canonical标签",-1)])),_:1,__:[10]}),e(f,{label:"og"},{default:s(()=>t[11]||(t[11]=[o("Open Graph标签",-1)])),_:1,__:[11]}),e(f,{label:"twitter"},{default:s(()=>t[12]||(t[12]=[o("Twitter Card标签",-1)])),_:1,__:[12]}),e(f,{label:"schema"},{default:s(()=>t[13]||(t[13]=[o("结构化数据",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1}),e(S,null,{default:s(()=>[e(z,{type:"primary",onClick:O,loading:E.value,style:{width:"100%"}},{default:s(()=>[e(v,null,{default:s(()=>[e(y(M))]),_:1}),o(" "+n(E.value?"检查中...":"开始检查"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),l.value&&l.value.quick_fixes?(i(),k(w,{key:0,class:"quick-fixes"},{header:s(()=>[a("span",null,[e(v,null,{default:s(()=>[e(y(nt))]),_:1}),t[14]||(t[14]=o(" 快速修复",-1))])]),default:s(()=>[(i(!0),u(C,null,T(l.value.quick_fixes,r=>(i(),u("div",{class:"fix-item",key:r.type},[a("h5",null,n(r.title),1),a("p",null,n(r.description),1),e(z,{size:"small",type:"primary",onClick:Ft=>q(r.code)},{default:s(()=>t[15]||(t[15]=[o(" 复制代码 ",-1)])),_:2,__:[15]},1032,["onClick"])]))),128))]),_:1})):p("",!0)]),_:1}),e(x,{span:16},{default:s(()=>[l.value?(i(),k(w,{key:0,class:"check-results"},{header:s(()=>[a("div",_t,[a("span",null,[e(v,null,{default:s(()=>[e(y(I))]),_:1}),t[16]||(t[16]=o(" 检查结果",-1))]),e(_,{type:U(l.value.overall_status)},{default:s(()=>[o(n(l.value.overall_status==="good"?"良好":l.value.overall_status==="warning"?"需优化":"有问题"),1)]),_:1},8,["type"])])]),default:s(()=>[a("div",pt,[a("h4",null,[e(v,null,{default:s(()=>[e(y(I))]),_:1}),t[17]||(t[17]=o(" 基础Meta标签",-1))]),l.value.title?(i(),u("div",ct,[a("div",mt,[t[18]||(t[18]=a("h5",null,"Title标签",-1)),e(_,{type:m(l.value.title.status)},{default:s(()=>[o(n(h(l.value.title.status)),1)]),_:1},8,["type"])]),a("div",vt,[a("p",ft,n(l.value.title.content||"未找到Title标签"),1),a("div",gt,[e(_,{size:"small"},{default:s(()=>[o("长度: "+n(l.value.title.length)+"字符",1)]),_:1}),e(_,{size:"small",type:"info"},{default:s(()=>t[19]||(t[19]=[o("建议: 30-60字符",-1)])),_:1,__:[19]})]),l.value.title.suggestions?(i(),u("div",yt,[t[20]||(t[20]=a("p",null,[a("strong",null,"建议:")],-1)),a("ul",null,[(i(!0),u(C,null,T(l.value.title.suggestions,r=>(i(),u("li",{key:r},n(r),1))),128))])])):p("",!0)])])):p("",!0),l.value.description?(i(),u("div",kt,[a("div",ht,[t[21]||(t[21]=a("h5",null,"Description标签",-1)),e(_,{type:m(l.value.description.status)},{default:s(()=>[o(n(h(l.value.description.status)),1)]),_:1},8,["type"])]),a("div",wt,[a("p",bt,n(l.value.description.content||"未找到Description标签"),1),a("div",Et,[e(_,{size:"small"},{default:s(()=>[o("长度: "+n(l.value.description.length)+"字符",1)]),_:1}),e(_,{size:"small",type:"info"},{default:s(()=>t[22]||(t[22]=[o("建议: 120-160字符",-1)])),_:1,__:[22]})]),l.value.description.suggestions?(i(),u("div",xt,[t[23]||(t[23]=a("p",null,[a("strong",null,"建议:")],-1)),a("ul",null,[(i(!0),u(C,null,T(l.value.description.suggestions,r=>(i(),u("li",{key:r},n(r),1))),128))])])):p("",!0)])])):p("",!0),l.value.keywords?(i(),u("div",Ct,[a("div",Tt,[t[24]||(t[24]=a("h5",null,"Keywords标签",-1)),e(_,{type:m(l.value.keywords.status)},{default:s(()=>[o(n(h(l.value.keywords.status)),1)]),_:1},8,["type"])]),a("div",Ot,[a("p",St,n(l.value.keywords.content||"未找到Keywords标签"),1),l.value.keywords.keywords_list?(i(),u("div",zt,[(i(!0),u(C,null,T(l.value.keywords.keywords_list,r=>(i(),k(_,{key:r,size:"small",style:{"margin-right":"5px","margin-bottom":"5px"}},{default:s(()=>[o(n(r),1)]),_:2},1024))),128))])):p("",!0)])])):p("",!0)]),e(V),l.value.open_graph||l.value.twitter_card?(i(),u("div",It,[a("h4",null,[e(v,null,{default:s(()=>[e(y(it))]),_:1}),t[25]||(t[25]=o(" 社交媒体标签",-1))]),l.value.open_graph?(i(),u("div",Vt,[a("div",Dt,[t[26]||(t[26]=a("h5",null,"Open Graph标签",-1)),e(_,{type:m(l.value.open_graph.status)},{default:s(()=>[o(n(h(l.value.open_graph.status)),1)]),_:1},8,["type"])]),a("div",Rt,[e(D,{column:2,border:"",size:"small"},{default:s(()=>[e(g,{label:"og:title"},{default:s(()=>[o(n(l.value.open_graph.title||"未设置"),1)]),_:1}),e(g,{label:"og:description"},{default:s(()=>[o(n(l.value.open_graph.description||"未设置"),1)]),_:1}),e(g,{label:"og:image"},{default:s(()=>[o(n(l.value.open_graph.image||"未设置"),1)]),_:1}),e(g,{label:"og:url"},{default:s(()=>[o(n(l.value.open_graph.url||"未设置"),1)]),_:1})]),_:1})])])):p("",!0),l.value.twitter_card?(i(),u("div",Lt,[a("div",Mt,[t[27]||(t[27]=a("h5",null,"Twitter Card标签",-1)),e(_,{type:m(l.value.twitter_card.status)},{default:s(()=>[o(n(h(l.value.twitter_card.status)),1)]),_:1},8,["type"])]),a("div",Ut,[e(D,{column:2,border:"",size:"small"},{default:s(()=>[e(g,{label:"twitter:card"},{default:s(()=>[o(n(l.value.twitter_card.card||"未设置"),1)]),_:1}),e(g,{label:"twitter:title"},{default:s(()=>[o(n(l.value.twitter_card.title||"未设置"),1)]),_:1}),e(g,{label:"twitter:description"},{default:s(()=>[o(n(l.value.twitter_card.description||"未设置"),1)]),_:1}),e(g,{label:"twitter:image"},{default:s(()=>[o(n(l.value.twitter_card.image||"未设置"),1)]),_:1})]),_:1})])])):p("",!0)])):p("",!0),e(V),a("div",qt,[a("h4",null,[e(v,null,{default:s(()=>[e(y(rt))]),_:1}),t[28]||(t[28]=o(" 技术标签",-1))]),e(R,{gutter:20},{default:s(()=>[l.value.robots?(i(),k(x,{key:0,span:12},{default:s(()=>[a("div",Bt,[t[29]||(t[29]=a("h6",null,"Robots标签",-1)),e(_,{type:m(l.value.robots.status)},{default:s(()=>[o(n(l.value.robots.content||"未设置"),1)]),_:1},8,["type"])])]),_:1})):p("",!0),l.value.canonical?(i(),k(x,{key:1,span:12},{default:s(()=>[a("div",Kt,[t[30]||(t[30]=a("h6",null,"Canonical标签",-1)),e(_,{type:m(l.value.canonical.status)},{default:s(()=>[o(n(l.value.canonical.status==="good"?"已设置":"未设置"),1)]),_:1},8,["type"]),a("p",Nt,n(l.value.canonical.url),1)])]),_:1})):p("",!0)]),_:1})])]),_:1})):(i(),k(w,{key:1,class:"empty-state"},{default:s(()=>[e(G,{description:"请输入网页URL并开始检查"},{default:s(()=>[e(z,{type:"primary",onClick:O},{default:s(()=>t[31]||(t[31]=[o("开始检查",-1)])),_:1,__:[31]})]),_:1})]),_:1}))]),_:1})]),_:1})])}}},ae=j(Gt,[["__scopeId","data-v-32470308"]]);export{ae as default};
