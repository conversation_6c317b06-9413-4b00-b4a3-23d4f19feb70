import{_ as L}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css               *//* empty css                */import{u as N,i as Y,a as M,b as O,c as F,d as H,e as P,f as V,H as x}from"./charts-5a62b0c7.js";import{w as z,c as K,x as U,y as Q,z as X,h as q,A as G,B as J,C as W}from"./elementPlus-ef333120.js";import{s as h}from"./index-bb3d8812.js";import{r as _,k as Z,al as m,y as tt,z as st,Q as t,I as s,A as e,O as i,u as S,M as g}from"./vendor-ad470fc0.js";const b={getStats(){return h({url:"/dashboard/stats",method:"get"})},getSpiderStats(){return h({url:"/dashboard/spider-stats",method:"get"})},getRecentLogs(w){return h({url:"/dashboard/recent-logs",method:"get",params:w})}};const et={class:"dashboard"},at={class:"stats-content"},ot={class:"stats-icon project"},nt={class:"stats-info"},lt={class:"stats-number"},dt={class:"stats-content"},rt={class:"stats-icon keyword"},it={class:"stats-info"},ct={class:"stats-number"},pt={class:"stats-content"},ut={class:"stats-icon template"},_t={class:"stats-info"},mt={class:"stats-number"},ft={class:"stats-content"},vt={class:"stats-icon spider"},yt={class:"stats-info"},ht={class:"stats-number"},gt={class:"chart-container"},bt={class:"chart-container"},wt={__name:"index",setup(w){N([Y,M,O,F,H,P,V]);const r=_({projectCount:0,keywordCount:0,templateCount:0,todaySpiderCount:0}),f=_([]),v=_({tooltip:{trigger:"axis"},legend:{data:["百度","搜狗","360","谷歌"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"百度",type:"line",data:[]},{name:"搜狗",type:"line",data:[]},{name:"360",type:"line",data:[]},{name:"谷歌",type:"line",data:[]}]}),C=_({tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},series:[{name:"蜘蛛访问",type:"pie",radius:"50%",data:[{value:1048,name:"百度"},{value:735,name:"搜狗"},{value:580,name:"360"},{value:484,name:"谷歌"},{value:300,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),k=o=>({1:"百度",2:"搜狗",3:"360",4:"必应",5:"谷歌",6:"神马",7:"Yandex",8:"Coccoc",9:"Naver"})[o]||"未知",D=o=>({1:"primary",2:"success",3:"warning",4:"info",5:"danger"})[o]||"info",A=o=>z(o).format("YYYY-MM-DD HH:mm:ss"),E=async()=>{try{const o=await b.getStats();o.code===200&&(r.value={projectCount:o.data.project_count,keywordCount:o.data.keyword_count,templateCount:o.data.template_count,todaySpiderCount:o.data.today_spider_count});const a=await b.getSpiderStats();if(a.code===200){const d=a.data.chart_data||[];v.value.xAxis.data=d.map(n=>n.date),v.value.series[0].data=d.map(n=>n.count);const l=a.data.spider_types||[];C.value.series[0].data=l.map(n=>({name:n.name,value:n.count}))}const u=await b.getRecentLogs({limit:10});u.code===200&&(f.value=u.data)}catch(o){console.error("Failed to fetch dashboard data:",o),r.value={projectCount:0,keywordCount:0,templateCount:0,todaySpiderCount:0},f.value=[]}};return Z(()=>{E()}),(o,a)=>{const u=m("Folder"),d=K,l=U,n=Q,T=m("Key"),$=m("Document"),j=m("DataAnalysis"),y=X,R=q,B=G,c=J,I=W;return tt(),st("div",et,[t(y,{gutter:20,class:"stats-row"},{default:s(()=>[t(n,{span:6},{default:s(()=>[t(l,{class:"stats-card"},{default:s(()=>[e("div",at,[e("div",ot,[t(d,null,{default:s(()=>[t(u)]),_:1})]),e("div",nt,[e("div",lt,i(r.value.projectCount||0),1),a[1]||(a[1]=e("div",{class:"stats-label"},"项目总数",-1))])])]),_:1})]),_:1}),t(n,{span:6},{default:s(()=>[t(l,{class:"stats-card"},{default:s(()=>[e("div",dt,[e("div",rt,[t(d,null,{default:s(()=>[t(T)]),_:1})]),e("div",it,[e("div",ct,i(r.value.keywordCount||0),1),a[2]||(a[2]=e("div",{class:"stats-label"},"关键词库",-1))])])]),_:1})]),_:1}),t(n,{span:6},{default:s(()=>[t(l,{class:"stats-card"},{default:s(()=>[e("div",pt,[e("div",ut,[t(d,null,{default:s(()=>[t($)]),_:1})]),e("div",_t,[e("div",mt,i(r.value.templateCount||0),1),a[3]||(a[3]=e("div",{class:"stats-label"},"模板数量",-1))])])]),_:1})]),_:1}),t(n,{span:6},{default:s(()=>[t(l,{class:"stats-card"},{default:s(()=>[e("div",ft,[e("div",vt,[t(d,null,{default:s(()=>[t(j)]),_:1})]),e("div",yt,[e("div",ht,i(r.value.todaySpiderCount||0),1),a[4]||(a[4]=e("div",{class:"stats-label"},"今日蜘蛛",-1))])])]),_:1})]),_:1})]),_:1}),t(y,{gutter:20,class:"charts-row"},{default:s(()=>[t(n,{span:12},{default:s(()=>[t(l,null,{header:s(()=>a[5]||(a[5]=[e("span",null,"蜘蛛访问趋势",-1)])),default:s(()=>[e("div",gt,[t(S(x),{option:v.value,style:{height:"300px"}},null,8,["option"])])]),_:1})]),_:1}),t(n,{span:12},{default:s(()=>[t(l,null,{header:s(()=>a[6]||(a[6]=[e("span",null,"蜘蛛类型分布",-1)])),default:s(()=>[e("div",bt,[t(S(x),{option:C.value,style:{height:"300px"}},null,8,["option"])])]),_:1})]),_:1})]),_:1}),t(y,null,{default:s(()=>[t(n,{span:24},{default:s(()=>[t(l,null,{header:s(()=>[a[8]||(a[8]=e("span",null,"最近访问日志",-1)),t(R,{type:"text",style:{float:"right"},onClick:a[0]||(a[0]=p=>o.$router.push("/spider/logs"))},{default:s(()=>a[7]||(a[7]=[g(" 查看更多 ",-1)])),_:1,__:[7]})]),default:s(()=>[t(I,{data:f.value,style:{width:"100%"}},{default:s(()=>[t(c,{prop:"spider_type",label:"蜘蛛类型",width:"120"},{default:s(({row:p})=>[t(B,{type:D(p.spider_type)},{default:s(()=>[g(i(k(p.spider_type)),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"ip",label:"IP地址",width:"140"}),t(c,{prop:"url",label:"访问URL","show-overflow-tooltip":""}),t(c,{prop:"user_agent",label:"User Agent","show-overflow-tooltip":""}),t(c,{prop:"created_at",label:"访问时间",width:"180"},{default:s(({row:p})=>[g(i(A(p.created_at)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])}}},Lt=L(wt,[["__scopeId","data-v-a8b5842d"]]);export{Lt as default};
