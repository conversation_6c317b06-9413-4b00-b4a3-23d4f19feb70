import{_ as N}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css               *//* empty css                */import{u as Y,i as F,a as M,b as O,c as H,d as P,e as V,f as z,H as S}from"./charts-d5c9e496.js";import{w as K,c as U,x as Q,y as X,z as q,h as G,A as J,B as W,C as Z,D as k,t as A,F as tt}from"./elementPlus-05e8f3ef.js";import{s as y}from"./index-24913185.js";import{r as m,k as at,y as st,z as et,Q as t,I as a,A as s,O as c,u as r,M as g,al as b}from"./vendor-ad470fc0.js";const w={getStats(){return y({url:"/dashboard/stats",method:"get"})},getSpiderStats(){return y({url:"/dashboard/spider-stats",method:"get"})},getRecentLogs(C){return y({url:"/dashboard/recent-logs",method:"get",params:C})}};const ot={class:"dashboard"},nt={class:"stats-content"},lt={class:"stats-icon project"},dt={class:"stats-info"},rt={class:"stats-number"},it={class:"stats-content"},ct={class:"stats-icon keyword"},_t={class:"stats-info"},pt={class:"stats-number"},ut={class:"stats-content"},mt={class:"stats-icon template"},ht={class:"stats-info"},ft={class:"stats-number"},vt={class:"stats-content"},yt={class:"stats-icon spider"},gt={class:"stats-info"},bt={class:"stats-number"},wt={class:"chart-header"},Ct={class:"chart-container"},xt={class:"chart-header"},St={class:"chart-container"},kt={class:"logs-header"},At={class:"logs-title"},Dt={__name:"index",setup(C){Y([F,M,O,H,P,V,z]);const i=m({projectCount:0,keywordCount:0,templateCount:0,todaySpiderCount:0}),h=m([]),f=m({tooltip:{trigger:"axis"},legend:{data:["百度","搜狗","360","谷歌"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"百度",type:"line",data:[]},{name:"搜狗",type:"line",data:[]},{name:"360",type:"line",data:[]},{name:"谷歌",type:"line",data:[]}]}),x=m({tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},series:[{name:"蜘蛛访问",type:"pie",radius:"50%",data:[{value:1048,name:"百度"},{value:735,name:"搜狗"},{value:580,name:"360"},{value:484,name:"谷歌"},{value:300,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),D=o=>({1:"百度",2:"搜狗",3:"360",4:"必应",5:"谷歌",6:"神马",7:"Yandex",8:"Coccoc",9:"Naver"})[o]||"未知",E=o=>({1:"primary",2:"success",3:"warning",4:"info",5:"danger"})[o]||"info",T=o=>K(o).format("YYYY-MM-DD HH:mm:ss"),$=async()=>{try{const o=await w.getStats();o.code===200&&(i.value={projectCount:o.data.project_count,keywordCount:o.data.keyword_count,templateCount:o.data.template_count,todaySpiderCount:o.data.today_spider_count});const e=await w.getSpiderStats();if(e.code===200){const l=e.data.chart_data||[];f.value.xAxis.data=l.map(n=>n.date),f.value.series[0].data=l.map(n=>n.count);const d=e.data.spider_types||[];x.value.series[0].data=d.map(n=>({name:n.name,value:n.count}))}const u=await w.getRecentLogs({limit:10});u.code===200&&(h.value=u.data)}catch(o){console.error("Failed to fetch dashboard data:",o),i.value={projectCount:0,keywordCount:0,templateCount:0,todaySpiderCount:0},h.value=[]}};return at(()=>{$()}),(o,e)=>{const u=b("Folder"),l=U,d=Q,n=X,j=b("Key"),R=b("DataAnalysis"),v=q,B=G,I=J,_=W,L=Z;return st(),et("div",ot,[t(v,{gutter:20,class:"stats-row"},{default:a(()=>[t(n,{span:6},{default:a(()=>[t(d,{class:"stats-card enhanced-card"},{default:a(()=>[s("div",nt,[s("div",lt,[t(l,null,{default:a(()=>[t(u)]),_:1})]),s("div",dt,[s("div",rt,c(i.value.projectCount||0),1),e[1]||(e[1]=s("div",{class:"stats-label"},"项目总数",-1))])])]),_:1})]),_:1}),t(n,{span:6},{default:a(()=>[t(d,{class:"stats-card enhanced-card"},{default:a(()=>[s("div",it,[s("div",ct,[t(l,null,{default:a(()=>[t(j)]),_:1})]),s("div",_t,[s("div",pt,c(i.value.keywordCount||0),1),e[2]||(e[2]=s("div",{class:"stats-label"},"关键词库",-1))])])]),_:1})]),_:1}),t(n,{span:6},{default:a(()=>[t(d,{class:"stats-card enhanced-card"},{default:a(()=>[s("div",ut,[s("div",mt,[t(l,null,{default:a(()=>[t(r(k))]),_:1})]),s("div",ht,[s("div",ft,c(i.value.templateCount||0),1),e[3]||(e[3]=s("div",{class:"stats-label"},"模板数量",-1))])])]),_:1})]),_:1}),t(n,{span:6},{default:a(()=>[t(d,{class:"stats-card enhanced-card"},{default:a(()=>[s("div",vt,[s("div",yt,[t(l,null,{default:a(()=>[t(R)]),_:1})]),s("div",gt,[s("div",bt,c(i.value.todaySpiderCount||0),1),e[4]||(e[4]=s("div",{class:"stats-label"},"今日蜘蛛",-1))])])]),_:1})]),_:1})]),_:1}),t(v,{gutter:24,class:"charts-row"},{default:a(()=>[t(n,{span:12},{default:a(()=>[t(d,{class:"chart-card enhanced-card"},{header:a(()=>[s("div",wt,[t(l,{class:"chart-icon"},{default:a(()=>[t(r(A))]),_:1}),e[5]||(e[5]=s("span",{class:"chart-title"},"蜘蛛访问趋势",-1))])]),default:a(()=>[s("div",Ct,[t(r(S),{option:f.value,style:{height:"320px"}},null,8,["option"])])]),_:1})]),_:1}),t(n,{span:12},{default:a(()=>[t(d,{class:"chart-card enhanced-card"},{header:a(()=>[s("div",xt,[t(l,{class:"chart-icon"},{default:a(()=>[t(r(A))]),_:1}),e[6]||(e[6]=s("span",{class:"chart-title"},"蜘蛛类型分布",-1))])]),default:a(()=>[s("div",St,[t(r(S),{option:x.value,style:{height:"320px"}},null,8,["option"])])]),_:1})]),_:1})]),_:1}),t(v,null,{default:a(()=>[t(n,{span:24},{default:a(()=>[t(d,{class:"logs-card enhanced-card"},{header:a(()=>[s("div",kt,[s("div",At,[t(l,{class:"logs-icon"},{default:a(()=>[t(r(k))]),_:1}),e[7]||(e[7]=s("span",null,"最近访问日志",-1))]),t(B,{class:"btn-gradient",onClick:e[0]||(e[0]=p=>o.$router.push("/spider/logs"))},{default:a(()=>[t(l,null,{default:a(()=>[t(r(tt))]),_:1}),e[8]||(e[8]=g(" 查看更多 ",-1))]),_:1,__:[8]})])]),default:a(()=>[t(L,{data:h.value,style:{width:"100%"}},{default:a(()=>[t(_,{prop:"spider_type",label:"蜘蛛类型",width:"120"},{default:a(({row:p})=>[t(I,{type:E(p.spider_type)},{default:a(()=>[g(c(D(p.spider_type)),1)]),_:2},1032,["type"])]),_:1}),t(_,{prop:"ip",label:"IP地址",width:"140"}),t(_,{prop:"url",label:"访问URL","show-overflow-tooltip":""}),t(_,{prop:"user_agent",label:"User Agent","show-overflow-tooltip":""}),t(_,{prop:"created_at",label:"访问时间",width:"180"},{default:a(({row:p})=>[g(c(T(p.created_at)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])}}},Ht=N(Dt,[["__scopeId","data-v-e89ae087"]]);export{Ht as default};
