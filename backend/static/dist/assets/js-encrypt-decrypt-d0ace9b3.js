import{_ as oe}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                    *//* empty css               *//* empty css                          *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                    *//* empty css                  *//* empty css                     */import{H as ae,E as _,I as se,J as ne,u as re,ag as de,ah as ue,s as ce,c as ie,h as pe,v as me,ai as fe,af as _e,G as ye,A as be,aj as ve,ak as N,a4 as O,al as Ce}from"./elementPlus-05e8f3ef.js";import{r as b,X as $,y as g,z as E,A as p,Q as t,I as o,H as I,M as u,L as S,u as V,O as h}from"./vendor-ad470fc0.js";const ge={class:"page-container"},Ve={class:"page-content"},ke={class:"tool-container"},xe={key:0,class:"result-section"},Se={class:"result-header"},he={class:"result-actions"},we={class:"result-info"},Ue={key:0,class:"result-section"},Be={class:"result-header"},Ee={class:"result-actions"},Ie={class:"result-info"},ze={__name:"js-encrypt-decrypt",setup(Ae){const z=b("encrypt"),w=b(!1),U=b(!1),m=b(""),y=b(""),k=b(!1),B=b(""),s=$({method:"base64",sourceCode:"",obfuscateOptions:["variable-names","string-literals"],customKey:""}),c=$({method:"auto",encryptedCode:"",customKey:""}),H=async()=>{if(!s.sourceCode.trim()){_.warning("请输入要加密的JavaScript代码");return}w.value=!0;try{await new Promise(e=>setTimeout(e,1e3));let l="";switch(s.method){case"base64":l=btoa(unescape(encodeURIComponent(s.sourceCode)));break;case"unicode":l=s.sourceCode.split("").map(e=>"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)).join("");break;case"hex":l=s.sourceCode.split("").map(e=>e.charCodeAt(0).toString(16).padStart(2,"0")).join("");break;case"obfuscate":l=D(s.sourceCode);break;case"minify":l=L(s.sourceCode);break;case"custom":l=M(s.sourceCode,s.customKey);break}m.value=l,_.success("代码加密成功")}catch(l){_.error("加密失败："+l.message)}finally{w.value=!1}},T=async()=>{if(!c.encryptedCode.trim()){_.warning("请输入要解密的JavaScript代码");return}U.value=!0;try{await new Promise(a=>setTimeout(a,1e3));let l="",e="";if(c.method==="auto"){const a=c.encryptedCode.trim();X(a)?(l=decodeURIComponent(escape(atob(a))),e="Base64"):P(a)?(l=a.replace(/\\u[\dA-F]{4}/gi,d=>String.fromCharCode(parseInt(d.replace(/\\u/g,""),16))),e="Unicode"):q(a)?(l=a.match(/.{1,2}/g).map(d=>String.fromCharCode(parseInt(d,16))).join(""),e="Hex"):(l="无法自动识别编码格式",e="未知")}else switch(c.method){case"base64":l=decodeURIComponent(escape(atob(c.encryptedCode))),e="Base64";break;case"unicode":l=c.encryptedCode.replace(/\\u[\dA-F]{4}/gi,a=>String.fromCharCode(parseInt(a.replace(/\\u/g,""),16))),e="Unicode";break;case"hex":l=c.encryptedCode.match(/.{1,2}/g).map(a=>String.fromCharCode(parseInt(a,16))).join(""),e="Hex";break;case"custom":l=Z(c.encryptedCode,c.customKey),e="Custom";break}y.value=l,B.value=e,k.value=l!=="无法自动识别编码格式",_.success("代码解密完成")}catch(l){y.value="解密失败："+l.message,k.value=!1,_.error("解密失败："+l.message)}finally{U.value=!1}},D=l=>{let e=l;return s.obfuscateOptions.includes("variable-names")&&(e=e.replace(/\bvar\s+(\w+)/g,(a,d)=>`var _0x${Math.random().toString(16).substr(2,6)}`)),s.obfuscateOptions.includes("string-literals")&&(e=e.replace(/"([^"]*)"/g,(a,d)=>`"${d.split("").map(r=>"\\x"+r.charCodeAt(0).toString(16).padStart(2,"0")).join("")}"`)),e},L=l=>l.replace(/\/\*[\s\S]*?\*\//g,"").replace(/\/\/.*$/gm,"").replace(/\s+/g," ").trim(),M=(l,e)=>{if(!e)return l;let a="";for(let d=0;d<l.length;d++){const r=l.charCodeAt(d)^e.charCodeAt(d%e.length);a+=String.fromCharCode(r)}return btoa(a)},Z=(l,e)=>{if(!e)return l;try{const a=atob(l);let d="";for(let r=0;r<a.length;r++){const i=a.charCodeAt(r)^e.charCodeAt(r%e.length);d+=String.fromCharCode(i)}return d}catch{return"解密失败：密钥错误"}},X=l=>{try{return btoa(atob(l))===l}catch{return!1}},P=l=>/^(\\u[0-9a-fA-F]{4})+$/.test(l.replace(/\s/g,"")),q=l=>/^[0-9a-fA-F]+$/.test(l.replace(/\s/g,""))&&l.replace(/\s/g,"").length%2===0,A=l=>{if(!l)return"0 B";const e=new Blob([l]).size,a=1024,d=["B","KB","MB"],r=Math.floor(Math.log(e)/Math.log(a));return parseFloat((e/Math.pow(a,r)).toFixed(2))+" "+d[r]},Y=()=>{if(!s.sourceCode||!m.value)return"0%";const l=new Blob([s.sourceCode]).size,e=new Blob([m.value]).size;return((l-e)/l*100).toFixed(1)+"%"},j=async l=>{const e=l==="encrypt"?m.value:y.value;try{await navigator.clipboard.writeText(e),_.success("结果已复制到剪贴板")}catch{_.error("复制失败，请手动复制")}},F=l=>{const e=l==="encrypt"?m.value:y.value,a=l==="encrypt"?"encrypted.js":"decrypted.js",d=new Blob([e],{type:"application/javascript"}),r=URL.createObjectURL(d),i=document.createElement("a");i.href=r,i.download=a,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(r),_.success("文件下载成功")},Q=()=>{s.sourceCode="",m.value=""},W=()=>{c.encryptedCode="",y.value="",k.value=!1,B.value=""},K=l=>{l==="encrypt"?s.sourceCode=`function hello() {
    console.log("Hello, World!");
    var message = "This is a sample JavaScript code";
    return message;
}

hello();`:c.encryptedCode="ZnVuY3Rpb24gaGVsbG8oKSB7CiAgICBjb25zb2xlLmxvZygiSGVsbG8sIFdvcmxkISIpOwogICAgdmFyIG1lc3NhZ2UgPSAiVGhpcyBpcyBhIHNhbXBsZSBKYXZhU2NyaXB0IGNvZGUiOwogICAgcmV0dXJuIG1lc3NhZ2U7Cn0KCmhlbGxvKCk7"};return(l,e)=>{const a=se,d=ne,r=re,i=de,ee=ue,v=ce,C=ie,f=pe,R=me,x=fe,G=_e,J=ye,te=be,le=ae;return g(),E("div",ge,[e[33]||(e[33]=p("div",{class:"page-header"},[p("h2",null,"JS 加密 or 解密"),p("p",{class:"page-description"},"JavaScript代码加密和解密工具，支持多种加密算法和混淆方式")],-1)),p("div",Ve,[p("div",ke,[t(le,{modelValue:z.value,"onUpdate:modelValue":e[15]||(e[15]=n=>z.value=n),class:"main-tabs"},{default:o(()=>[t(J,{label:"JS加密",name:"encrypt"},{default:o(()=>[t(R,{model:s,"label-width":"120px",class:"encrypt-form"},{default:o(()=>[t(r,{label:"加密方式"},{default:o(()=>[t(d,{modelValue:s.method,"onUpdate:modelValue":e[0]||(e[0]=n=>s.method=n),placeholder:"选择加密方式"},{default:o(()=>[t(a,{label:"Base64编码",value:"base64"}),t(a,{label:"Unicode编码",value:"unicode"}),t(a,{label:"Hex编码",value:"hex"}),t(a,{label:"代码混淆",value:"obfuscate"}),t(a,{label:"压缩混淆",value:"minify"}),t(a,{label:"自定义加密",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),s.method==="obfuscate"?(g(),I(r,{key:0,label:"混淆选项"},{default:o(()=>[t(ee,{modelValue:s.obfuscateOptions,"onUpdate:modelValue":e[1]||(e[1]=n=>s.obfuscateOptions=n)},{default:o(()=>[t(i,{label:"variable-names"},{default:o(()=>e[16]||(e[16]=[u("变量名混淆",-1)])),_:1,__:[16]}),t(i,{label:"function-names"},{default:o(()=>e[17]||(e[17]=[u("函数名混淆",-1)])),_:1,__:[17]}),t(i,{label:"string-literals"},{default:o(()=>e[18]||(e[18]=[u("字符串混淆",-1)])),_:1,__:[18]}),t(i,{label:"control-flow"},{default:o(()=>e[19]||(e[19]=[u("控制流混淆",-1)])),_:1,__:[19]}),t(i,{label:"dead-code"},{default:o(()=>e[20]||(e[20]=[u("死代码注入",-1)])),_:1,__:[20]})]),_:1},8,["modelValue"])]),_:1})):S("",!0),s.method==="custom"?(g(),I(r,{key:1,label:"自定义密钥"},{default:o(()=>[t(v,{modelValue:s.customKey,"onUpdate:modelValue":e[2]||(e[2]=n=>s.customKey=n),placeholder:"输入自定义加密密钥"},null,8,["modelValue"])]),_:1})):S("",!0),t(r,{label:"源代码",required:""},{default:o(()=>[t(v,{modelValue:s.sourceCode,"onUpdate:modelValue":e[3]||(e[3]=n=>s.sourceCode=n),type:"textarea",rows:12,placeholder:"请输入要加密的JavaScript代码...",class:"code-input"},null,8,["modelValue"])]),_:1}),t(r,null,{default:o(()=>[t(f,{type:"primary",onClick:H,loading:w.value,size:"large"},{default:o(()=>[t(C,null,{default:o(()=>[t(V(ve))]),_:1}),e[21]||(e[21]=u(" 开始加密 ",-1))]),_:1,__:[21]},8,["loading"]),t(f,{onClick:Q},{default:o(()=>e[22]||(e[22]=[u("清空",-1)])),_:1,__:[22]}),t(f,{onClick:e[4]||(e[4]=n=>K("encrypt"))},{default:o(()=>e[23]||(e[23]=[u("加载示例",-1)])),_:1,__:[23]})]),_:1})]),_:1},8,["model"]),m.value?(g(),E("div",xe,[p("div",Se,[e[26]||(e[26]=p("h4",null,"加密结果",-1)),p("div",he,[t(f,{type:"primary",size:"small",onClick:e[5]||(e[5]=n=>j("encrypt"))},{default:o(()=>[t(C,null,{default:o(()=>[t(V(N))]),_:1}),e[24]||(e[24]=u(" 复制结果 ",-1))]),_:1,__:[24]}),t(f,{type:"success",size:"small",onClick:e[6]||(e[6]=n=>F("encrypt"))},{default:o(()=>[t(C,null,{default:o(()=>[t(V(O))]),_:1}),e[25]||(e[25]=u(" 下载文件 ",-1))]),_:1,__:[25]})])]),t(v,{modelValue:m.value,"onUpdate:modelValue":e[7]||(e[7]=n=>m.value=n),type:"textarea",rows:12,readonly:"",class:"result-textarea"},null,8,["modelValue"]),p("div",we,[t(G,{column:3,size:"small"},{default:o(()=>[t(x,{label:"原始大小"},{default:o(()=>[u(h(A(s.sourceCode)),1)]),_:1}),t(x,{label:"加密后大小"},{default:o(()=>[u(h(A(m.value)),1)]),_:1}),t(x,{label:"压缩比例"},{default:o(()=>[u(h(Y()),1)]),_:1})]),_:1})])])):S("",!0)]),_:1}),t(J,{label:"JS解密",name:"decrypt"},{default:o(()=>[t(R,{model:c,"label-width":"120px",class:"decrypt-form"},{default:o(()=>[t(r,{label:"解密方式"},{default:o(()=>[t(d,{modelValue:c.method,"onUpdate:modelValue":e[8]||(e[8]=n=>c.method=n),placeholder:"选择解密方式"},{default:o(()=>[t(a,{label:"Base64解码",value:"base64"}),t(a,{label:"Unicode解码",value:"unicode"}),t(a,{label:"Hex解码",value:"hex"}),t(a,{label:"自动检测",value:"auto"}),t(a,{label:"自定义解密",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),c.method==="custom"?(g(),I(r,{key:0,label:"自定义密钥"},{default:o(()=>[t(v,{modelValue:c.customKey,"onUpdate:modelValue":e[9]||(e[9]=n=>c.customKey=n),placeholder:"输入解密密钥"},null,8,["modelValue"])]),_:1})):S("",!0),t(r,{label:"加密代码",required:""},{default:o(()=>[t(v,{modelValue:c.encryptedCode,"onUpdate:modelValue":e[10]||(e[10]=n=>c.encryptedCode=n),type:"textarea",rows:12,placeholder:"请输入要解密的JavaScript代码...",class:"code-input"},null,8,["modelValue"])]),_:1}),t(r,null,{default:o(()=>[t(f,{type:"primary",onClick:T,loading:U.value,size:"large"},{default:o(()=>[t(C,null,{default:o(()=>[t(V(Ce))]),_:1}),e[27]||(e[27]=u(" 开始解密 ",-1))]),_:1,__:[27]},8,["loading"]),t(f,{onClick:W},{default:o(()=>e[28]||(e[28]=[u("清空",-1)])),_:1,__:[28]}),t(f,{onClick:e[11]||(e[11]=n=>K("decrypt"))},{default:o(()=>e[29]||(e[29]=[u("加载示例",-1)])),_:1,__:[29]})]),_:1})]),_:1},8,["model"]),y.value?(g(),E("div",Ue,[p("div",Be,[e[32]||(e[32]=p("h4",null,"解密结果",-1)),p("div",Ee,[t(f,{type:"primary",size:"small",onClick:e[12]||(e[12]=n=>j("decrypt"))},{default:o(()=>[t(C,null,{default:o(()=>[t(V(N))]),_:1}),e[30]||(e[30]=u(" 复制结果 ",-1))]),_:1,__:[30]}),t(f,{type:"success",size:"small",onClick:e[13]||(e[13]=n=>F("decrypt"))},{default:o(()=>[t(C,null,{default:o(()=>[t(V(O))]),_:1}),e[31]||(e[31]=u(" 下载文件 ",-1))]),_:1,__:[31]})])]),t(v,{modelValue:y.value,"onUpdate:modelValue":e[14]||(e[14]=n=>y.value=n),type:"textarea",rows:12,readonly:"",class:"result-textarea"},null,8,["modelValue"]),p("div",Ie,[t(G,{column:2,size:"small"},{default:o(()=>[t(x,{label:"解密状态"},{default:o(()=>[t(te,{type:k.value?"success":"danger"},{default:o(()=>[u(h(k.value?"解密成功":"解密失败"),1)]),_:1},8,["type"])]),_:1}),t(x,{label:"检测到的编码"},{default:o(()=>[u(h(B.value),1)]),_:1})]),_:1})])])):S("",!0)]),_:1})]),_:1},8,["modelValue"])])])])}}},Le=oe(ze,[["__scopeId","data-v-e67a9361"]]);export{Le as default};
