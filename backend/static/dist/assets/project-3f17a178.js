import{s as e}from"./index-24913185.js";const s={getList(t){return e({url:"/projects",method:"get",params:t})},create(t){return e({url:"/projects",method:"post",data:t})},get(t){return e({url:`/projects/${t}`,method:"get"})},update(t,r){return e({url:`/projects/${t}`,method:"put",data:r})},delete(t){return e({url:`/projects/${t}`,method:"delete"})},sync(t){return e({url:`/projects/${t}/sync`,method:"post"})},getStats(t){return e({url:`/projects/${t}/stats`,method:"get"})},batchDelete(t){return e({url:"/projects/batch/delete",method:"post",data:{ids:t}})},batchUpdateStatus(t,r){return e({url:"/projects/batch/status",method:"post",data:{ids:t,status:r}})},getExtendedInfo(t){return e({url:`/projects/${t}/extended`,method:"get"})}};export{s as p};
