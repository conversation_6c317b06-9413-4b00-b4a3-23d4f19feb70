import{_ as x}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                  *//* empty css                 */import{E as l,c as I,s as k,h as U,W as E,aC as L,a8 as V}from"./elementPlus-ef333120.js";import{r as _,y as f,z as b,A as a,Q as o,I as i,H as $,L as v,u as R,M as c}from"./vendor-ad470fc0.js";const h={class:"iis-generator"},B={class:"breadcrumb"},z={class:"generator-container"},G={class:"input-area"},S={class:"action-area"},A={class:"result-area"},N={class:"area-header"},j={key:0,class:"result-actions"},q={__name:"iis6-generator",setup(O){const d=_(!1),u=_(""),s=_(""),g=async()=>{if(!u.value.trim()){l.warning("请输入域名列表");return}d.value=!0;try{await new Promise(t=>setTimeout(t,1500));const r=u.value.split(`
`).filter(t=>t.trim());let e=`# IIS 6 ISAPI Rewrite2 Configuration
`;e+=`# Generated by SEO Platform

`,r.forEach((t,p)=>{const n=t.trim();n&&(e+=`# Redirect rule for ${n}
`,e+=`RewriteCond Host: ^(www\\.)?${n.replace(".","\\.")}$
`,e+=`RewriteCond User-Agent: .*(googlebot|bingbot|baiduspider).*
`,e+=`RewriteRule (.*) http://target-domain.com/$1 [R=301,L]

`)}),e+=`# General hijack rules for search engines
`,e+=`RewriteCond User-Agent: .*(googlebot|bingbot|baiduspider|sogou|360spider).*
`,e+=`RewriteRule ^(.*)$ http://seo-target.com/$1 [R=301,L]

`,e+=`# Default redirect for other traffic
`,e+=`RewriteCond Request_URI: !^/(admin|api|static).*
`,e+=`RewriteRule ^(.*)$ http://default-target.com/$1 [R=301,L]

`,e+=`# Block direct access to rewrite rules
`,e+=`RewriteCond Request_URI: .*\\.ini$
`,e+=`RewriteRule .* - [F]

`,e+=`# Error handling
`,e+=`RewriteCond Request_URI: .*404.*
`,e+=`RewriteRule .* /404.asp [L]
`,s.value=e,l.success(`成功生成 ${r.length} 个域名的IIS 6重写规则`)}catch{l.error("生成失败，请重试")}finally{d.value=!1}},w=()=>{s.value="",l.info("结果已清空")},y=async()=>{try{await navigator.clipboard.writeText(s.value),l.success("代码已复制到剪贴板")}catch{l.error("复制失败，请手动复制")}},C=()=>{const r=new Blob([s.value],{type:"text/plain"}),e=URL.createObjectURL(r),t=document.createElement("a");t.href=e,t.download="httpd.ini",document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(e),l.success("文件下载成功")};return(r,e)=>{const t=I,p=k,n=U;return f(),b("div",h,[a("div",B,[o(t,{class:"breadcrumb-icon"},{default:i(()=>[o(R(E))]),_:1}),e[2]||(e[2]=a("span",{class:"breadcrumb-text"},"辅助工具",-1)),e[3]||(e[3]=a("span",{class:"breadcrumb-separator"},">",-1)),e[4]||(e[4]=a("span",{class:"breadcrumb-current"},"IS6 Rewrite2 站群劫持代码生成",-1))]),a("div",z,[a("div",G,[e[5]||(e[5]=a("div",{class:"area-label"},"域名列表:",-1)),o(p,{modelValue:u.value,"onUpdate:modelValue":e[0]||(e[0]=m=>u.value=m),type:"textarea",rows:20,placeholder:"域名列表,一行表示一条域名",class:"domain-textarea"},null,8,["modelValue"])]),a("div",S,[o(n,{type:"primary",onClick:g,loading:d.value,class:"generate-btn",size:"large"},{default:i(()=>e[6]||(e[6]=[c(" 下一步 > ",-1)])),_:1,__:[6]},8,["loading"])]),a("div",A,[a("div",N,[e[8]||(e[8]=a("span",{class:"area-label"},"生成结果:",-1)),s.value?(f(),$(n,{key:0,type:"text",size:"small",onClick:w,class:"clear-btn"},{default:i(()=>e[7]||(e[7]=[c(" [清空结果] ",-1)])),_:1,__:[7]})):v("",!0)]),o(p,{modelValue:s.value,"onUpdate:modelValue":e[1]||(e[1]=m=>s.value=m),type:"textarea",rows:20,readonly:"",class:"result-textarea",placeholder:d.value?"正在生成...":s.value?"":"等待生成..."},null,8,["modelValue","placeholder"]),s.value?(f(),b("div",j,[o(n,{onClick:y,size:"small"},{default:i(()=>[o(t,null,{default:i(()=>[o(R(L))]),_:1}),e[9]||(e[9]=c(" 复制代码 ",-1))]),_:1,__:[9]}),o(n,{onClick:C,size:"small"},{default:i(()=>[o(t,null,{default:i(()=>[o(R(V))]),_:1}),e[10]||(e[10]=c(" 下载文件 ",-1))]),_:1,__:[10]})])):v("",!0)])])])}}},F=x(q,[["__scopeId","data-v-8567174f"]]);export{F as default};
