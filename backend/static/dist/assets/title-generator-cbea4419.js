import{_ as re}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                             *//* empty css               *//* empty css                 *//* empty css                   *//* empty css               *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css                          *//* empty css                    *//* empty css                  *//* empty css                */import{r as S,X as ie,y as g,z as w,Q as e,I as l,A as i,u as x,M as n,O as u,H as R,P as L,a4 as j,C as ue,V as O,L as I}from"./vendor-ad470fc0.js";import{E as b,c as de,x as _e,s as ce,u as me,G as pe,H as ye,ag as fe,ah as ve,as as ge,h as be,v as we,y as xe,A as ke,at as Ee,al as Ve,a6 as Ce,z as Se,ad as Te,ae as he,ac as $e,an as Oe,a4 as D,af as Ue,au as ze,a3 as Ke,a9 as Re}from"./elementPlus-ef333120.js";const Le={class:"title-generator-container"},je={class:"header-content"},Ie={class:"results-header"},De={class:"titles-list"},Be=["onClick"],Ne={class:"title-content"},qe={class:"title-text"},Ge={class:"title-number"},Ae={class:"title-main"},Fe={class:"title-stats"},Me={class:"title-actions"},He={key:0,class:"batch-actions"},Pe={key:0},Je={style:{"margin-top":"20px"}},Qe={__name:"title-generator",setup(Xe){const T=S(!1),f=S([]),m=S([]),$=S(!1),p=S(null),a=ie({mainKeyword:"",relatedKeywords:"",industry:"",contentType:"",styles:["how_to","number"],count:10}),U=async()=>{if(!a.mainKeyword){b.warning("请输入主要关键词");return}T.value=!0;try{const o=await fetch("/api/tools/title-generator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({main_keyword:a.mainKeyword,related_keywords:a.relatedKeywords.split(",").map(t=>t.trim()).filter(t=>t),industry:a.industry,content_type:a.contentType,styles:a.styles,count:a.count})});if(o.ok)f.value=await o.json(),m.value=[],b.success("标题生成完成");else throw new Error("生成失败")}catch(o){b.error("生成失败: "+o.message),f.value=[{text:`如何通过${a.mainKeyword}提升网站排名：10个实用技巧`,style:"how_to",length:25,seo_score:85,rating:4},{text:`2024年${a.mainKeyword}完全指南：从入门到精通`,style:"number",length:22,seo_score:78,rating:4},{text:`${a.mainKeyword}vs传统方法：哪种更有效？`,style:"comparison",length:18,seo_score:72,rating:3},{text:`为什么${a.mainKeyword}是提升业务的关键？`,style:"question",length:19,seo_score:80,rating:4},{text:`立即掌握${a.mainKeyword}：5分钟快速入门`,style:"urgent",length:20,seo_score:75,rating:3}]}finally{T.value=!1}},B=o=>{const t=m.value.indexOf(o);t>-1?m.value.splice(t,1):m.value.push(o)},N=async o=>{try{await navigator.clipboard.writeText(o),b.success("标题已复制到剪贴板")}catch{b.error("复制失败")}},q=async()=>{const o=m.value.map(t=>f.value[t].text);try{await navigator.clipboard.writeText(o.join(`
`)),b.success(`已复制${o.length}个标题`)}catch{b.error("复制失败")}},G=()=>{const o=f.value.map((y,_)=>`${_+1}. ${y.text} (${y.style}, SEO评分: ${y.seo_score})`).join(`
`),t=new Blob([o],{type:"text/plain"}),d=URL.createObjectURL(t),c=document.createElement("a");c.href=d,c.download=`标题生成结果_${new Date().toISOString().slice(0,10)}.txt`,c.click(),URL.revokeObjectURL(d)},A=()=>{const t=m.value.map(_=>{const r=f.value[_];return`${_+1}. ${r.text} (${r.style}, SEO评分: ${r.seo_score})`}).join(`
`),d=new Blob([t],{type:"text/plain"}),c=URL.createObjectURL(d),y=document.createElement("a");y.href=c,y.download=`选中标题_${new Date().toISOString().slice(0,10)}.txt`,y.click(),URL.revokeObjectURL(c)},F=()=>{m.value=[]},M=o=>{b.info("编辑功能开发中...")},H=o=>{p.value={title:o.text,length:o.length,seo_score:o.seo_score,keyword_density:2.5,sentiment:"positive",suggestions:["标题长度适中，有利于搜索引擎展示","包含主要关键词，SEO友好","建议在社交媒体分享时添加相关话题标签"]},$.value=!0},P=(o,t)=>{f.value[o].rating=t},J=o=>({question:"primary",number:"success",how_to:"warning",urgent:"danger",benefit:"info",comparison:"primary"})[o]||"default",Q=o=>({question:"疑问式",number:"数字式",how_to:"教程式",urgent:"紧迫式",benefit:"利益式",comparison:"对比式"})[o]||o,X=o=>({positive:"success",neutral:"info",negative:"warning"})[o]||"info",W=o=>({positive:"积极",neutral:"中性",negative:"消极"})[o]||o;return(o,t)=>{const d=de,c=_e,y=ce,_=me,r=pe,z=ye,k=fe,Y=ve,Z=ge,v=be,ee=we,K=xe,C=ke,te=Ee,le=Ve,oe=Ce,se=Se,h=Te,ae=he,ne=$e;return g(),w("div",Le,[e(c,{class:"page-header"},{default:l(()=>[i("div",je,[i("h2",null,[e(d,null,{default:l(()=>[e(x(Oe))]),_:1}),t[7]||(t[7]=n(" 文章标题生成器",-1))]),t[8]||(t[8]=i("p",null,"基于关键词和行业特点，智能生成吸引人的SEO友好标题",-1))])]),_:1}),e(se,{gutter:20},{default:l(()=>[e(K,{span:10},{default:l(()=>[e(c,{class:"generator-form"},{header:l(()=>[i("span",null,[e(d,null,{default:l(()=>[e(x(D))]),_:1}),t[9]||(t[9]=n(" 标题生成",-1))])]),default:l(()=>[e(ee,{model:a,"label-width":"100px"},{default:l(()=>[e(_,{label:"主要关键词"},{default:l(()=>[e(y,{modelValue:a.mainKeyword,"onUpdate:modelValue":t[0]||(t[0]=s=>a.mainKeyword=s),placeholder:"输入主要关键词"},null,8,["modelValue"])]),_:1}),e(_,{label:"相关关键词"},{default:l(()=>[e(y,{modelValue:a.relatedKeywords,"onUpdate:modelValue":t[1]||(t[1]=s=>a.relatedKeywords=s),placeholder:"输入相关关键词，用逗号分隔"},null,8,["modelValue"]),t[10]||(t[10]=i("small",null,"例如: SEO优化,网站排名,搜索引擎",-1))]),_:1,__:[10]}),e(_,{label:"行业类型"},{default:l(()=>[e(z,{modelValue:a.industry,"onUpdate:modelValue":t[2]||(t[2]=s=>a.industry=s),placeholder:"选择行业类型",style:{width:"100%"}},{default:l(()=>[e(r,{label:"科技/互联网",value:"tech"}),e(r,{label:"电商/零售",value:"ecommerce"}),e(r,{label:"教育/培训",value:"education"}),e(r,{label:"医疗/健康",value:"health"}),e(r,{label:"金融/投资",value:"finance"}),e(r,{label:"旅游/生活",value:"travel"}),e(r,{label:"房产/建筑",value:"realestate"}),e(r,{label:"汽车/交通",value:"automotive"}),e(r,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"内容类型"},{default:l(()=>[e(z,{modelValue:a.contentType,"onUpdate:modelValue":t[3]||(t[3]=s=>a.contentType=s),placeholder:"选择内容类型",style:{width:"100%"}},{default:l(()=>[e(r,{label:"教程指南",value:"tutorial"}),e(r,{label:"产品评测",value:"review"}),e(r,{label:"新闻资讯",value:"news"}),e(r,{label:"案例分析",value:"case_study"}),e(r,{label:"对比分析",value:"comparison"}),e(r,{label:"问题解答",value:"qa"}),e(r,{label:"列表总结",value:"list"}),e(r,{label:"深度分析",value:"analysis"})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"标题风格"},{default:l(()=>[e(Y,{modelValue:a.styles,"onUpdate:modelValue":t[4]||(t[4]=s=>a.styles=s)},{default:l(()=>[e(k,{label:"question"},{default:l(()=>t[11]||(t[11]=[n("疑问式",-1)])),_:1,__:[11]}),e(k,{label:"number"},{default:l(()=>t[12]||(t[12]=[n("数字式",-1)])),_:1,__:[12]}),e(k,{label:"how_to"},{default:l(()=>t[13]||(t[13]=[n("教程式",-1)])),_:1,__:[13]}),e(k,{label:"urgent"},{default:l(()=>t[14]||(t[14]=[n("紧迫式",-1)])),_:1,__:[14]}),e(k,{label:"benefit"},{default:l(()=>t[15]||(t[15]=[n("利益式",-1)])),_:1,__:[15]}),e(k,{label:"comparison"},{default:l(()=>t[16]||(t[16]=[n("对比式",-1)])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"生成数量"},{default:l(()=>[e(Z,{modelValue:a.count,"onUpdate:modelValue":t[5]||(t[5]=s=>a.count=s),min:5,max:20,"show-stops":"","show-input":"",style:{"margin-top":"10px"}},null,8,["modelValue"])]),_:1}),e(_,null,{default:l(()=>[e(v,{type:"primary",onClick:U,loading:T.value,style:{width:"100%"}},{default:l(()=>[e(d,null,{default:l(()=>[e(x(D))]),_:1}),n(" "+u(T.value?"生成中...":"生成标题"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(K,{span:14},{default:l(()=>[f.value&&f.value.length>0?(g(),R(c,{key:0,class:"generator-results"},{header:l(()=>[i("div",Ie,[i("span",null,[e(d,null,{default:l(()=>[e(x(Ue))]),_:1}),t[17]||(t[17]=n(" 生成结果",-1))]),e(v,{size:"small",onClick:G},{default:l(()=>t[18]||(t[18]=[n("导出标题",-1)])),_:1,__:[18]})])]),default:l(()=>[i("div",De,[(g(!0),w(L,null,j(f.value,(s,E)=>(g(),w("div",{key:E,class:ue(["title-item",{selected:m.value.includes(E)}]),onClick:V=>B(E)},[i("div",Ne,[i("div",qe,[i("span",Ge,u(E+1)+".",1),i("span",Ae,u(s.text),1),e(C,{type:J(s.style),size:"small",style:{"margin-left":"10px"}},{default:l(()=>[n(u(Q(s.style)),1)]),_:2},1032,["type"])]),i("div",Fe,[e(C,{size:"small",type:"info"},{default:l(()=>[n(" 长度: "+u(s.length)+"字符 ",1)]),_:2},1024),e(C,{size:"small",type:s.seo_score>=80?"success":s.seo_score>=60?"warning":"danger"},{default:l(()=>[n(" SEO评分: "+u(s.seo_score),1)]),_:2},1032,["type"]),e(te,{modelValue:s.rating,"onUpdate:modelValue":V=>s.rating=V,size:"small",onChange:V=>P(E,s.rating)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),i("div",Me,[e(v,{size:"small",onClick:O(V=>N(s.text),["stop"])},{default:l(()=>[e(d,null,{default:l(()=>[e(x(ze))]),_:1}),t[19]||(t[19]=n(" 复制 ",-1))]),_:2,__:[19]},1032,["onClick"]),e(v,{size:"small",onClick:O(V=>M(E),["stop"])},{default:l(()=>[e(d,null,{default:l(()=>[e(x(Ke))]),_:1}),t[20]||(t[20]=n(" 编辑 ",-1))]),_:2,__:[20]},1032,["onClick"]),e(v,{size:"small",onClick:O(V=>H(s),["stop"])},{default:l(()=>[e(d,null,{default:l(()=>[e(x(Re))]),_:1}),t[21]||(t[21]=n(" 分析 ",-1))]),_:2,__:[21]},1032,["onClick"])])])],10,Be))),128))]),m.value.length>0?(g(),w("div",He,[e(le),i("p",null,"已选择 "+u(m.value.length)+" 个标题",1),e(v,{size:"small",onClick:q},{default:l(()=>t[22]||(t[22]=[n("批量复制",-1)])),_:1,__:[22]}),e(v,{size:"small",onClick:A},{default:l(()=>t[23]||(t[23]=[n("导出选中",-1)])),_:1,__:[23]}),e(v,{size:"small",onClick:F},{default:l(()=>t[24]||(t[24]=[n("清除选择",-1)])),_:1,__:[24]})])):I("",!0)]),_:1})):(g(),R(c,{key:1,class:"empty-state"},{default:l(()=>[e(oe,{description:"请输入关键词并生成标题"},{default:l(()=>[e(v,{type:"primary",onClick:U},{default:l(()=>t[25]||(t[25]=[n("生成标题",-1)])),_:1,__:[25]})]),_:1})]),_:1}))]),_:1})]),_:1}),e(ne,{modelValue:$.value,"onUpdate:modelValue":t[6]||(t[6]=s=>$.value=s),title:"标题分析",width:"600px"},{default:l(()=>[p.value?(g(),w("div",Pe,[i("h4",null,u(p.value.title),1),e(ae,{column:2,border:""},{default:l(()=>[e(h,{label:"字符长度"},{default:l(()=>[n(u(p.value.length)+"字符 ",1)]),_:1}),e(h,{label:"SEO评分"},{default:l(()=>[e(C,{type:p.value.seo_score>=80?"success":"warning"},{default:l(()=>[n(u(p.value.seo_score)+"/100 ",1)]),_:1},8,["type"])]),_:1}),e(h,{label:"关键词密度"},{default:l(()=>[n(u(p.value.keyword_density)+"% ",1)]),_:1}),e(h,{label:"情感倾向"},{default:l(()=>[e(C,{type:X(p.value.sentiment)},{default:l(()=>[n(u(W(p.value.sentiment)),1)]),_:1},8,["type"])]),_:1})]),_:1}),i("div",Je,[t[26]||(t[26]=i("h5",null,"优化建议:",-1)),i("ul",null,[(g(!0),w(L,null,j(p.value.suggestions,s=>(g(),w("li",{key:s},u(s),1))),128))])])])):I("",!0)]),_:1},8,["modelValue"])])}}},vt=re(Qe,[["__scopeId","data-v-76b817b3"]]);export{vt as default};
