import{_ as O}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                   *//* empty css                   *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                *//* empty css                  */import{aC as G,r as y,X as x,c as Q,k as X,y as I,z as q,A as o,Q as t,I as a,u,O as r,M as i,a5 as W,J as Z,H as ee}from"./vendor-ad470fc0.js";import{a as C}from"./api-c21033af.js";import{w as te,a as ae,E as se,c as oe,h as le,s as ne,x as ie,B as re,A as de,C as pe,J as ce,ad as ue,ae as _e,ac as me,K as fe,_ as ge,M as P,N as ve,R as T,S as ye}from"./elementPlus-ef333120.js";import"./index-bb3d8812.js";const he={class:"page-container"},be={class:"page-header"},we={class:"header-content"},Ce={class:"header-left"},ke={class:"header-icon"},Ee={class:"header-text"},ze={class:"breadcrumb"},xe={class:"header-stats"},Ie={class:"stat-item"},Pe={class:"stat-value"},Te={class:"stat-item"},Ae={class:"stat-value"},Se={class:"page-content"},De={class:"table-toolbar"},Ve={class:"toolbar-left"},Be={class:"toolbar-right"},Me={class:"search-group"},$e={class:"pagination-container"},Ue={class:"test-result"},Re={class:"dialog-footer"},Le={__name:"list",setup(Fe){const A=G(),h=y(!1),b=y([]),f=y(!1),_=y({}),g=x({keyword:""}),n=x({page:1,pageSize:10,total:0}),S=s=>({GET:"success",POST:"primary",PUT:"warning",DELETE:"danger"})[s]||"info",D=s=>s>=200&&s<300?"success":s>=300&&s<400?"warning":s>=400?"danger":"info",V=s=>te(s).format("YYYY-MM-DD HH:mm:ss"),m=async()=>{h.value=!0;try{const s={page:n.page,page_size:n.pageSize,keyword:g.keyword},e=await C.getList(s);e.code===200&&(b.value=e.data.list,n.total=e.data.total)}catch(s){console.error("Failed to fetch APIs:",s)}finally{h.value=!1}},k=()=>{n.page=1,m()},B=()=>{g.keyword="",n.page=1,m()},M=s=>{n.pageSize=s,n.page=1,m()},$=s=>{n.page=s,m()},U=s=>{A.push(`/api/edit/${s.id}`)},R=async s=>{try{const e=await C.test(s.id);e.code===200&&(_.value=e.data,f.value=!0)}catch(e){console.error("Failed to test API:",e)}},L=s=>{ae.confirm(`确定要删除API "${s.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{(await C.delete(s.id)).code===200&&(se.success("删除成功"),m())}catch(e){console.error("Failed to delete API:",e)}})},F=Q(()=>b.value.filter(s=>s.status===1).length);return X(()=>{m()}),(s,e)=>{const d=oe,p=le,N=ne,E=ie,c=re,v=de,Y=pe,H=ce,w=ue,K=_e,j=me,J=fe;return I(),q("div",he,[o("div",be,[o("div",we,[o("div",Ce,[o("div",ke,[t(d,null,{default:a(()=>[t(u(ge))]),_:1})]),o("div",Ee,[e[8]||(e[8]=o("h2",null,"API管理",-1)),o("div",ze,[t(d,null,{default:a(()=>[t(u(P))]),_:1}),e[6]||(e[6]=o("span",null,"系统管理",-1)),t(d,null,{default:a(()=>[t(u(P))]),_:1}),e[7]||(e[7]=o("span",null,"API接口管理",-1))])])]),o("div",xe,[o("div",Ie,[o("div",Pe,r(n.total),1),e[9]||(e[9]=o("div",{class:"stat-label"},"总API数",-1))]),o("div",Te,[o("div",Ae,r(F.value),1),e[10]||(e[10]=o("div",{class:"stat-label"},"启用中",-1))])])])]),o("div",Se,[t(E,{class:"toolbar-card"},{default:a(()=>[o("div",De,[o("div",Ve,[t(p,{type:"primary",class:"action-btn",onClick:e[0]||(e[0]=l=>s.$router.push("/api/create"))},{default:a(()=>[t(d,null,{default:a(()=>[t(u(ve))]),_:1}),e[11]||(e[11]=i(" 新建API ",-1))]),_:1,__:[11]})]),o("div",Be,[o("div",Me,[t(N,{modelValue:g.keyword,"onUpdate:modelValue":e[1]||(e[1]=l=>g.keyword=l),placeholder:"搜索API名称或URL",class:"search-input",clearable:"",onKeyup:W(k,["enter"])},{prefix:a(()=>[t(d,null,{default:a(()=>[t(u(T))]),_:1})]),_:1},8,["modelValue"]),t(p,{type:"primary",class:"search-btn",onClick:k},{default:a(()=>[t(d,null,{default:a(()=>[t(u(T))]),_:1}),e[12]||(e[12]=i(" 搜索 ",-1))]),_:1,__:[12]}),t(p,{class:"reset-btn",onClick:B},{default:a(()=>[t(d,null,{default:a(()=>[t(u(ye))]),_:1}),e[13]||(e[13]=i(" 重置 ",-1))]),_:1,__:[13]})])])])]),_:1}),t(E,{class:"table-card"},{default:a(()=>[Z((I(),ee(Y,{data:b.value,style:{width:"100%"}},{default:a(()=>[t(c,{prop:"name",label:"API名称","min-width":"150"}),t(c,{prop:"method",label:"方法",width:"80"},{default:a(({row:l})=>[t(v,{type:S(l.method),size:"small"},{default:a(()=>[i(r(l.method),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"url",label:"URL","min-width":"200","show-overflow-tooltip":""}),t(c,{prop:"description",label:"描述","show-overflow-tooltip":""}),t(c,{prop:"status",label:"状态",width:"80"},{default:a(({row:l})=>[t(v,{type:l.status===1?"success":"danger"},{default:a(()=>[i(r(l.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"created_at",label:"创建时间",width:"180"},{default:a(({row:l})=>[i(r(V(l.created_at)),1)]),_:1}),t(c,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[t(p,{type:"text",size:"small",onClick:z=>U(l)},{default:a(()=>e[14]||(e[14]=[i(" 编辑 ",-1)])),_:2,__:[14]},1032,["onClick"]),t(p,{type:"text",size:"small",onClick:z=>R(l)},{default:a(()=>e[15]||(e[15]=[i(" 测试 ",-1)])),_:2,__:[15]},1032,["onClick"]),t(p,{type:"text",size:"small",onClick:z=>L(l),style:{color:"#f56c6c"}},{default:a(()=>e[16]||(e[16]=[i(" 删除 ",-1)])),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,h.value]]),o("div",$e,[t(H,{"current-page":n.page,"onUpdate:currentPage":e[2]||(e[2]=l=>n.page=l),"page-size":n.pageSize,"onUpdate:pageSize":e[3]||(e[3]=l=>n.pageSize=l),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:M,onCurrentChange:$},null,8,["current-page","page-size","total"])])]),_:1})]),t(j,{modelValue:f.value,"onUpdate:modelValue":e[5]||(e[5]=l=>f.value=l),title:"API测试结果",width:"600px"},{footer:a(()=>[o("span",Re,[t(p,{onClick:e[4]||(e[4]=l=>f.value=!1)},{default:a(()=>e[17]||(e[17]=[i("关闭",-1)])),_:1,__:[17]})])]),default:a(()=>[o("div",Ue,[t(K,{column:2,border:""},{default:a(()=>[t(w,{label:"状态"},{default:a(()=>[t(v,{type:_.value.status==="success"?"success":"danger"},{default:a(()=>[i(r(_.value.status),1)]),_:1},8,["type"])]),_:1}),t(w,{label:"响应时间"},{default:a(()=>[i(r(_.value.response_time),1)]),_:1}),t(w,{label:"状态码"},{default:a(()=>[t(v,{type:D(_.value.status_code)},{default:a(()=>[i(r(_.value.status_code),1)]),_:1},8,["type"])]),_:1})]),_:1})])]),_:1},8,["modelValue"])])}}},lt=O(Le,[["__scopeId","data-v-8ee0c193"]]);export{lt as default};
