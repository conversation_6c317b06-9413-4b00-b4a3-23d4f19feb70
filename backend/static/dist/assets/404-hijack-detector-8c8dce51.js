import{_ as I}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                    *//* empty css               *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                 */import{E as g,c as L,s as N,J as A,h as B,A as D,H as M,I as O,y as q,z as H,G as P,X as J}from"./elementPlus-05e8f3ef.js";import{r,y as _,z as p,A as e,Q as t,I as a,P as F,a4 as G,L as E,u as Q,C as X,O as i,M as k}from"./vendor-ad470fc0.js";const $={class:"referer-simulator"},K={class:"breadcrumb"},W={class:"simulator-container"},Y={class:"search-engine-list"},Z=["onClick"],ee={class:"operation-area"},se={class:"input-section"},te={class:"controls"},ae={key:0,class:"waiting-status"},le={key:1,class:"result-area"},oe={class:"result-header"},ne={class:"result-status"},ie={class:"result-content"},re={class:"request-info"},ue={class:"info-item"},ce={class:"info-value"},de={class:"info-item"},_e={class:"info-value"},pe={class:"info-item"},ve={class:"info-value"},me={class:"info-item"},fe={class:"info-value"},be={class:"headers-content"},ge={class:"page-content-area"},ye={__name:"404-hijack-detector",setup(he){const u=r(!1),v=r("sogou"),c=r(""),m=r("desktop"),y=r("request"),l=r(null),V=[{label:"搜狗搜索",value:"sogou"},{label:"百度搜索",value:"baidu"},{label:"神马SM",value:"shenma"},{label:"360搜索",value:"360"}],w=o=>{v.value=o},T=async()=>{if(!c.value.trim()){g.warning("请输入要模拟的URL");return}u.value=!0;try{const o=await fetch("/api/tools/404-hijack-detector",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:c.value,engine:v.value,device:m.value})});if(o.ok)l.value=await o.json(),g.success("检测完成");else throw new Error("检测失败")}catch{g.error("检测失败，请检查URL是否正确")}finally{u.value=!1}},U=o=>o>=200&&o<300?"success":o>=300&&o<400?"warning":o>=400?"danger":"info";return(o,s)=>{const x=L,h=N,f=O,R=A,C=B,z=D,d=q,S=H,b=P,j=M;return _(),p("div",$,[e("div",K,[t(x,{class:"breadcrumb-icon"},{default:a(()=>[t(Q(J))]),_:1}),s[4]||(s[4]=e("span",{class:"breadcrumb-text"},"辅助工具",-1)),s[5]||(s[5]=e("span",{class:"breadcrumb-separator"},">",-1)),s[6]||(s[6]=e("span",{class:"breadcrumb-current"},"Referer模拟",-1))]),e("div",W,[e("div",Y,[(_(),p(F,null,G(V,n=>e("div",{key:n.value,class:X(["engine-item",{active:v.value===n.value}]),onClick:Ee=>w(n.value)},i(n.label),11,Z)),64))]),e("div",ee,[e("div",se,[t(h,{modelValue:c.value,"onUpdate:modelValue":s[0]||(s[0]=n=>c.value=n),placeholder:"Referer 模拟请求地址...",class:"url-input",size:"large"},null,8,["modelValue"]),e("div",te,[t(R,{modelValue:m.value,"onUpdate:modelValue":s[1]||(s[1]=n=>m.value=n),class:"device-select",size:"large"},{default:a(()=>[t(f,{label:"Desktop",value:"desktop"}),t(f,{label:"Mobile",value:"mobile"}),t(f,{label:"UC浏览器",value:"uc"})]),_:1},8,["modelValue"]),t(C,{type:"primary",onClick:T,loading:u.value,class:"simulate-btn",size:"large"},{default:a(()=>s[7]||(s[7]=[k(" 转到一下 ",-1)])),_:1,__:[7]},8,["loading"])])]),u.value?(_(),p("div",ae," 等待模拟... ")):E("",!0),l.value?(_(),p("div",le,[e("div",oe,[s[8]||(s[8]=e("h3",null,"模拟结果",-1)),e("div",ne,[t(z,{type:U(l.value.statusCode),size:"large"},{default:a(()=>[k(i(l.value.statusCode)+" "+i(l.value.statusText),1)]),_:1},8,["type"])])]),e("div",ie,[t(j,{modelValue:y.value,"onUpdate:modelValue":s[3]||(s[3]=n=>y.value=n),class:"result-tabs"},{default:a(()=>[t(b,{label:"请求信息",name:"request"},{default:a(()=>[e("div",re,[t(S,{gutter:20},{default:a(()=>[t(d,{span:12},{default:a(()=>[e("div",ue,[s[9]||(s[9]=e("span",{class:"info-label"},"目标URL:",-1)),e("span",ce,i(l.value.targetUrl),1)])]),_:1}),t(d,{span:12},{default:a(()=>[e("div",de,[s[10]||(s[10]=e("span",{class:"info-label"},"Referer:",-1)),e("span",_e,i(l.value.referer),1)])]),_:1}),t(d,{span:12},{default:a(()=>[e("div",pe,[s[11]||(s[11]=e("span",{class:"info-label"},"User-Agent:",-1)),e("span",ve,i(l.value.userAgent),1)])]),_:1}),t(d,{span:12},{default:a(()=>[e("div",me,[s[12]||(s[12]=e("span",{class:"info-label"},"响应时间:",-1)),e("span",fe,i(l.value.responseTime)+"ms",1)])]),_:1})]),_:1})])]),_:1}),t(b,{label:"响应头",name:"headers"},{default:a(()=>[e("div",be,[e("pre",null,i(l.value.headers),1)])]),_:1}),t(b,{label:"页面内容",name:"content"},{default:a(()=>[e("div",ge,[t(h,{modelValue:l.value.content,"onUpdate:modelValue":s[2]||(s[2]=n=>l.value.content=n),type:"textarea",rows:20,readonly:"",class:"content-textarea"},null,8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])])])):E("",!0)])])])}}},je=I(ye,[["__scopeId","data-v-0f31ed39"]]);export{je as default};
