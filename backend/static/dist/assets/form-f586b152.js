import{_ as Q}from"./_plugin-vue_export-helper-183d9f20.js";/* empty css                *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                     *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  */import{aB as X,aC as Y,r as f,c as W,X as Z,k as ee,y as F,z as E,A as s,Q as e,I as a,u,O as h,M as i,P as le,a4 as ae}from"./vendor-ad470fc0.js";import{p as x}from"./project-3f17a178.js";import{E as j,c as te,h as oe,I as se,J as re,u as ne,s as de,V as ie,v as ue,G as pe,W as me,H as _e,x as fe,X as w,Y as ce,D as be,Z as T,_ as O,O as ge,S as Ve,$ as ve,a0 as he,a1 as ye,a2 as ke,R as xe,Q as je}from"./elementPlus-05e8f3ef.js";import"./index-24913185.js";const we={class:"project-form-page"},Ue={class:"page-header"},Re={class:"header-content"},Fe={class:"header-left"},Ee={class:"header-text"},Te={class:"page-content"},Oe={class:"form-container"},Ce={class:"card-header"},De={class:"tab-label"},Ie={class:"form-row"},Se={class:"switch-container"},Be={class:"switch-label"},Le={class:"form-tip enhanced-tip"},qe={class:"tab-label"},Pe={class:"form-section"},Ke={class:"tdk-buttons"},Me={class:"tab-label"},Ne={class:"tab-label"},$e={class:"tab-label"},ze={class:"tab-label"},Ge={class:"tab-label"},He={class:"tab-label"},Ae={class:"form-actions"},Je={__name:"form",setup(Qe){const y=X(),C=Y(),U=f("basic"),k=f(!1),R=f(),D=f(),I=f(),S=f(),B=f(),L=f(),q=f(),P=f(),V=W(()=>!!y.params.id),K=[{label:"Title",value:"title"},{label:"Keyword",value:"keyword"},{label:"Description",value:"description"}],t=Z({basic:{project_type:"",project_name:"新的项目",project_url:"http://www.example.com/",description:"",monitoring:!1,encoding:"utf-8"},seo:{title_template:"",keywords_template:"",description_template:"",tdk_method:"",tdk_range:[],article_library:"",title_library:"",image_library:""},internal:{quick_rule:"",url_rule:""},external:{external_type:"",custom_external:""},cache:{cache_enabled:!0,cache_method:"tkdb",cache_period:0},ads:{region_block:!1,blocked_regions:"",ads_js:"",error_page:""},push:{shenma_push:!1,shenma_token:"",baidu_push:!1,baidu_token:""},other:{redirect_error:!1,simulate_error:!1,website_error:!1}}),M={project_type:[{required:!0,message:"请选择项目类型",trigger:"change"}],project_name:[{required:!0,message:"请输入项目标识",trigger:"blur"}],project_url:[{required:!0,message:"请输入项目地址",trigger:"blur"}]},N=p=>{const l=t.seo.tdk_range.indexOf(p);l>-1?t.seo.tdk_range.splice(l,1):t.seo.tdk_range.push(p)},$=()=>{j.info("测试URL功能开发中...")},z=async()=>{var p;try{await((p=R.value)==null?void 0:p.validate()),k.value=!0;const l={project_name:t.basic.project_name,project_url:t.basic.project_url,project_type:t.basic.project_type,description:t.basic.description,monitoring:t.basic.monitoring,encoding:t.basic.encoding,seo_config:t.seo,internal_config:t.internal,external_config:t.external,cache_config:t.cache,ads_config:t.ads,push_config:t.push,other_config:t.other};let d;V.value?d=await x.update(y.params.id,l):d=await x.create(l),d.code===200&&(j.success(V.value?"更新成功":"创建成功"),C.push("/project"))}catch(l){console.error("Submit failed:",l),j.error("提交失败，请检查表单数据")}finally{k.value=!1}},G=async()=>{if(V.value)try{const p=await x.get(y.params.id);if(p.code===200){const l=p.data;Object.assign(t.basic,{project_name:l.project_name,project_url:l.project_url,project_type:l.project_type,description:l.description,monitoring:l.monitoring,encoding:l.encoding}),l.seo_config&&Object.assign(t.seo,l.seo_config),l.internal_config&&Object.assign(t.internal,l.internal_config),l.external_config&&Object.assign(t.external,l.external_config),l.cache_config&&Object.assign(t.cache,l.cache_config),l.ads_config&&Object.assign(t.ads,l.ads_config),l.push_config&&Object.assign(t.push,l.push_config),l.other_config&&Object.assign(t.other,l.other_config)}}catch(p){console.error("Failed to fetch project:",p)}};return ee(()=>{G()}),(p,l)=>{const d=te,v=oe,n=se,m=re,r=ne,_=de,c=ie,b=ue,g=pe,H=me,A=_e,J=fe;return F(),E("div",we,[s("div",Ue,[s("div",Re,[s("div",Fe,[e(d,{class:"header-icon"},{default:a(()=>[e(u(w))]),_:1}),s("div",Ee,[s("h1",null,h(V.value?"编辑项目":"创建单例项目"),1),l[33]||(l[33]=s("p",null,"你只管做，其他的交给我",-1))])]),e(v,{class:"back-btn",onClick:l[0]||(l[0]=o=>p.$router.go(-1))},{default:a(()=>[e(d,null,{default:a(()=>[e(u(ce))]),_:1}),l[34]||(l[34]=i(" 返回 ",-1))]),_:1,__:[34]})])]),s("div",Te,[s("div",Oe,[e(J,{class:"form-card"},{header:a(()=>[s("div",Ce,[e(d,{class:"card-icon"},{default:a(()=>[e(u(be))]),_:1}),l[35]||(l[35]=i(" 项目配置 ",-1))])]),default:a(()=>[e(A,{modelValue:U.value,"onUpdate:modelValue":l[32]||(l[32]=o=>U.value=o),class:"project-tabs"},{default:a(()=>[e(g,{label:"基础信息",name:"basic"},{label:a(()=>[s("span",De,[e(d,null,{default:a(()=>[e(u(T))]),_:1}),l[36]||(l[36]=i(" 基础信息 ",-1))])]),default:a(()=>[e(b,{ref_key:"basicFormRef",ref:R,model:t.basic,rules:M,"label-width":"120px",class:"tab-form enhanced-form"},{default:a(()=>[s("div",Ie,[e(r,{label:"项目类型",prop:"project_type",class:"enhanced-form-item"},{default:a(()=>[e(m,{modelValue:t.basic.project_type,"onUpdate:modelValue":l[1]||(l[1]=o=>t.basic.project_type=o),placeholder:"请选择",class:"input-enhanced"},{default:a(()=>[e(n,{label:"泛目录",value:1}),e(n,{label:"寄生虫",value:2}),e(n,{label:"URL劫持",value:3}),e(n,{label:"404劫持",value:4})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"网站编码",prop:"encoding",class:"enhanced-form-item"},{default:a(()=>[e(m,{modelValue:t.basic.encoding,"onUpdate:modelValue":l[2]||(l[2]=o=>t.basic.encoding=o),placeholder:"请选择",class:"input-enhanced"},{default:a(()=>[e(n,{label:"UTF-8",value:"utf-8"}),e(n,{label:"GBK",value:"gbk"}),e(n,{label:"GB2312",value:"gb2312"})]),_:1},8,["modelValue"])]),_:1})]),e(r,{label:"项目标识",prop:"project_name",class:"enhanced-form-item"},{default:a(()=>[e(_,{modelValue:t.basic.project_name,"onUpdate:modelValue":l[3]||(l[3]=o=>t.basic.project_name=o),placeholder:"新的项目",class:"input-enhanced"},{prefix:a(()=>[e(d,{class:"input-icon"},{default:a(()=>[e(u(T))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"项目地址",prop:"project_url",class:"enhanced-form-item"},{default:a(()=>[e(_,{modelValue:t.basic.project_url,"onUpdate:modelValue":l[4]||(l[4]=o=>t.basic.project_url=o),placeholder:"http://www.example.com/",class:"input-enhanced"},{prefix:a(()=>[e(d,{class:"input-icon"},{default:a(()=>[e(u(O))]),_:1})]),append:a(()=>[e(v,{onClick:$,class:"btn-gradient"},{default:a(()=>[e(d,null,{default:a(()=>[e(u(ge))]),_:1}),l[37]||(l[37]=i(" 测试 ",-1))]),_:1,__:[37]})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"项目备注",prop:"description",class:"enhanced-form-item"},{default:a(()=>[e(_,{modelValue:t.basic.description,"onUpdate:modelValue":l[5]||(l[5]=o=>t.basic.description=o),type:"textarea",rows:4,placeholder:"请输入项目备注",class:"textarea-enhanced"},null,8,["modelValue"])]),_:1}),e(r,{label:"项目监控",prop:"monitoring",class:"enhanced-form-item switch-item"},{default:a(()=>[s("div",Se,[e(c,{modelValue:t.basic.monitoring,"onUpdate:modelValue":l[6]||(l[6]=o=>t.basic.monitoring=o),"active-text":"开启","inactive-text":"关闭",class:"enhanced-switch"},null,8,["modelValue"]),s("div",Be,h(t.basic.monitoring?"监控已开启":"监控已关闭"),1)]),s("div",Le,[e(d,null,{default:a(()=>[e(u(w))]),_:1}),l[38]||(l[38]=i(' 开启项目监控后，在"其他设置"选项中配置监控的内容 ',-1))])]),_:1})]),_:1},8,["model"])]),_:1}),e(g,{label:"SEO选项",name:"seo"},{label:a(()=>[s("span",qe,[e(d,null,{default:a(()=>[e(u(Ve))]),_:1}),l[39]||(l[39]=i(" SEO选项 ",-1))])]),default:a(()=>[e(b,{ref_key:"seoFormRef",ref:D,model:t.seo,"label-width":"120px",class:"tab-form"},{default:a(()=>[l[41]||(l[41]=s("div",{class:"form-section"},[s("h4",null,"词库")],-1)),s("div",Pe,[l[40]||(l[40]=s("h4",null,"模板",-1)),e(r,{label:"标题",prop:"title_template"},{default:a(()=>[e(m,{modelValue:t.seo.title_template,"onUpdate:modelValue":l[7]||(l[7]=o=>t.seo.title_template=o),placeholder:"可选择多个标题样式 程序随机调用"},{default:a(()=>[e(n,{label:"标题模板1",value:"template1"}),e(n,{label:"标题模板2",value:"template2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"关键词",prop:"keywords_template"},{default:a(()=>[e(m,{modelValue:t.seo.keywords_template,"onUpdate:modelValue":l[8]||(l[8]=o=>t.seo.keywords_template=o),placeholder:"可选择多个关键词样式 程序随机调用"},{default:a(()=>[e(n,{label:"关键词模板1",value:"template1"}),e(n,{label:"关键词模板2",value:"template2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"描述",prop:"description_template"},{default:a(()=>[e(m,{modelValue:t.seo.description_template,"onUpdate:modelValue":l[9]||(l[9]=o=>t.seo.description_template=o),placeholder:"可选择多个描述样式 程序随机调用"},{default:a(()=>[e(n,{label:"描述模板1",value:"template1"}),e(n,{label:"描述模板2",value:"template2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"TDK方式",prop:"tdk_method"},{default:a(()=>[e(m,{modelValue:t.seo.tdk_method,"onUpdate:modelValue":l[10]||(l[10]=o=>t.seo.tdk_method=o),placeholder:"请选择"},{default:a(()=>[e(n,{label:"方式1",value:"method1"}),e(n,{label:"方式2",value:"method2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"TDK范围",prop:"tdk_range"},{default:a(()=>[s("div",Ke,[(F(),E(le,null,ae(K,o=>e(v,{key:o.value,type:t.seo.tdk_range.includes(o.value)?"primary":"default",onClick:Xe=>N(o.value)},{default:a(()=>[i(h(o.label),1)]),_:2},1032,["type","onClick"])),64))])]),_:1}),e(r,{label:"文章库",prop:"article_library"},{default:a(()=>[e(m,{modelValue:t.seo.article_library,"onUpdate:modelValue":l[11]||(l[11]=o=>t.seo.article_library=o),placeholder:"请选择"},{default:a(()=>[e(n,{label:"文章库1",value:"library1"}),e(n,{label:"文章库2",value:"library2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"标题库",prop:"title_library"},{default:a(()=>[e(m,{modelValue:t.seo.title_library,"onUpdate:modelValue":l[12]||(l[12]=o=>t.seo.title_library=o),placeholder:"请选择"},{default:a(()=>[e(n,{label:"标题库1",value:"library1"}),e(n,{label:"标题库2",value:"library2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"图片",prop:"image_library"},{default:a(()=>[e(m,{modelValue:t.seo.image_library,"onUpdate:modelValue":l[13]||(l[13]=o=>t.seo.image_library=o),placeholder:"请选择"},{default:a(()=>[e(n,{label:"图片库1",value:"library1"}),e(n,{label:"图片库2",value:"library2"})]),_:1},8,["modelValue"])]),_:1})])]),_:1,__:[41]},8,["model"])]),_:1}),e(g,{label:"内链设置",name:"internal"},{label:a(()=>[s("span",Me,[e(d,null,{default:a(()=>[e(u(O))]),_:1}),l[42]||(l[42]=i(" 内链设置 ",-1))])]),default:a(()=>[e(b,{ref_key:"internalFormRef",ref:I,model:t.internal,"label-width":"120px",class:"tab-form"},{default:a(()=>[e(r,{label:"快速规则",prop:"quick_rule"},{default:a(()=>[e(m,{modelValue:t.internal.quick_rule,"onUpdate:modelValue":l[14]||(l[14]=o=>t.internal.quick_rule=o),placeholder:"请选择"},{default:a(()=>[e(n,{label:"规则1",value:"rule1"}),e(n,{label:"规则2",value:"rule2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"URL规则",prop:"url_rule"},{default:a(()=>[e(_,{modelValue:t.internal.url_rule,"onUpdate:modelValue":l[15]||(l[15]=o=>t.internal.url_rule=o),type:"textarea",rows:8,placeholder:"一行代表一个规则 标签参照下方提示"},null,8,["modelValue"])]),_:1}),e(r,{label:"标签提示"},{default:a(()=>l[43]||(l[43]=[s("div",{class:"tag-tips"},[s("p",null,[s("strong",null,"字符标签："),i("{数字}、{字母}、{大写字母}、{大小写字母}、{大写字母数字}、{大小写字母数字}、{数字字母}、{随机字符}")]),s("p",null,[s("strong",null,"时间标签："),i("{日期}、{年}、{月}、{日}、{时}、{分}、{秒}")]),s("p",null,[s("strong",null,"动态标签："),i("数字和字母标签后面加数字表位数，如：{数字8}表示8个数字、{数字1-8}范围1-8个数字")])],-1)])),_:1,__:[43]})]),_:1},8,["model"])]),_:1}),e(g,{label:"外链设置",name:"external"},{label:a(()=>[s("span",Ne,[e(d,null,{default:a(()=>[e(u(ve))]),_:1}),l[44]||(l[44]=i(" 外链设置 ",-1))])]),default:a(()=>[e(b,{ref_key:"externalFormRef",ref:S,model:t.external,"label-width":"120px",class:"tab-form"},{default:a(()=>[e(r,{label:"外链类别",prop:"external_type"},{default:a(()=>[e(m,{modelValue:t.external.external_type,"onUpdate:modelValue":l[16]||(l[16]=o=>t.external.external_type=o),placeholder:"请选择"},{default:a(()=>[e(n,{label:"类别1",value:"type1"}),e(n,{label:"类别2",value:"type2"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"自定义外链",prop:"custom_external"},{default:a(()=>[e(_,{modelValue:t.external.custom_external,"onUpdate:modelValue":l[17]||(l[17]=o=>t.external.custom_external=o),type:"textarea",rows:8,placeholder:"一行代表一个规则"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),e(g,{label:"缓存设置",name:"cache"},{label:a(()=>[s("span",$e,[e(d,null,{default:a(()=>[e(u(he))]),_:1}),l[45]||(l[45]=i(" 缓存设置 ",-1))])]),default:a(()=>[e(b,{ref_key:"cacheFormRef",ref:B,model:t.cache,"label-width":"120px",class:"tab-form"},{default:a(()=>[e(r,{label:"缓存开关",prop:"cache_enabled"},{default:a(()=>[e(c,{modelValue:t.cache.cache_enabled,"onUpdate:modelValue":l[18]||(l[18]=o=>t.cache.cache_enabled=o),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),l[46]||(l[46]=s("div",{class:"form-tip"}," 缓存关闭后页面将动态更新 ",-1))]),_:1,__:[46]}),e(r,{label:"缓存方法",prop:"cache_method"},{default:a(()=>[e(m,{modelValue:t.cache.cache_method,"onUpdate:modelValue":l[19]||(l[19]=o=>t.cache.cache_method=o),placeholder:"请选择缓存方法"},{default:a(()=>[e(n,{label:"缓存TKDB",value:"tkdb"}),e(n,{label:"文件缓存",value:"file"}),e(n,{label:"内存缓存",value:"memory"})]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"缓存周期",prop:"cache_period"},{default:a(()=>[e(H,{modelValue:t.cache.cache_period,"onUpdate:modelValue":l[20]||(l[20]=o=>t.cache.cache_period=o),min:0,style:{width:"200px"}},null,8,["modelValue"]),l[47]||(l[47]=s("span",{class:"form-tip",style:{"margin-left":"10px"}}," 单位：天 如果大于缓存有效期则会强制删除缓存 ",-1))]),_:1,__:[47]})]),_:1},8,["model"])]),_:1}),e(g,{label:"广告设置",name:"ads"},{label:a(()=>[s("span",ze,[e(d,null,{default:a(()=>[e(u(ye))]),_:1}),l[48]||(l[48]=i(" 广告设置 ",-1))])]),default:a(()=>[e(b,{ref_key:"adsFormRef",ref:L,model:t.ads,"label-width":"120px",class:"tab-form"},{default:a(()=>[e(r,{label:"地区屏蔽",prop:"region_block"},{default:a(()=>[e(c,{modelValue:t.ads.region_block,"onUpdate:modelValue":l[21]||(l[21]=o=>t.ads.region_block=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),l[49]||(l[49]=s("div",{class:"form-tip"}," 开启后会在跳转的页面进行地区判断 ",-1))]),_:1,__:[49]}),e(r,{label:"地区",prop:"blocked_regions"},{default:a(()=>[e(_,{modelValue:t.ads.blocked_regions,"onUpdate:modelValue":l[22]||(l[22]=o=>t.ads.blocked_regions=o),placeholder:"请输入被屏蔽的地区 如北京|上海等"},null,8,["modelValue"]),l[50]||(l[50]=s("div",{class:"form-tip"}," 被屏蔽的地区会显示404错误页面 ",-1))]),_:1,__:[50]}),e(r,{label:"广告JS",prop:"ads_js"},{default:a(()=>[e(_,{modelValue:t.ads.ads_js,"onUpdate:modelValue":l[23]||(l[23]=o=>t.ads.ads_js=o),placeholder:"https://www.example.com/advertise.js"},null,8,["modelValue"])]),_:1}),e(r,{label:"错误页",prop:"error_page"},{default:a(()=>[e(_,{modelValue:t.ads.error_page,"onUpdate:modelValue":l[24]||(l[24]=o=>t.ads.error_page=o),type:"textarea",rows:8,placeholder:"<!DOCTYPE HTML PUBLIC '-//IETF//DTD HTML 2.0//EN'>..."},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),e(g,{label:"推送设置",name:"push"},{label:a(()=>[s("span",Ge,[e(d,null,{default:a(()=>[e(u(ke))]),_:1}),l[51]||(l[51]=i(" 推送设置 ",-1))])]),default:a(()=>[e(b,{ref_key:"pushFormRef",ref:q,model:t.push,"label-width":"120px",class:"tab-form"},{default:a(()=>[e(r,{label:"神马推送",prop:"shenma_push"},{default:a(()=>[e(c,{modelValue:t.push.shenma_push,"onUpdate:modelValue":l[25]||(l[25]=o=>t.push.shenma_push=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),l[52]||(l[52]=s("div",{class:"form-tip"}," 把URL提交给神马搜索引擎，加快收录 ",-1))]),_:1,__:[52]}),e(r,{label:"神马Token",prop:"shenma_token"},{default:a(()=>[e(_,{modelValue:t.push.shenma_token,"onUpdate:modelValue":l[26]||(l[26]=o=>t.push.shenma_token=o),placeholder:"神马推送token 需在神马站长平台获取"},null,8,["modelValue"])]),_:1}),e(r,{label:"百度推送",prop:"baidu_push"},{default:a(()=>[e(c,{modelValue:t.push.baidu_push,"onUpdate:modelValue":l[27]||(l[27]=o=>t.push.baidu_push=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),l[53]||(l[53]=s("div",{class:"form-tip"}," 把URL提交给百度搜索引擎，加快收录 ",-1))]),_:1,__:[53]}),e(r,{label:"百度Token",prop:"baidu_token"},{default:a(()=>[e(_,{modelValue:t.push.baidu_token,"onUpdate:modelValue":l[28]||(l[28]=o=>t.push.baidu_token=o),placeholder:"百度推送token 需在百度站长平台获取"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),e(g,{label:"其他设置",name:"other"},{label:a(()=>[s("span",He,[e(d,null,{default:a(()=>[e(u(w))]),_:1}),l[54]||(l[54]=i(" 其他设置 ",-1))])]),default:a(()=>[e(b,{ref_key:"otherFormRef",ref:P,model:t.other,"label-width":"120px",class:"tab-form"},{default:a(()=>[e(r,{label:"跳转异常",prop:"redirect_error"},{default:a(()=>[e(c,{modelValue:t.other.redirect_error,"onUpdate:modelValue":l[29]||(l[29]=o=>t.other.redirect_error=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),l[55]||(l[55]=s("div",{class:"form-tip"},' 模拟跳转，获取返回结果 如有异常将会推送已配置好的"消息通知" ',-1))]),_:1,__:[55]}),e(r,{label:"模拟异常",prop:"simulate_error"},{default:a(()=>[e(c,{modelValue:t.other.simulate_error,"onUpdate:modelValue":l[30]||(l[30]=o=>t.other.simulate_error=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),l[56]||(l[56]=s("div",{class:"form-tip"},' 模拟抓取，获取返回结果 如有异常将会推送已配置好的"消息通知" ',-1))]),_:1,__:[56]}),e(r,{label:"网站异常",prop:"website_error"},{default:a(()=>[e(c,{modelValue:t.other.website_error,"onUpdate:modelValue":l[31]||(l[31]=o=>t.other.website_error=o),"active-text":"关闭","inactive-text":"开启"},null,8,["modelValue"]),l[57]||(l[57]=s("div",{class:"form-tip"},' 网站异常，如 502 404 或域名错误将会推送已配置好的"消息通知" ',-1))]),_:1,__:[57]})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"]),s("div",Ae,[e(v,{class:"cancel-btn",size:"large",onClick:p.handleCancel},{default:a(()=>[e(d,null,{default:a(()=>[e(u(xe))]),_:1}),l[58]||(l[58]=i(" 取消 ",-1))]),_:1,__:[58]},8,["onClick"]),e(v,{class:"submit-btn",type:"primary",size:"large",onClick:z,loading:k.value},{default:a(()=>[e(d,null,{default:a(()=>[e(u(je))]),_:1}),i(" "+h(V.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),_:1})])])])}}},pl=Q(Je,[["__scopeId","data-v-e3e61f2a"]]);export{pl as default};
