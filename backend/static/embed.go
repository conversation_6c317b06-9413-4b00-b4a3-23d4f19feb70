package static

import (
	"embed"
	"fmt"
	"io/fs"
	"mime"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

// 嵌入前端静态文件
//go:embed dist/*
//go:embed dist/assets/_*
var StaticFiles embed.FS

// StaticFileHandler 处理静态文件请求
func StaticFileHandler() gin.HandlerFunc {
	// 获取嵌入的文件系统，去掉 "dist" 前缀
	staticFS, err := fs.Sub(StaticFiles, "dist")
	if err != nil {
		panic("Failed to create sub filesystem: " + err.Error())
	}

	// 调试：列出嵌入文件系统中的所有文件
	// fmt.Println("=== Listing embedded files ===")
	// fs.WalkDir(staticFS, ".", func(path string, d fs.DirEntry, err error) error {
	// 	if err != nil {
	// 		return err
	// 	}
	// 	fmt.Printf("Embedded file: %s\n", path)
	// 	return nil
	// })
	// fmt.Println("=== End of embedded files ===")

	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// 如果是API请求，跳过静态文件处理
		if strings.HasPrefix(path, "/api/") {
			c.Next()
			return
		}

		// 去掉路径前的斜杠
		filePath := strings.TrimPrefix(path, "/")
		if filePath == "" {
			filePath = "index.html"
		}

		// 调试日志
		fmt.Printf("Requested path: %s, filePath: %s\n", path, filePath)

		// 调试：打印请求的文件路径
		fmt.Printf("Requesting file: %s\n", filePath)

		// 检查文件是否存在
		if file, err := staticFS.Open(filePath); err == nil {
			defer file.Close()
			// fmt.Printf("File found: %s\n", filePath)

			// 读取文件内容
			if fileInfo, err := file.Stat(); err == nil && !fileInfo.IsDir() {
				// 设置正确的MIME类型
				ext := filepath.Ext(filePath)
				contentType := mime.TypeByExtension(ext)
				if contentType == "" {
					// 为一些特殊文件类型设置MIME类型
					switch ext {
					case ".js":
						contentType = "application/javascript; charset=utf-8"
					case ".css":
						contentType = "text/css; charset=utf-8"
					case ".html":
						contentType = "text/html; charset=utf-8"
					case ".svg":
						contentType = "image/svg+xml"
					case ".json":
						contentType = "application/json"
					default:
						contentType = "application/octet-stream"
					}
				}

				// 读取文件内容
				content, err := fs.ReadFile(staticFS, filePath)
				if err != nil {
					c.Status(http.StatusInternalServerError)
					c.Abort()
					return
				}

				// 设置响应头
				c.Header("Content-Type", contentType)
				c.Header("Cache-Control", "public, max-age=31536000") // 1年缓存

				// 返回文件内容
				c.Data(http.StatusOK, contentType, content)
				c.Abort()
				return
			}
		}

		// 文件不存在
		fmt.Printf("File not found: %s\n", filePath)

		// 对于SPA路由返回index.html
		if !strings.Contains(filePath, ".") {
			// 这是一个路由路径，返回index.html
			indexHTML, err := StaticFiles.ReadFile("dist/index.html")
			if err != nil {
				c.String(http.StatusInternalServerError, "Failed to load index.html")
				c.Abort()
				return
			}
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.Data(http.StatusOK, "text/html; charset=utf-8", indexHTML)
			c.Abort()
			return
		}

		// 其他情况返回404
		c.Status(http.StatusNotFound)
		c.Abort()
	}
}

// IndexHandler 处理根路径请求
func IndexHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 读取嵌入的index.html文件
		indexHTML, err := StaticFiles.ReadFile("dist/index.html")
		if err != nil {
			c.String(http.StatusInternalServerError, "Failed to load index.html")
			return
		}

		c.Data(http.StatusOK, "text/html; charset=utf-8", indexHTML)
	}
}
