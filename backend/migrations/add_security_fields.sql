-- 添加防爆破安全字段到用户表
ALTER TABLE users ADD COLUMN login_attempts INT DEFAULT 0 COMMENT '登录尝试次数';
ALTER TABLE users ADD COLUMN locked_until DATETIME NULL COMMENT '账户锁定到期时间';
ALTER TABLE users ADD COLUMN last_failed_at DATETIME NULL COMMENT '最后失败登录时间';

-- 删除Google OAuth相关字段（如果存在）
ALTER TABLE users DROP COLUMN IF EXISTS google_auth;
ALTER TABLE users DROP COLUMN IF EXISTS google_id;
ALTER TABLE users DROP COLUMN IF EXISTS google_email;
ALTER TABLE users DROP COLUMN IF EXISTS auth_provider;

-- 添加索引以提高查询性能
CREATE INDEX idx_users_login_attempts ON users(login_attempts);
CREATE INDEX idx_users_locked_until ON users(locked_until);
CREATE INDEX idx_users_last_failed_at ON users(last_failed_at);

-- 确保密码字段不为空（恢复原始设计）
ALTER TABLE users MODIFY COLUMN password VARCHAR(255) NOT NULL;