-- SEO Platform 数据库初始化脚本
-- 基于原有 seo_platform 项目的数据库结构重新设计

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint DEFAULT '1' COMMENT '状态 1正常 0禁用',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `google_auth` varchar(32) DEFAULT NULL COMMENT 'Google验证器密钥',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户组表
CREATE TABLE IF NOT EXISTS `auth_groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '组名',
  `status` tinyint DEFAULT '1' COMMENT '状态',
  `rules` text COMMENT '规则ID列表',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- 权限规则表
CREATE TABLE IF NOT EXISTS `auth_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `pid` bigint unsigned DEFAULT '0' COMMENT '父级ID',
  `name` varchar(80) NOT NULL COMMENT '规则唯一标识',
  `title` varchar(20) NOT NULL COMMENT '规则中文名称',
  `status` tinyint DEFAULT '1' COMMENT '状态 1正常 0禁用',
  `type` tinyint DEFAULT '1' COMMENT '类型',
  `condition` varchar(100) DEFAULT '' COMMENT '规则表达式',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限规则表';

-- 用户组关联表
CREATE TABLE IF NOT EXISTS `auth_group_access` (
  `uid` bigint unsigned NOT NULL COMMENT '用户ID',
  `group_id` bigint unsigned NOT NULL COMMENT '用户组ID',
  UNIQUE KEY `idx_uid_group_id` (`uid`,`group_id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组关联表';

-- 项目表
CREATE TABLE IF NOT EXISTS `projects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `site_id` varchar(32) NOT NULL COMMENT '站点ID',
  `project_name` varchar(255) NOT NULL COMMENT '项目名称',
  `project_url` varchar(255) NOT NULL COMMENT '项目地址',
  `project_type` int NOT NULL COMMENT '项目类型',
  `project_status` int NOT NULL COMMENT '项目状态',
  `monitor_status` int NOT NULL COMMENT '监控状态',
  `keyword` varchar(255) DEFAULT '' COMMENT '词库类型',
  `seo_option` text COMMENT 'SEO优化相关配置',
  `cache_option` text COMMENT '缓存相关设置',
  `elink_option` text COMMENT '外链设置',
  `push_option` text COMMENT '推送设置',
  `other_option` text COMMENT '其他设置',
  `project_charset` int DEFAULT NULL COMMENT '项目编码',
  `add_time` bigint DEFAULT NULL COMMENT '添加时间',
  `update_time` bigint DEFAULT NULL COMMENT '修改时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_site_id` (`site_id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';

-- 关键词表
CREATE TABLE IF NOT EXISTS `keywords` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) NOT NULL COMMENT '词库表名',
  `display_name` varchar(255) NOT NULL COMMENT '显示名称',
  `description` text COMMENT '描述',
  `keyword_type` varchar(50) DEFAULT NULL COMMENT '关键词类型',
  `status` int DEFAULT '1' COMMENT '状态 1启用 0禁用',
  `count` int DEFAULT '0' COMMENT '关键词数量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键词表';

-- 模板表
CREATE TABLE IF NOT EXISTS `templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '模板名称',
  `type` varchar(50) NOT NULL COMMENT '模板类型',
  `source` longtext NOT NULL COMMENT '模板源码',
  `description` text COMMENT '模板描述',
  `status` int DEFAULT '1' COMMENT '状态 1启用 0禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板表';

-- 蜘蛛日志表
CREATE TABLE IF NOT EXISTS `spider_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `spider_type` int NOT NULL COMMENT '蜘蛛类型',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `url` varchar(1000) DEFAULT NULL COMMENT '访问URL',
  `referer` varchar(1000) DEFAULT NULL COMMENT '来源页面',
  `domain` varchar(255) DEFAULT NULL COMMENT '域名',
  `method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `status_code` int DEFAULT NULL COMMENT '响应状态码',
  `response_time` int DEFAULT NULL COMMENT '响应时间',
  `visit_time` bigint DEFAULT NULL COMMENT '访问时间戳',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_spider_type` (`spider_type`),
  KEY `idx_domain` (`domain`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='蜘蛛日志表';

-- 缓存表
CREATE TABLE IF NOT EXISTS `caches` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) NOT NULL COMMENT '缓存键',
  `value` longtext COMMENT '缓存值',
  `expire_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_key` (`key`),
  KEY `idx_expire_at` (`expire_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存表';

-- 内容表
CREATE TABLE IF NOT EXISTS `contents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '内容类型',
  `title` varchar(500) NOT NULL COMMENT '标题',
  `content` longtext NOT NULL COMMENT '内容',
  `keywords` text COMMENT '关键词JSON',
  `domain` varchar(255) COMMENT '域名',
  `url` varchar(1000) COMMENT 'URL',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_domain` (`domain`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容表';

-- 注意：生产环境中请手动创建管理员用户
-- 不要在生产代码中包含硬编码的用户数据

SET FOREIGN_KEY_CHECKS = 1;
