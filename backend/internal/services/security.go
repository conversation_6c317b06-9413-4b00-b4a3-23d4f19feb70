package services

import (
	"fmt"
	"seo-platform/internal/config"
	"seo-platform/internal/models"
	"time"

	"gorm.io/gorm"
)

// SecurityService 安全服务
type SecurityService struct {
	db  *gorm.DB
	cfg *config.Config
}

// NewSecurityService 创建安全服务实例
func NewSecurityService(db *gorm.DB, cfg *config.Config) *SecurityService {
	return &SecurityService{
		db:  db,
		cfg: cfg,
	}
}

// CheckAccountLocked 检查账户是否被锁定
func (s *SecurityService) CheckAccountLocked(userID uint) (bool, time.Duration, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return false, 0, err
	}

	// 如果没有锁定时间，账户未被锁定
	if user.LockedUntil == nil {
		return false, 0, nil
	}

	// 检查锁定是否已过期
	if time.Now().After(*user.LockedUntil) {
		// 锁定已过期，清除锁定状态
		s.db.Model(&user).Updates(models.User{
			LockedUntil:   nil,
			LoginAttempts: 0,
		})
		return false, 0, nil
	}

	// 账户仍被锁定
	remainingTime := time.Until(*user.LockedUntil)
	return true, remainingTime, nil
}

// RecordFailedLogin 记录失败的登录尝试
func (s *SecurityService) RecordFailedLogin(userID uint) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	now := time.Now()

	// 检查是否需要重置尝试次数
	if user.LastFailedAt != nil {
		timeSinceLastFailed := now.Sub(*user.LastFailedAt)
		if timeSinceLastFailed.Seconds() > float64(s.cfg.Security.ResetAttemptsAfter) {
			// 重置尝试次数
			user.LoginAttempts = 0
		}
	}

	// 增加失败尝试次数
	user.LoginAttempts++
	user.LastFailedAt = &now

	// 检查是否需要锁定账户
	if user.LoginAttempts >= s.cfg.Security.MaxLoginAttempts {
		lockUntil := now.Add(time.Duration(s.cfg.Security.LockoutDuration) * time.Second)
		user.LockedUntil = &lockUntil
	}

	return s.db.Save(&user).Error
}

// RecordSuccessfulLogin 记录成功的登录
func (s *SecurityService) RecordSuccessfulLogin(userID uint, clientIP string) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	now := time.Now()

	// 清除失败登录记录
	user.LoginAttempts = 0
	user.LockedUntil = nil
	user.LastFailedAt = nil
	user.LastLoginAt = &now
	user.LastLoginIP = clientIP

	return s.db.Save(&user).Error
}

// GetLoginAttempts 获取登录尝试次数
func (s *SecurityService) GetLoginAttempts(userID uint) (int, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return 0, err
	}
	return user.LoginAttempts, nil
}

// UnlockAccount 手动解锁账户（管理员功能）
func (s *SecurityService) UnlockAccount(userID uint) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	user.LoginAttempts = 0
	user.LockedUntil = nil
	user.LastFailedAt = nil

	return s.db.Save(&user).Error
}

// GetSecurityStatus 获取用户安全状态
func (s *SecurityService) GetSecurityStatus(userID uint) (map[string]interface{}, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}

	status := map[string]interface{}{
		"login_attempts": user.LoginAttempts,
		"is_locked":      user.LockedUntil != nil && time.Now().Before(*user.LockedUntil),
		"locked_until":   user.LockedUntil,
		"last_failed_at": user.LastFailedAt,
		"last_login_at":  user.LastLoginAt,
		"last_login_ip":  user.LastLoginIP,
	}

	if user.LockedUntil != nil && time.Now().Before(*user.LockedUntil) {
		status["remaining_lockout"] = time.Until(*user.LockedUntil).Seconds()
	}

	return status, nil
}

// ValidateLoginAttempt 验证登录尝试是否被允许
func (s *SecurityService) ValidateLoginAttempt(username string) error {
	var user models.User
	if err := s.db.Where("username = ?", username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("用户不存在")
		}
		return err
	}

	// 检查账户是否被锁定
	isLocked, remainingTime, err := s.CheckAccountLocked(user.ID)
	if err != nil {
		return err
	}

	if isLocked {
		minutes := int(remainingTime.Minutes())
		seconds := int(remainingTime.Seconds()) % 60
		return fmt.Errorf("账户已被锁定，请在 %d 分 %d 秒后重试", minutes, seconds)
	}

	return nil
}