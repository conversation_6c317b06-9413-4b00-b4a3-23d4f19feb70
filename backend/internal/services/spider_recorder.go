package services

import (
	"encoding/json"
	"fmt"
	"seo-platform/internal/models"
	"seo-platform/internal/utils"
	"strings"
	"time"

	"gorm.io/gorm"
)

// SpiderRecorder 蜘蛛数据记录器
type SpiderRecorder struct {
	db     *gorm.DB
	crypto *utils.CryptoManager
}

// NewSpiderRecorder 创建蜘蛛数据记录器
func NewSpiderRecorder(db *gorm.DB) *SpiderRecorder {
	return &SpiderRecorder{
		db:     db,
		crypto: utils.GetDefaultCrypto(),
	}
}

// EncryptedSpiderLog 加密的蜘蛛日志
type EncryptedSpiderLog struct {
	ID           uint   `gorm:"primaryKey" json:"id"`
	SpiderType   int    `json:"spider_type"`
	EncryptedUA  string `json:"encrypted_ua"`   // 加密的User-Agent
	EncryptedIP  string `json:"encrypted_ip"`   // 加密的IP地址
	EncryptedURL string `json:"encrypted_url"`  // 加密的URL
	RefererHash  string `json:"referer_hash"`   // Referer哈希
	Domain       string `json:"domain"`
	Method       string `json:"method"`
	StatusCode   int    `json:"status_code"`
	ResponseTime int64  `json:"response_time"`
	VisitTime    int64  `json:"visit_time"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// SpiderStats 蜘蛛统计数据
type SpiderStats struct {
	ID               uint   `gorm:"primaryKey" json:"id"`
	Date             string `gorm:"size:10;index" json:"date"`             // 日期 YYYY-MM-DD
	SpiderType       int    `gorm:"index" json:"spider_type"`      // 蜘蛛类型
	TotalVisits      int64  `json:"total_visits"`     // 总访问数
	UniqueIPs        int64  `json:"unique_ips"`       // 唯一IP数
	AvgResponseTime  int64  `json:"avg_response_time"` // 平均响应时间
	CompressedData   string `gorm:"type:longtext" json:"compressed_data"`  // 压缩的详细数据
	EncryptionMethod string `gorm:"size:50" json:"encryption_method"` // 加密方法
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// RecordSpiderVisit 记录蜘蛛访问（加密存储）
func (sr *SpiderRecorder) RecordSpiderVisit(spiderType int, userAgent, ip, url, referer, domain, method string, statusCode int, responseTime int64) error {
	// 加密敏感数据
	encryptedUA := sr.crypto.Base64Encrypt([]byte(userAgent))
	encryptedIP := sr.crypto.Base64Encrypt([]byte(ip))
	encryptedURL := sr.crypto.Base64Encrypt([]byte(url))
	
	// 对Referer进行哈希处理（不完全加密，便于统计）
	refererHash := fmt.Sprintf("%x", sr.crypto.XOREncrypt([]byte(referer)))
	if len(refererHash) > 64 {
		refererHash = refererHash[:64]
	}

	// 创建加密日志记录
	encryptedLog := EncryptedSpiderLog{
		SpiderType:   spiderType,
		EncryptedUA:  encryptedUA,
		EncryptedIP:  encryptedIP,
		EncryptedURL: encryptedURL,
		RefererHash:  refererHash,
		Domain:       domain,
		Method:       method,
		StatusCode:   statusCode,
		ResponseTime: responseTime,
		VisitTime:    time.Now().Unix(),
	}

	// 保存到数据库
	if err := sr.db.Create(&encryptedLog).Error; err != nil {
		return fmt.Errorf("failed to save encrypted spider log: %v", err)
	}

	// 异步更新统计数据
	go sr.updateDailyStats(spiderType, responseTime)

	return nil
}

// updateDailyStats 更新每日统计数据
func (sr *SpiderRecorder) updateDailyStats(spiderType int, responseTime int64) {
	today := time.Now().Format("2006-01-02")
	
	// 查找或创建今日统计记录
	var stats SpiderStats
	result := sr.db.Where("date = ? AND spider_type = ?", today, spiderType).First(&stats)
	
	if result.Error == gorm.ErrRecordNotFound {
		// 创建新的统计记录
		stats = SpiderStats{
			Date:             today,
			SpiderType:       spiderType,
			TotalVisits:      1,
			UniqueIPs:        1, // 这里简化处理，实际应该统计唯一IP
			AvgResponseTime:  responseTime,
			EncryptionMethod: "base64",
		}
		
		// 压缩并加密详细数据
		detailData := map[string]interface{}{
			"hourly_visits": make(map[string]int),
			"status_codes": make(map[string]int),
			"domains":      make(map[string]int),
		}
		
		jsonData, _ := json.Marshal(detailData)
		compressedData := sr.crypto.CompressData(jsonData)
		stats.CompressedData = sr.crypto.Base64Encrypt(compressedData)
		
		sr.db.Create(&stats)
	} else {
		// 更新现有统计记录
		stats.TotalVisits++
		stats.AvgResponseTime = (stats.AvgResponseTime + responseTime) / 2
		
		// 解密并更新详细数据
		if compressedData, err := sr.crypto.Base64Decrypt(stats.CompressedData); err == nil {
			if decompressedData := sr.crypto.DecompressData(compressedData); len(decompressedData) > 0 {
				var detailData map[string]interface{}
				if json.Unmarshal(decompressedData, &detailData) == nil {
					// 更新小时访问统计
					hour := time.Now().Format("15")
					if hourlyVisits, ok := detailData["hourly_visits"].(map[string]interface{}); ok {
						if count, exists := hourlyVisits[hour]; exists {
							if countFloat, ok := count.(float64); ok {
								hourlyVisits[hour] = countFloat + 1
							}
						} else {
							hourlyVisits[hour] = 1
						}
					}
					
					// 重新压缩和加密
					jsonData, _ := json.Marshal(detailData)
					compressedData := sr.crypto.CompressData(jsonData)
					stats.CompressedData = sr.crypto.Base64Encrypt(compressedData)
				}
			}
		}
		
		sr.db.Save(&stats)
	}
}

// GetDecryptedSpiderLogs 获取解密的蜘蛛日志
func (sr *SpiderRecorder) GetDecryptedSpiderLogs(limit int, offset int) ([]models.SpiderLog, error) {
	var encryptedLogs []EncryptedSpiderLog
	if err := sr.db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&encryptedLogs).Error; err != nil {
		return nil, err
	}

	var decryptedLogs []models.SpiderLog
	for _, encLog := range encryptedLogs {
		// 解密数据
		uaBytes, _ := sr.crypto.Base64Decrypt(encLog.EncryptedUA)
		ipBytes, _ := sr.crypto.Base64Decrypt(encLog.EncryptedIP)
		urlBytes, _ := sr.crypto.Base64Decrypt(encLog.EncryptedURL)

		decryptedLog := models.SpiderLog{
			ID:           encLog.ID,
			SpiderType:   encLog.SpiderType,
			UserAgent:    string(uaBytes),
			IP:           string(ipBytes),
			URL:          string(urlBytes),
			Referer:      encLog.RefererHash, // 显示哈希值
			Domain:       encLog.Domain,
			Method:       encLog.Method,
			StatusCode:   encLog.StatusCode,
			ResponseTime: int(encLog.ResponseTime),
			VisitTime:    encLog.VisitTime,
			CreatedAt:    encLog.CreatedAt,
			UpdatedAt:    encLog.UpdatedAt,
		}
		decryptedLogs = append(decryptedLogs, decryptedLog)
	}

	return decryptedLogs, nil
}

// GetSpiderStats 获取蜘蛛统计数据
func (sr *SpiderRecorder) GetSpiderStats(date string, spiderType int) (*SpiderStats, error) {
	var stats SpiderStats
	query := sr.db.Where("date = ?", date)
	if spiderType > 0 {
		query = query.Where("spider_type = ?", spiderType)
	}
	
	if err := query.First(&stats).Error; err != nil {
		return nil, err
	}

	return &stats, nil
}

// GetDecompressedStatsData 获取解压缩的统计详细数据
func (sr *SpiderRecorder) GetDecompressedStatsData(stats *SpiderStats) (map[string]interface{}, error) {
	if stats.CompressedData == "" {
		return nil, fmt.Errorf("no compressed data available")
	}

	// 解密数据
	compressedData, err := sr.crypto.Base64Decrypt(stats.CompressedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt data: %v", err)
	}

	// 解压缩数据
	decompressedData := sr.crypto.DecompressData(compressedData)
	if len(decompressedData) == 0 {
		return nil, fmt.Errorf("failed to decompress data")
	}

	// 解析JSON
	var detailData map[string]interface{}
	if err := json.Unmarshal(decompressedData, &detailData); err != nil {
		return nil, fmt.Errorf("failed to parse JSON data: %v", err)
	}

	return detailData, nil
}

// CleanOldLogs 清理旧日志（保留加密数据的完整性）
func (sr *SpiderRecorder) CleanOldLogs(days int) (int64, error) {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	
	// 删除旧的加密日志
	result := sr.db.Where("created_at < ?", cutoffTime).Delete(&EncryptedSpiderLog{})
	if result.Error != nil {
		return 0, result.Error
	}

	return result.RowsAffected, nil
}

// IdentifySpider 蜘蛛识别（加密存储识别数据）
func (sr *SpiderRecorder) IdentifySpider(userAgent string) (int, map[string]interface{}) {
	userAgent = strings.ToLower(userAgent)
	
	// 蜘蛛识别规则
	spiderRules := map[string]int{
		"baiduspider":  1, // 百度
		"sogou":        2, // 搜狗
		"360spider":    3, // 360
		"bingbot":      4, // 必应
		"googlebot":    5, // 谷歌
		"yisouspider":  6, // 神马
		"yandexbot":    7, // Yandex
		"coccocbot":    8, // Coccoc
		"naverbot":     9, // Naver
	}

	spiderType := 0
	identificationData := map[string]interface{}{
		"user_agent_length": len(userAgent),
		"contains_keywords": []string{},
		"confidence":        0.0,
		"timestamp":         time.Now().Unix(),
	}

	// 识别蜘蛛类型
	for keyword, typeID := range spiderRules {
		if strings.Contains(userAgent, keyword) {
			spiderType = typeID
			identificationData["contains_keywords"] = append(
				identificationData["contains_keywords"].([]string), 
				keyword,
			)
			identificationData["confidence"] = 0.9
			break
		}
	}

	// 如果没有明确识别，进行模糊匹配
	if spiderType == 0 {
		if strings.Contains(userAgent, "bot") || 
		   strings.Contains(userAgent, "spider") || 
		   strings.Contains(userAgent, "crawler") {
			spiderType = 10 // 其他蜘蛛
			identificationData["confidence"] = 0.5
		}
	}

	// 加密识别数据并存储（可选）
	if spiderType > 0 {
		go sr.storeIdentificationData(userAgent, spiderType, identificationData)
	}

	return spiderType, identificationData
}

// storeIdentificationData 存储加密的识别数据
func (sr *SpiderRecorder) storeIdentificationData(userAgent string, spiderType int, data map[string]interface{}) {
	// 这里可以将识别数据加密存储到专门的表中
	// 用于后续的机器学习和模式识别
	jsonData, _ := json.Marshal(data)
	encryptedData := sr.crypto.Base64Encrypt(jsonData)
	
	// 存储到识别数据表（这里简化处理）
	_ = encryptedData // 避免未使用变量警告
}

// 确保表结构存在
func (sr *SpiderRecorder) AutoMigrate() error {
	return sr.db.AutoMigrate(&EncryptedSpiderLog{}, &SpiderStats{})
}