package services

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"seo-platform/internal/utils"
	"sync"
	"time"

	"gorm.io/gorm"
)

// TemplateCacheService 模板缓存服务
type TemplateCacheService struct {
	db     *gorm.DB
	crypto *utils.CryptoManager
	cache  sync.Map // 内存缓存
	mu     sync.RWMutex
}

// NewTemplateCacheService 创建模板缓存服务
func NewTemplateCacheService(db *gorm.DB) *TemplateCacheService {
	return &TemplateCacheService{
		db:     db,
		crypto: utils.GetDefaultCrypto(),
	}
}

// EncryptedTemplateCache 加密的模板缓存
type EncryptedTemplateCache struct {
	ID               uint      `gorm:"primaryKey" json:"id"`
	CacheKey         string    `gorm:"uniqueIndex" json:"cache_key"`
	TemplateID       uint      `json:"template_id"`
	EncryptedContent string    `json:"encrypted_content"` // 加密的模板内容
	EncryptedData    string    `json:"encrypted_data"`    // 加密的动态数据
	EncryptionMethod string    `json:"encryption_method"`
	Compressed       bool      `json:"compressed"`
	ExpiresAt        time.Time `json:"expires_at"`
	HitCount         int64     `json:"hit_count"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// DynamicContent 动态内容结构
type DynamicContent struct {
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Keywords    string                 `json:"keywords"`
	Content     string                 `json:"content"`
	Metadata    map[string]interface{} `json:"metadata"`
	GeneratedAt time.Time              `json:"generated_at"`
}

// CacheTemplate 缓存加密模板
func (tcs *TemplateCacheService) CacheTemplate(templateID uint, templateContent string, dynamicData map[string]interface{}, ttl time.Duration) (string, error) {
	// 生成缓存键
	cacheKey := tcs.generateCacheKey(templateID, dynamicData)
	
	// 加密模板内容
	encryptedContent, err := tcs.encryptTemplateContent(templateContent)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt template content: %v", err)
	}
	
	// 加密动态数据
	encryptedData, err := tcs.encryptDynamicData(dynamicData)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt dynamic data: %v", err)
	}
	
	// 创建缓存记录
	cacheRecord := EncryptedTemplateCache{
		CacheKey:         cacheKey,
		TemplateID:       templateID,
		EncryptedContent: encryptedContent,
		EncryptedData:    encryptedData,
		EncryptionMethod: "xxtea",
		Compressed:       true,
		ExpiresAt:        time.Now().Add(ttl),
		HitCount:         0,
	}
	
	// 保存到数据库
	if err := tcs.db.Create(&cacheRecord).Error; err != nil {
		return "", fmt.Errorf("failed to save cache record: %v", err)
	}
	
	// 同时保存到内存缓存
	tcs.cache.Store(cacheKey, &cacheRecord)
	
	return cacheKey, nil
}

// GetCachedTemplate 获取缓存的模板
func (tcs *TemplateCacheService) GetCachedTemplate(cacheKey string) (*DynamicContent, error) {
	// 先从内存缓存查找
	if cached, ok := tcs.cache.Load(cacheKey); ok {
		if cacheRecord, ok := cached.(*EncryptedTemplateCache); ok {
			// 检查是否过期
			if time.Now().Before(cacheRecord.ExpiresAt) {
				return tcs.decryptCachedContent(cacheRecord)
			} else {
				// 过期则删除
				tcs.cache.Delete(cacheKey)
			}
		}
	}
	
	// 从数据库查找
	var cacheRecord EncryptedTemplateCache
	if err := tcs.db.Where("cache_key = ? AND expires_at > ?", cacheKey, time.Now()).First(&cacheRecord).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 缓存不存在或已过期
		}
		return nil, fmt.Errorf("failed to query cache: %v", err)
	}
	
	// 更新命中次数
	go tcs.updateHitCount(cacheRecord.ID)
	
	// 解密并返回内容
	return tcs.decryptCachedContent(&cacheRecord)
}

// GenerateEncryptedContent 生成加密的动态内容
func (tcs *TemplateCacheService) GenerateEncryptedContent(templateContent string, data map[string]interface{}) (*DynamicContent, error) {
	// 创建动态内容
	dynamicContent := &DynamicContent{
		Title:       tcs.extractValue(data, "title", "默认标题"),
		Description: tcs.extractValue(data, "description", "默认描述"),
		Keywords:    tcs.extractValue(data, "keywords", "默认关键词"),
		Content:     tcs.processTemplate(templateContent, data),
		Metadata:    data,
		GeneratedAt: time.Now(),
	}
	
	return dynamicContent, nil
}

// CompressAndEncryptOutput 压缩并加密页面输出
func (tcs *TemplateCacheService) CompressAndEncryptOutput(content string, method string) (string, error) {
	contentBytes := []byte(content)
	
	// 先压缩
	compressedData := tcs.crypto.CompressData(contentBytes)
	
	// 再加密
	var encryptedData []byte
	var err error
	
	switch method {
	case "xor":
		encryptedData = tcs.crypto.XOREncrypt(compressedData)
	case "des":
		encryptedData, err = tcs.crypto.DESEncrypt(compressedData)
		if err != nil {
			return "", err
		}
	case "xxtea":
		encryptedData = tcs.crypto.XXTEAEncrypt(compressedData)
	case "base64":
		return tcs.crypto.Base64Encrypt(compressedData), nil
	default:
		return content, nil // 不加密
	}
	
	// 转换为Base64字符串便于传输
	return tcs.crypto.Base64Encrypt(encryptedData), nil
}

// DecompressAndDecryptOutput 解压缩并解密页面输出
func (tcs *TemplateCacheService) DecompressAndDecryptOutput(encryptedContent string, method string) (string, error) {
	// 先Base64解码
	encryptedData, err := tcs.crypto.Base64Decrypt(encryptedContent)
	if err != nil {
		return "", err
	}
	
	// 解密
	var compressedData []byte
	
	switch method {
	case "xor":
		compressedData = tcs.crypto.XORDecrypt(encryptedData)
	case "des":
		compressedData, err = tcs.crypto.DESDecrypt(encryptedData)
		if err != nil {
			return "", err
		}
	case "xxtea":
		compressedData = tcs.crypto.XXTEADecrypt(encryptedData)
	case "base64":
		// 对于base64方法，encryptedData就是压缩数据
		compressedData = encryptedData
	default:
		return encryptedContent, nil // 不解密
	}
	
	// 解压缩
	decompressedData := tcs.crypto.DecompressData(compressedData)
	
	return string(decompressedData), nil
}

// CleanExpiredCache 清理过期缓存
func (tcs *TemplateCacheService) CleanExpiredCache() (int64, error) {
	// 清理数据库中的过期记录
	result := tcs.db.Where("expires_at < ?", time.Now()).Delete(&EncryptedTemplateCache{})
	if result.Error != nil {
		return 0, result.Error
	}
	
	// 清理内存缓存中的过期记录
	var expiredKeys []string
	tcs.cache.Range(func(key, value interface{}) bool {
		if cacheRecord, ok := value.(*EncryptedTemplateCache); ok {
			if time.Now().After(cacheRecord.ExpiresAt) {
				if keyStr, ok := key.(string); ok {
					expiredKeys = append(expiredKeys, keyStr)
				}
			}
		}
		return true
	})
	
	for _, key := range expiredKeys {
		tcs.cache.Delete(key)
	}
	
	return result.RowsAffected, nil
}

// GetCacheStats 获取缓存统计信息
func (tcs *TemplateCacheService) GetCacheStats() (map[string]interface{}, error) {
	var totalRecords int64
	var activeRecords int64
	var totalHits int64
	
	// 统计总记录数
	tcs.db.Model(&EncryptedTemplateCache{}).Count(&totalRecords)
	
	// 统计活跃记录数
	tcs.db.Model(&EncryptedTemplateCache{}).Where("expires_at > ?", time.Now()).Count(&activeRecords)
	
	// 统计总命中数
	tcs.db.Model(&EncryptedTemplateCache{}).Select("COALESCE(SUM(hit_count), 0)").Scan(&totalHits)
	
	// 统计内存缓存数量
	memoryCount := 0
	tcs.cache.Range(func(key, value interface{}) bool {
		memoryCount++
		return true
	})
	
	stats := map[string]interface{}{
		"total_records":  totalRecords,
		"active_records": activeRecords,
		"total_hits":     totalHits,
		"memory_count":   memoryCount,
		"hit_rate":       float64(totalHits) / float64(totalRecords+1), // 避免除零
		"updated_at":     time.Now(),
	}
	
	return stats, nil
}

// 私有方法

// generateCacheKey 生成缓存键
func (tcs *TemplateCacheService) generateCacheKey(templateID uint, data map[string]interface{}) string {
	// 将数据序列化并生成MD5哈希
	jsonData, _ := json.Marshal(data)
	hash := md5.Sum(jsonData)
	return fmt.Sprintf("tpl_%d_%x", templateID, hash)
}

// encryptTemplateContent 加密模板内容
func (tcs *TemplateCacheService) encryptTemplateContent(content string) (string, error) {
	// 使用XXTEA加密
	contentBytes := []byte(content)
	compressedData := tcs.crypto.CompressData(contentBytes)
	encryptedData := tcs.crypto.XXTEAEncrypt(compressedData)
	return tcs.crypto.Base64Encrypt(encryptedData), nil
}

// encryptDynamicData 加密动态数据
func (tcs *TemplateCacheService) encryptDynamicData(data map[string]interface{}) (string, error) {
	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	
	// 压缩并加密
	compressedData := tcs.crypto.CompressData(jsonData)
	encryptedData := tcs.crypto.XXTEAEncrypt(compressedData)
	return tcs.crypto.Base64Encrypt(encryptedData), nil
}

// decryptCachedContent 解密缓存内容
func (tcs *TemplateCacheService) decryptCachedContent(cacheRecord *EncryptedTemplateCache) (*DynamicContent, error) {
	// 解密模板内容
	encryptedContentBytes, err := tcs.crypto.Base64Decrypt(cacheRecord.EncryptedContent)
	if err != nil {
		return nil, err
	}
	
	decryptedContentBytes := tcs.crypto.XXTEADecrypt(encryptedContentBytes)
	decompressedContent := tcs.crypto.DecompressData(decryptedContentBytes)
	
	// 解密动态数据
	encryptedDataBytes, err := tcs.crypto.Base64Decrypt(cacheRecord.EncryptedData)
	if err != nil {
		return nil, err
	}
	
	decryptedDataBytes := tcs.crypto.XXTEADecrypt(encryptedDataBytes)
	decompressedData := tcs.crypto.DecompressData(decryptedDataBytes)
	
	// 解析动态数据
	var dynamicData map[string]interface{}
	if err := json.Unmarshal(decompressedData, &dynamicData); err != nil {
		return nil, err
	}
	
	// 生成动态内容
	return tcs.GenerateEncryptedContent(string(decompressedContent), dynamicData)
}

// updateHitCount 更新命中次数
func (tcs *TemplateCacheService) updateHitCount(cacheID uint) {
	tcs.db.Model(&EncryptedTemplateCache{}).Where("id = ?", cacheID).UpdateColumn("hit_count", gorm.Expr("hit_count + 1"))
}

// extractValue 提取值
func (tcs *TemplateCacheService) extractValue(data map[string]interface{}, key, defaultValue string) string {
	if value, exists := data[key]; exists {
		if strValue, ok := value.(string); ok {
			return strValue
		}
	}
	return defaultValue
}

// processTemplate 处理模板
func (tcs *TemplateCacheService) processTemplate(template string, data map[string]interface{}) string {
	// 这里可以实现更复杂的模板处理逻辑
	// 简单的字符串替换示例
	processed := template
	for key, value := range data {
		if strValue, ok := value.(string); ok {
			placeholder := fmt.Sprintf("{{%s}}", key)
			processed = fmt.Sprintf("%s", fmt.Sprintf(processed, placeholder, strValue))
		}
	}
	return processed
}

// AutoMigrate 自动迁移表结构
func (tcs *TemplateCacheService) AutoMigrate() error {
	return tcs.db.AutoMigrate(&EncryptedTemplateCache{})
}