package models

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Username     string         `json:"username" gorm:"unique;size:50;not null"`
	Password     string         `json:"-" gorm:"size:255"`
	Email        string         `json:"email" gorm:"size:100"`
	Phone        string         `json:"phone" gorm:"size:20"`
	Avatar       string         `json:"avatar" gorm:"size:255"`
	Status       int            `json:"status" gorm:"default:1;comment:状态 1正常 0禁用"`
	LastLoginIP  string         `json:"last_login_ip" gorm:"size:45"`
	LastLoginAt  *time.Time     `json:"last_login_at"`
	// 防爆破安全字段
	LoginAttempts int           `json:"login_attempts" gorm:"default:0;comment:登录尝试次数"`
	LockedUntil   *time.Time    `json:"locked_until" gorm:"comment:账户锁定到期时间"`
	LastFailedAt  *time.Time    `json:"last_failed_at" gorm:"comment:最后失败登录时间"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// AuthGroup 用户组模型
type AuthGroup struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Title     string         `json:"title" gorm:"size:100;not null"`
	Status    int            `json:"status" gorm:"default:1"`
	Rules     string         `json:"rules" gorm:"type:text;comment:规则ID列表"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// AuthRule 权限规则模型
type AuthRule struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	PID       uint           `json:"pid" gorm:"default:0;comment:父级ID"`
	Name      string         `json:"name" gorm:"size:80;unique;not null;comment:规则唯一标识"`
	Title     string         `json:"title" gorm:"size:20;not null;comment:规则中文名称"`
	Status    int            `json:"status" gorm:"default:1;comment:状态 1正常 0禁用"`
	Type      int            `json:"type" gorm:"default:1"`
	Condition string         `json:"condition" gorm:"size:100;comment:规则表达式"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// AuthGroupAccess 用户组关联模型
type AuthGroupAccess struct {
	UID     uint `json:"uid" gorm:"comment:用户ID"`
	GroupID uint `json:"group_id" gorm:"comment:用户组ID"`
}

func (AuthGroupAccess) TableName() string {
	return "auth_group_access"
}
