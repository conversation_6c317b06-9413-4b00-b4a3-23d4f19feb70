package models

import (
	"time"

	"gorm.io/gorm"
)

// Keyword 关键词模型
type Keyword struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	TableName   string         `json:"table_name" gorm:"size:100;not null;comment:词库表名"`
	DisplayName string         `json:"display_name" gorm:"size:255;not null;comment:显示名称"`
	Description string         `json:"description" gorm:"type:text;comment:描述"`
	KeywordType string         `json:"keyword_type" gorm:"size:50;comment:关键词类型"`
	Status      int            `json:"status" gorm:"default:1;comment:状态 1启用 0禁用"`
	Count       int            `json:"count" gorm:"default:0;comment:关键词数量"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// KeywordData 关键词数据模型（动态表）
type KeywordData struct {
	ID      uint   `json:"id" gorm:"primaryKey"`
	Keyword string `json:"keyword" gorm:"size:255;not null;comment:关键词"`
	Weight  int    `json:"weight" gorm:"default:1;comment:权重"`
}

// Template 模板模型
type Template struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:255;not null;comment:模板名称"`
	Type        string         `json:"type" gorm:"size:50;not null;comment:模板类型"`
	Source      string         `json:"source" gorm:"type:longtext;not null;comment:模板源码"`
	Description string         `json:"description" gorm:"type:text;comment:模板描述"`
	Status      int            `json:"status" gorm:"default:1;comment:状态 1启用 0禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// Article 文章模型
type Article struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Title       string         `json:"title" gorm:"size:255;not null;comment:文章标题"`
	Content     string         `json:"content" gorm:"type:longtext;not null;comment:文章内容"`
	Category    string         `json:"category" gorm:"size:100;comment:文章分类"`
	Tags        string         `json:"tags" gorm:"size:255;comment:文章标签"`
	Status      int            `json:"status" gorm:"default:1;comment:状态 1发布 0草稿"`
	ViewCount   int            `json:"view_count" gorm:"default:0;comment:浏览次数"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}
