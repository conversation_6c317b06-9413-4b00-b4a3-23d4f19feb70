package models

import (
	"time"

	"gorm.io/gorm"
)

// SpiderLog 蜘蛛日志模型
type SpiderLog struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	SpiderType int            `json:"spider_type" gorm:"not null;comment:蜘蛛类型 1百度 2搜狗 3360 4必应 5谷歌 6神马 7Yandex 8Coccoc 9Naver"`
	UserAgent  string         `json:"user_agent" gorm:"size:500;comment:用户代理"`
	IP         string         `json:"ip" gorm:"size:45;comment:IP地址"`
	URL        string         `json:"url" gorm:"size:1000;comment:访问URL"`
	Referer    string         `json:"referer" gorm:"size:1000;comment:来源页面"`
	Domain     string         `json:"domain" gorm:"size:255;comment:域名"`
	Method     string         `json:"method" gorm:"size:10;comment:请求方法"`
	StatusCode int            `json:"status_code" gorm:"comment:响应状态码"`
	ResponseTime int          `json:"response_time" gorm:"comment:响应时间(ms)"`
	VisitTime  int64          `json:"visit_time" gorm:"comment:访问时间戳"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

// SpiderStats 蜘蛛统计模型
type SpiderStats struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	Date       string    `json:"date" gorm:"size:10;unique;comment:统计日期 YYYY-MM-DD"`
	Hour       int       `json:"hour" gorm:"comment:小时 0-23"`
	SpiderType int       `json:"spider_type" gorm:"comment:蜘蛛类型"`
	Count      int       `json:"count" gorm:"default:0;comment:访问次数"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// Cache 缓存模型（用于MySQL存储）
type Cache struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Key       string         `json:"key" gorm:"size:255;unique;not null;comment:缓存键"`
	Value     string         `json:"value" gorm:"type:longtext;comment:缓存值"`
	ExpireAt  *time.Time     `json:"expire_at" gorm:"comment:过期时间"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Content 内容模型（用于MySQL存储）
type Content struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Type      string         `json:"type" gorm:"size:50;not null;comment:内容类型"`
	Title     string         `json:"title" gorm:"size:500;not null;comment:标题"`
	Content   string         `json:"content" gorm:"type:longtext;not null;comment:内容"`
	Keywords  string         `json:"keywords" gorm:"type:text;comment:关键词JSON"`
	Domain    string         `json:"domain" gorm:"size:255;comment:域名"`
	URL       string         `json:"url" gorm:"size:1000;comment:URL"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}
