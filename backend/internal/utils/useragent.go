package utils

import (
	"bufio"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"sync"
	"time"
)

// UserAgentManager User-Agent管理器
type UserAgentManager struct {
	userAgents []string
	mutex      sync.RWMutex
	loaded     bool
}

var (
	// 全局User-Agent管理器实例
	defaultManager *UserAgentManager
	once           sync.Once
)

// GetUserAgentManager 获取默认的User-Agent管理器实例
func GetUserAgentManager() *UserAgentManager {
	once.Do(func() {
		defaultManager = &UserAgentManager{}
		// 自动加载User-Agent列表
		if err := defaultManager.LoadFromFile("static/data/user-agents.txt"); err != nil {
			// 如果加载失败，使用默认的User-Agent
			defaultManager.userAgents = []string{
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
				"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
			}
			defaultManager.loaded = true
		}
	})
	return defaultManager
}

// LoadFromFile 从文件加载User-Agent列表
func (m *UserAgentManager) LoadFromFile(filePath string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open user-agent file: %w", err)
	}
	defer file.Close()

	var userAgents []string
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") {
			userAgents = append(userAgents, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("failed to read user-agent file: %w", err)
	}

	if len(userAgents) == 0 {
		return fmt.Errorf("no valid user-agents found in file")
	}

	m.userAgents = userAgents
	m.loaded = true
	
	return nil
}

// GetRandomUserAgent 获取随机User-Agent
func (m *UserAgentManager) GetRandomUserAgent() string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.loaded || len(m.userAgents) == 0 {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
	}

	rand.Seed(time.Now().UnixNano())
	index := rand.Intn(len(m.userAgents))
	return m.userAgents[index]
}

// GetUserAgentByIndex 根据索引获取User-Agent
func (m *UserAgentManager) GetUserAgentByIndex(index int) string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.loaded || len(m.userAgents) == 0 || index < 0 || index >= len(m.userAgents) {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
	}

	return m.userAgents[index]
}

// GetAllUserAgents 获取所有User-Agent
func (m *UserAgentManager) GetAllUserAgents() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.loaded {
		return []string{}
	}

	// 返回副本，避免外部修改
	result := make([]string, len(m.userAgents))
	copy(result, m.userAgents)
	return result
}

// GetCount 获取User-Agent数量
func (m *UserAgentManager) GetCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.userAgents)
}

// IsLoaded 检查是否已加载
func (m *UserAgentManager) IsLoaded() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.loaded
}

// FilterByKeyword 根据关键词过滤User-Agent
func (m *UserAgentManager) FilterByKeyword(keyword string) []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.loaded {
		return []string{}
	}

	var filtered []string
	keyword = strings.ToLower(keyword)
	
	for _, ua := range m.userAgents {
		if strings.Contains(strings.ToLower(ua), keyword) {
			filtered = append(filtered, ua)
		}
	}

	return filtered
}

// GetRandomUserAgentByKeyword 根据关键词获取随机User-Agent
func (m *UserAgentManager) GetRandomUserAgentByKeyword(keyword string) string {
	filtered := m.FilterByKeyword(keyword)
	if len(filtered) == 0 {
		return m.GetRandomUserAgent()
	}

	rand.Seed(time.Now().UnixNano())
	index := rand.Intn(len(filtered))
	return filtered[index]
}

// 便捷函数

// GetRandomUserAgent 获取随机User-Agent（全局函数）
func GetRandomUserAgent() string {
	return GetUserAgentManager().GetRandomUserAgent()
}

// GetRandomUserAgentByKeyword 根据关键词获取随机User-Agent（全局函数）
func GetRandomUserAgentByKeyword(keyword string) string {
	return GetUserAgentManager().GetRandomUserAgentByKeyword(keyword)
}

// GetUserAgentCount 获取User-Agent总数（全局函数）
func GetUserAgentCount() int {
	return GetUserAgentManager().GetCount()
}

// IsUserAgentLoaded 检查User-Agent是否已加载（全局函数）
func IsUserAgentLoaded() bool {
	return GetUserAgentManager().IsLoaded()
}
