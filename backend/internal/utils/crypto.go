package utils

import (
	"crypto/des"
	"encoding/base64"
	"errors"
	"fmt"
	"math/rand"
	"time"
)

// CryptoManager 加密管理器
type CryptoManager struct {
	key []byte
}

// NewCryptoManager 创建加密管理器
func NewCryptoManager(key string) *CryptoManager {
	if len(key) < 8 {
		// 补齐密钥长度
		for len(key) < 8 {
			key += key
		}
	}
	return &CryptoManager{
		key: []byte(key[:8]), // DES需要8字节密钥
	}
}

// XOREncrypt XOR异或加密
func (c *CryptoManager) XOREncrypt(data []byte) []byte {
	result := make([]byte, len(data))
	keyLen := len(c.key)
	
	for i, b := range data {
		result[i] = b ^ c.key[i%keyLen]
	}
	return result
}

// XORDecrypt XOR异或解密
func (c *CryptoManager) XORDecrypt(data []byte) []byte {
	// XOR加密和解密是相同的操作
	return c.XOREncrypt(data)
}

// DESEncrypt DES加密
func (c *CryptoManager) DESEncrypt(data []byte) ([]byte, error) {
	block, err := des.NewCipher(c.key)
	if err != nil {
		return nil, err
	}

	// 填充数据到8字节的倍数
	padding := 8 - len(data)%8
	padText := make([]byte, padding)
	for i := range padText {
		padText[i] = byte(padding)
	}
	data = append(data, padText...)

	// 使用ECB模式加密
	encrypted := make([]byte, len(data))
	for i := 0; i < len(data); i += 8 {
		block.Encrypt(encrypted[i:i+8], data[i:i+8])
	}

	return encrypted, nil
}

// DESDecrypt DES解密
func (c *CryptoManager) DESDecrypt(data []byte) ([]byte, error) {
	block, err := des.NewCipher(c.key)
	if err != nil {
		return nil, err
	}

	if len(data)%8 != 0 {
		return nil, errors.New("encrypted data length must be multiple of 8")
	}

	// 解密
	decrypted := make([]byte, len(data))
	for i := 0; i < len(data); i += 8 {
		block.Decrypt(decrypted[i:i+8], data[i:i+8])
	}

	// 去除填充
	padding := int(decrypted[len(decrypted)-1])
	if padding > 8 || padding <= 0 {
		return nil, errors.New("invalid padding")
	}

	return decrypted[:len(decrypted)-padding], nil
}

// XXTEAEncrypt XXTEA加密
func (c *CryptoManager) XXTEAEncrypt(data []byte) []byte {
	// 将字节转换为32位整数数组
	v := bytesToUint32Array(data)
	k := bytesToUint32Array(c.key)
	
	n := len(v)
	if n <= 1 {
		return data
	}

	// XXTEA算法
	delta := uint32(0x9E3779B9)
	sum := uint32(0)
	rounds := 6 + 52/n

	for rounds > 0 {
		sum += delta
		e := (sum >> 2) & 3
		for p := 0; p < n-1; p++ {
			y := v[p+1]
			z := v[n-1]
			if p == 0 {
				z = v[n-1]
			} else {
				z = v[p-1]
			}
			v[p] += ((z>>5 ^ y<<2) + (y>>3 ^ z<<4)) ^ ((sum ^ y) + (k[(uint32(p)&3)^e] ^ z))
		}
		y := v[0]
		z := v[n-2]
		v[n-1] += ((z>>5 ^ y<<2) + (y>>3 ^ z<<4)) ^ ((sum ^ y) + (k[(uint32(n-1)&3)^e] ^ z))
		rounds--
	}

	return uint32ArrayToBytes(v)
}

// XXTEADecrypt XXTEA解密
func (c *CryptoManager) XXTEADecrypt(data []byte) []byte {
	v := bytesToUint32Array(data)
	k := bytesToUint32Array(c.key)
	
	n := len(v)
	if n <= 1 {
		return data
	}

	delta := uint32(0x9E3779B9)
	rounds := 6 + 52/n
	sum := uint32(rounds) * delta

	for sum != 0 {
		e := (sum >> 2) & 3
		for p := n - 1; p > 0; p-- {
			y := v[p-1]
			z := v[p+1]
			if p == n-1 {
				z = v[0]
			} else {
				z = v[p+1]
			}
			v[p] -= ((z>>5 ^ y<<2) + (y>>3 ^ z<<4)) ^ ((sum ^ y) + (k[(uint32(p)&3)^e] ^ z))
		}
		y := v[n-1]
		z := v[1]
		v[0] -= ((z>>5 ^ y<<2) + (y>>3 ^ z<<4)) ^ ((sum ^ y) + (k[(0&3)^e] ^ z))
		sum -= delta
	}

	return uint32ArrayToBytes(v)
}

// Base64Encrypt 改进的Base64编码
func (c *CryptoManager) Base64Encrypt(data []byte) string {
	// 先进行XOR加密，再Base64编码
	xorData := c.XOREncrypt(data)
	return base64.StdEncoding.EncodeToString(xorData)
}

// Base64Decrypt 改进的Base64解码
func (c *CryptoManager) Base64Decrypt(data string) ([]byte, error) {
	// 先Base64解码，再XOR解密
	decodedData, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return nil, err
	}
	return c.XORDecrypt(decodedData), nil
}

// CompressData 数据压缩（简单的RLE压缩）
func (c *CryptoManager) CompressData(data []byte) []byte {
	if len(data) == 0 {
		return data
	}

	var compressed []byte
	count := 1
	current := data[0]

	for i := 1; i < len(data); i++ {
		if data[i] == current && count < 255 {
			count++
		} else {
			compressed = append(compressed, byte(count), current)
			current = data[i]
			count = 1
		}
	}
	compressed = append(compressed, byte(count), current)

	return compressed
}

// DecompressData 数据解压缩
func (c *CryptoManager) DecompressData(data []byte) []byte {
	if len(data)%2 != 0 {
		return data // 无效的压缩数据
	}

	var decompressed []byte
	for i := 0; i < len(data); i += 2 {
		count := int(data[i])
		value := data[i+1]
		for j := 0; j < count; j++ {
			decompressed = append(decompressed, value)
		}
	}

	return decompressed
}

// 辅助函数
func bytesToUint32Array(data []byte) []uint32 {
	// 确保数据长度是4的倍数
	for len(data)%4 != 0 {
		data = append(data, 0)
	}

	result := make([]uint32, len(data)/4)
	for i := 0; i < len(result); i++ {
		result[i] = uint32(data[i*4]) |
			(uint32(data[i*4+1]) << 8) |
			(uint32(data[i*4+2]) << 16) |
			(uint32(data[i*4+3]) << 24)
	}
	return result
}

func uint32ArrayToBytes(data []uint32) []byte {
	result := make([]byte, len(data)*4)
	for i, v := range data {
		result[i*4] = byte(v)
		result[i*4+1] = byte(v >> 8)
		result[i*4+2] = byte(v >> 16)
		result[i*4+3] = byte(v >> 24)
	}
	return result
}

// 全局加密管理器实例
var defaultCrypto *CryptoManager

func init() {
	// 使用随机种子初始化默认加密器
	rand.Seed(time.Now().UnixNano())
	defaultKey := fmt.Sprintf("seo%d", time.Now().Unix()%10000)
	defaultCrypto = NewCryptoManager(defaultKey)
}

// GetDefaultCrypto 获取默认加密管理器
func GetDefaultCrypto() *CryptoManager {
	return defaultCrypto
}

// EncryptSpiderData 加密蜘蛛数据
func EncryptSpiderData(data []byte, method string) ([]byte, error) {
	switch method {
	case "xor":
		return defaultCrypto.XOREncrypt(data), nil
	case "des":
		return defaultCrypto.DESEncrypt(data)
	case "xxtea":
		return defaultCrypto.XXTEAEncrypt(data), nil
	default:
		return data, nil
	}
}

// DecryptSpiderData 解密蜘蛛数据
func DecryptSpiderData(data []byte, method string) ([]byte, error) {
	switch method {
	case "xor":
		return defaultCrypto.XORDecrypt(data), nil
	case "des":
		return defaultCrypto.DESDecrypt(data)
	case "xxtea":
		return defaultCrypto.XXTEADecrypt(data), nil
	default:
		return data, nil
	}
}