package database

import (
	"fmt"
	"seo-platform/internal/config"
	"seo-platform/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func InitMySQL(cfg config.MySQLConfig) (*gorm.DB, error) {
	// Step 1: Connect to MySQL server without specifying database to check/create database
	if err := ensureDatabaseExists(cfg); err != nil {
		return nil, fmt.Errorf("failed to ensure database exists: %w", err)
	}

	// Step 2: Connect to the specific database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MySQL database '%s': %w", cfg.Database, err)
	}

	// Step 3: Auto-migrate database tables
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Step 4: Insert initial data (admin user, permissions, etc.)
	if err := insertInitialData(db); err != nil {
		return nil, fmt.Errorf("failed to insert initial data: %w", err)
	}

	return db, nil
}

// ensureDatabaseExists connects to MySQL server and creates the database if it doesn't exist
func ensureDatabaseExists(cfg config.MySQLConfig) error {
	// Connect to MySQL server without specifying a database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/?charset=%s&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Reduce log noise for this step
	})
	if err != nil {
		return fmt.Errorf("failed to connect to MySQL server at %s:%s: %w", cfg.Host, cfg.Port, err)
	}

	// Get the underlying SQL database connection
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}
	defer sqlDB.Close()

	// Check if database exists
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?", cfg.Database).Scan(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check if database exists: %w", err)
	}

	if count == 0 {
		// Database doesn't exist, create it
		createSQL := fmt.Sprintf("CREATE DATABASE `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", cfg.Database)
		err = db.Exec(createSQL).Error
		if err != nil {
			return fmt.Errorf("failed to create database '%s': %w", cfg.Database, err)
		}
		fmt.Printf("✅ Database '%s' created successfully\n", cfg.Database)
	} else {
		fmt.Printf("✅ Database '%s' already exists\n", cfg.Database)
	}

	return nil
}

func autoMigrate(db *gorm.DB) error {
	fmt.Println("🔄 Running database migrations...")

	err := db.AutoMigrate(
		&models.User{},
		&models.AuthGroup{},
		&models.AuthRule{},
		&models.AuthGroupAccess{},
		&models.Project{},
		&models.Keyword{},
		&models.KeywordData{},
		&models.Template{},
		&models.Article{},
		&models.SpiderLog{},
		&models.SpiderStats{},
		&models.Monitor{},
		&models.WebsiteList{},
		&models.WebsiteProfile{},
		&models.Cache{},
		&models.Content{},

	)

	if err != nil {
		return err
	}

	fmt.Println("✅ Database tables created/updated successfully")
	return nil
}

func insertInitialData(db *gorm.DB) error {
	fmt.Println("🔄 Checking initial data setup...")

	// 注意：生产环境中不应该自动创建默认用户
	// 请手动创建管理员用户或通过安全的初始化脚本创建
	fmt.Println("⚠️  No default users will be created in production mode")
	fmt.Println("📋 Please create admin user manually or use secure initialization script")

	return nil
}
