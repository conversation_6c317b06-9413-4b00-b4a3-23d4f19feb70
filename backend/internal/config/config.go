package config

import (
	"github.com/spf13/viper"
)

type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Security SecurityConfig `mapstructure:"security"`
	SEO      SEOConfig      `mapstructure:"seo"`
}

type ServerConfig struct {
	Port string `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
}

type DatabaseConfig struct {
	MySQL MySQLConfig `mapstructure:"mysql"`
}

type MySQLConfig struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	Charset  string `mapstructure:"charset"`
}



type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpireTime int    `mapstructure:"expire_time"`
}

type SecurityConfig struct {
	MaxLoginAttempts   int `mapstructure:"max_login_attempts"`
	LockoutDuration    int `mapstructure:"lockout_duration"`
	ResetAttemptsAfter int `mapstructure:"reset_attempts_after"`
}

type SEOConfig struct {
	DefaultUserAgent string   `mapstructure:"default_user_agent"`
	AllowedDomains   []string `mapstructure:"allowed_domains"`
	CacheExpire      int      `mapstructure:"cache_expire"`
}

func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")

	// 支持通过环境变量指定配置文件路径
	if configPath := viper.GetString("CONFIG_PATH"); configPath != "" {
		viper.AddConfigPath(configPath)
	}

	// 默认搜索路径
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	viper.AddConfigPath("../config")
	viper.AddConfigPath("../../config")
	viper.AddConfigPath("./backend/config")
	viper.AddConfigPath("/etc/seo-platform")
	viper.AddConfigPath("$HOME/.seo-platform")

	// 设置默认值
	setDefaults()

	// 绑定环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("SEO_PLATFORM")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

func setDefaults() {
	viper.SetDefault("server.port", "8080")
	viper.SetDefault("server.mode", "debug")
	
	viper.SetDefault("database.mysql.host", "localhost")
	viper.SetDefault("database.mysql.port", "3306")
	viper.SetDefault("database.mysql.charset", "utf8mb4")
	
	viper.SetDefault("jwt.secret", "your-secret-key")
	viper.SetDefault("jwt.expire_time", 86400)
	
	viper.SetDefault("seo.default_user_agent", "Mozilla/5.0 (compatible; SEO-Platform/1.0)")
	viper.SetDefault("seo.cache_expire", 3600)
}
