package middleware

import (
	"seo-platform/internal/services"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SpiderTracker 蜘蛛访问跟踪中间件
type SpiderTracker struct {
	spiderRecorder *services.SpiderRecorder
}

// NewSpiderTracker 创建蜘蛛跟踪中间件
func NewSpiderTracker(db *gorm.DB) *SpiderTracker {
	return &SpiderTracker{
		spiderRecorder: services.NewSpiderRecorder(db),
	}
}

// TrackSpiderVisit 跟踪蜘蛛访问的中间件
func (st *SpiderTracker) TrackSpiderVisit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		startTime := time.Now()
		
		// 处理请求
		c.Next()
		
		// 计算响应时间
		responseTime := time.Since(startTime).Milliseconds()
		
		// 获取请求信息
		userAgent := c.GetHeader("User-Agent")
		clientIP := c.ClientIP()
		url := c.Request.URL.Path
		referer := c.GetHeader("Referer")
		domain := c.Request.Host
		method := c.Request.Method
		statusCode := c.Writer.Status()
		
		// 识别是否为蜘蛛访问
		spiderType, _ := st.spiderRecorder.IdentifySpider(userAgent)
		
		// 如果是蜘蛛访问，则记录并加密存储
		if spiderType > 0 {
			// 异步记录，不影响响应性能
			go func() {
				err := st.spiderRecorder.RecordSpiderVisit(
					spiderType,
					userAgent,
					clientIP,
					url,
					referer,
					domain,
					method,
					statusCode,
					responseTime,
				)
				if err != nil {
					// 记录错误但不影响主流程
					// 可以在这里添加日志记录
				}
			}()
		}
	}
}

// IsSpiderUserAgent 检查是否为蜘蛛User-Agent
func IsSpiderUserAgent(userAgent string) bool {
	userAgent = strings.ToLower(userAgent)
	
	// 常见蜘蛛标识
	spiderKeywords := []string{
		"baiduspider", "sogou", "360spider", "bingbot", "googlebot",
		"yisouspider", "yandexbot", "coccocbot", "naverbot",
		"bot", "spider", "crawler", "slurp", "crawl",
	}
	
	for _, keyword := range spiderKeywords {
		if strings.Contains(userAgent, keyword) {
			return true
		}
	}
	
	return false
}

// GetSpiderType 获取蜘蛛类型
func GetSpiderType(userAgent string) int {
	userAgent = strings.ToLower(userAgent)
	
	// 蜘蛛类型映射
	spiderTypes := map[string]int{
		"baiduspider":  1, // 百度
		"sogou":        2, // 搜狗
		"360spider":    3, // 360
		"bingbot":      4, // 必应
		"googlebot":    5, // 谷歌
		"yisouspider":  6, // 神马
		"yandexbot":    7, // Yandex
		"coccocbot":    8, // Coccoc
		"naverbot":     9, // Naver
	}
	
	for keyword, typeID := range spiderTypes {
		if strings.Contains(userAgent, keyword) {
			return typeID
		}
	}
	
	// 如果包含通用蜘蛛关键词但不是特定蜘蛛
	if IsSpiderUserAgent(userAgent) {
		return 10 // 其他蜘蛛
	}
	
	return 0 // 非蜘蛛
}