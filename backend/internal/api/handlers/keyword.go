package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type KeywordHandler struct {
	db *gorm.DB
}

func NewKeywordHandler(db *gorm.DB) *KeywordHandler {
	return &KeywordHandler{
		db: db,
	}
}

// List 获取关键词库列表
func (h *KeywordHandler) List(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page_size", "10"))
	keyword := c.Query("keyword")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.Keyword{})
	if keyword != "" {
		query = query.Where("display_name LIKE ? OR table_name LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	var total int64
	query.Count(&total)

	var keywords []models.Keyword
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&keywords).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      keywords,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// Create 创建关键词库
func (h *KeywordHandler) Create(c *gin.Context) {
	var req struct {
		DisplayName string `json:"display_name" binding:"required"`
		Description string `json:"description"`
		KeywordType string `json:"keyword_type"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 生成表名
	tableName := "keywords_" + strings.ToLower(strings.ReplaceAll(req.DisplayName, " ", "_"))

	keyword := models.Keyword{
		TableName:   tableName,
		DisplayName: req.DisplayName,
		Description: req.Description,
		KeywordType: req.KeywordType,
		Status:      1,
		Count:       0,
	}

	if err := h.db.Create(&keyword).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    keyword,
	})
}

// Get 获取关键词库详情
func (h *KeywordHandler) Get(c *gin.Context) {
	id := c.Param("id")

	var keyword models.Keyword
	if err := h.db.First(&keyword, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "关键词库不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    keyword,
	})
}

// Update 更新关键词库
func (h *KeywordHandler) Update(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		DisplayName string `json:"display_name"`
		Description string `json:"description"`
		KeywordType string `json:"keyword_type"`
		Status      int    `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	if err := h.db.Model(&models.Keyword{}).Where("id = ?", id).Updates(req).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// Delete 删除关键词库
func (h *KeywordHandler) Delete(c *gin.Context) {
	id := c.Param("id")

	if err := h.db.Delete(&models.Keyword{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// Import 导入关键词
func (h *KeywordHandler) Import(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		Keywords []string `json:"keywords" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 验证关键词库是否存在
	var keyword models.Keyword
	if err := h.db.First(&keyword, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "关键词库不存在",
		})
		return
	}

	// 这里实现关键词导入逻辑
	// 由于涉及动态表操作，暂时返回成功
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "导入成功",
		"data": gin.H{
			"imported_count": len(req.Keywords),
		},
	})
}

// Export 导出关键词
func (h *KeywordHandler) Export(c *gin.Context) {
	id := c.Param("id")

	var keyword models.Keyword
	if err := h.db.First(&keyword, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "关键词库不存在",
		})
		return
	}

	// 这里实现关键词导出逻辑
	// 获取所有关键词进行导出
	var keywords []models.Keyword
	if err := h.db.Find(&keywords).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	// 提取关键词名称
	var keywordNames []string
	for _, keyword := range keywords {
		keywordNames = append(keywordNames, keyword.DisplayName)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "导出成功",
		"data": gin.H{
			"keywords": keywordNames,
			"count":    len(keywordNames),
		},
	})
}
