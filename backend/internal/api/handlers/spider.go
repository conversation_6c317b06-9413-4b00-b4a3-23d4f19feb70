package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"seo-platform/internal/services"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type SpiderHandler struct {
	db             *gorm.DB
	spiderRecorder *services.SpiderRecorder
}

func NewSpiderHandler(db *gorm.DB) *SpiderHandler {
	spiderRecorder := services.NewSpiderRecorder(db)
	// 自动迁移加密表结构
	spiderRecorder.AutoMigrate()
	
	return &SpiderHandler{
		db:             db,
		spiderRecorder: spiderRecorder,
	}
}

// GetLogs 获取蜘蛛日志（从加密存储中解密获取）
func (h *SpiderHandler) GetLogs(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.Default<PERSON>uery("page_size", "20"))
	spiderType := c.Query("spider_type")
	domain := c.Query("domain")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	offset := (page - 1) * pageSize

	// 优先从加密存储中获取数据
	logs, err := h.spiderRecorder.GetDecryptedSpiderLogs(pageSize, offset)
	if err != nil {
		// 如果加密存储失败，回退到原始查询
		query := h.db.Model(&models.SpiderLog{})

		if spiderType != "" {
			query = query.Where("spider_type = ?", spiderType)
		}
		if domain != "" {
			query = query.Where("domain LIKE ?", "%"+domain+"%")
		}
		if startDate != "" && endDate != "" {
			query = query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
		}

		var total int64
		query.Count(&total)

		var originalLogs []models.SpiderLog
		if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&originalLogs).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "查询失败",
			})
			return
		}

		// 转换为统一格式
		logs = make([]models.SpiderLog, len(originalLogs))
		copy(logs, originalLogs)

		c.JSON(http.StatusOK, gin.H{
			"code":    200,
			"message": "获取成功",
			"data": gin.H{
				"logs":       logs,
				"total":      total,
				"page":       page,
				"page_size":  pageSize,
				"encrypted":  false,
			},
		})
		return
	}

	// 使用加密数据
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"logs":      logs,
			"total":     len(logs),
			"page":      page,
			"page_size": pageSize,
			"encrypted": true,
		},
	})
}

// GetStats 获取蜘蛛统计
func (h *SpiderHandler) GetStats(c *gin.Context) {
	// 统计各类蜘蛛的访问次数
	var stats []struct {
		SpiderType int   `json:"spider_type"`
		Count      int64 `json:"count"`
		Name       string `json:"name"`
	}

	rows, err := h.db.Model(&models.SpiderLog{}).
		Select("spider_type, COUNT(*) as count").
		Group("spider_type").
		Rows()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}
	defer rows.Close()

	spiderNames := map[int]string{
		1: "百度", 2: "搜狗", 3: "360", 4: "必应", 5: "谷歌",
		6: "神马", 7: "Yandex", 8: "Coccoc", 9: "Naver",
	}

	for rows.Next() {
		var stat struct {
			SpiderType int   `json:"spider_type"`
			Count      int64 `json:"count"`
			Name       string `json:"name"`
		}
		rows.Scan(&stat.SpiderType, &stat.Count)
		stat.Name = spiderNames[stat.SpiderType]
		stats = append(stats, stat)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    stats,
	})
}

// GetTodayStats 获取今日统计
func (h *SpiderHandler) GetTodayStats(c *gin.Context) {
	today := time.Now().Format("2006-01-02")

	var todayCount int64
	h.db.Model(&models.SpiderLog{}).
		Where("DATE(created_at) = ?", today).
		Count(&todayCount)

	var yesterdayCount int64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	h.db.Model(&models.SpiderLog{}).
		Where("DATE(created_at) = ?", yesterday).
		Count(&yesterdayCount)

	// 计算增长率
	var growthRate float64
	if yesterdayCount > 0 {
		growthRate = float64(todayCount-yesterdayCount) / float64(yesterdayCount) * 100
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"today_count":     todayCount,
			"yesterday_count": yesterdayCount,
			"growth_rate":     growthRate,
		},
	})
}

// GetComparisonStats 获取对比统计数据
func (h *SpiderHandler) GetComparisonStats(c *gin.Context) {
	today := time.Now().Format("2006-01-02")
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	spiderNames := map[int]string{
		1: "百度", 2: "搜狗", 3: "360", 4: "必应", 5: "谷歌",
		6: "神马", 7: "Yandex", 8: "Coccoc", 9: "Naver",
	}

	var comparisonData []struct {
		SpiderType     int    `json:"spider_type"`
		SpiderName     string `json:"spider_name"`
		TodayCount     int64  `json:"today_count"`
		YesterdayCount int64  `json:"yesterday_count"`
	}

	// 获取所有蜘蛛类型的今日和昨日统计
	for spiderType, spiderName := range spiderNames {
		var todayCount, yesterdayCount int64

		// 今日统计
		h.db.Model(&models.SpiderLog{}).
			Where("spider_type = ? AND DATE(created_at) = ?", spiderType, today).
			Count(&todayCount)

		// 昨日统计
		h.db.Model(&models.SpiderLog{}).
			Where("spider_type = ? AND DATE(created_at) = ?", spiderType, yesterday).
			Count(&yesterdayCount)

		comparisonData = append(comparisonData, struct {
			SpiderType     int    `json:"spider_type"`
			SpiderName     string `json:"spider_name"`
			TodayCount     int64  `json:"today_count"`
			YesterdayCount int64  `json:"yesterday_count"`
		}{
			SpiderType:     spiderType,
			SpiderName:     spiderName,
			TodayCount:     todayCount,
			YesterdayCount: yesterdayCount,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    comparisonData,
	})
}

// GetHourlyStats 获取小时统计数据
func (h *SpiderHandler) GetHourlyStats(c *gin.Context) {
	date := c.Query("date")
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 验证日期格式
	_, err := time.Parse("2006-01-02", date)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "日期格式错误，请使用 YYYY-MM-DD 格式",
		})
		return
	}

	// 初始化24小时的数据
	hourlyData := make([]int64, 24)

	// 查询指定日期每小时的访问统计
	rows, err := h.db.Model(&models.SpiderLog{}).
		Select("HOUR(created_at) as hour, COUNT(*) as count").
		Where("DATE(created_at) = ?", date).
		Group("HOUR(created_at)").
		Rows()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}
	defer rows.Close()

	// 填充实际数据
	for rows.Next() {
		var hour int
		var count int64
		rows.Scan(&hour, &count)
		if hour >= 0 && hour < 24 {
			hourlyData[hour] = count
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    hourlyData,
	})
}

// GetChartData 获取图表数据
func (h *SpiderHandler) GetChartData(c *gin.Context) {
	days := c.DefaultQuery("days", "7")
	daysInt, _ := strconv.Atoi(days)

	// 获取最近N天的数据
	var chartData []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	for i := daysInt - 1; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		var count int64
		h.db.Model(&models.SpiderLog{}).
			Where("DATE(created_at) = ?", date).
			Count(&count)

		chartData = append(chartData, struct {
			Date  string `json:"date"`
			Count int64  `json:"count"`
		}{
			Date:  date,
			Count: count,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    chartData,
	})
}

// ClearLogs 清理日志
func (h *SpiderHandler) ClearLogs(c *gin.Context) {
	var req struct {
		Days int `json:"days" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 删除N天前的日志
	cutoffDate := time.Now().AddDate(0, 0, -req.Days)
	result := h.db.Where("created_at < ?", cutoffDate).Delete(&models.SpiderLog{})

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "清理成功",
		"data": gin.H{
			"deleted_count": result.RowsAffected,
		},
	})
}
