package handlers

import (
	"net/http"
	"seo-platform/internal/services"
	"seo-platform/internal/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// EncryptionHandler 加密功能处理器
type EncryptionHandler struct {
	db              *gorm.DB
	spiderRecorder  *services.SpiderRecorder
	templateCache   *services.TemplateCacheService
	crypto          *utils.CryptoManager
}

// NewEncryptionHandler 创建加密处理器
func NewEncryptionHandler(db *gorm.DB) *EncryptionHandler {
	spiderRecorder := services.NewSpiderRecorder(db)
	templateCache := services.NewTemplateCacheService(db)
	
	// 自动迁移表结构
	spiderRecorder.AutoMigrate()
	templateCache.AutoMigrate()
	
	return &EncryptionHandler{
		db:             db,
		spiderRecorder: spiderRecorder,
		templateCache:  templateCache,
		crypto:         utils.GetDefaultCrypto(),
	}
}

// RecordSpiderVisit 记录蜘蛛访问
// @Summary 记录蜘蛛访问（加密存储）
// @Description 记录蜘蛛访问日志并进行加密存储
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param request body SpiderVisitRequest true "蜘蛛访问信息"
// @Success 200 {object} Response
// @Router /api/encryption/spider/record [post]
func (eh *EncryptionHandler) RecordSpiderVisit(c *gin.Context) {
	var req SpiderVisitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 识别蜘蛛类型
	spiderType, identificationData := eh.spiderRecorder.IdentifySpider(req.UserAgent)
	
	// 记录访问
	err := eh.spiderRecorder.RecordSpiderVisit(
		spiderType,
		req.UserAgent,
		req.IP,
		req.URL,
		req.Referer,
		req.Domain,
		req.Method,
		req.StatusCode,
		req.ResponseTime,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":             "蜘蛛访问记录成功",
		"spider_type":         spiderType,
		"identification_data": identificationData,
	})
}

// GetEncryptedSpiderLogs 获取加密的蜘蛛日志
// @Summary 获取蜘蛛日志
// @Description 获取解密后的蜘蛛访问日志
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response
// @Router /api/encryption/spider/logs [get]
func (eh *EncryptionHandler) GetEncryptedSpiderLogs(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	
	offset := (page - 1) * limit
	
	logs, err := eh.spiderRecorder.GetDecryptedSpiderLogs(limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取蜘蛛日志成功",
		"data":    logs,
		"page":    page,
		"limit":   limit,
	})
}

// GetSpiderStats 获取蜘蛛统计数据
// @Summary 获取蜘蛛统计
// @Description 获取蜘蛛访问统计数据
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param date query string false "日期 YYYY-MM-DD" default("today")
// @Param spider_type query int false "蜘蛛类型" default(0)
// @Success 200 {object} Response
// @Router /api/encryption/spider/stats [get]
func (eh *EncryptionHandler) GetSpiderStats(c *gin.Context) {
	date := c.DefaultQuery("date", time.Now().Format("2006-01-02"))
	spiderType, _ := strconv.Atoi(c.DefaultQuery("spider_type", "0"))
	
	stats, err := eh.spiderRecorder.GetSpiderStats(date, spiderType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// 获取解压缩的详细数据
	detailData, _ := eh.spiderRecorder.GetDecompressedStatsData(stats)

	c.JSON(http.StatusOK, gin.H{
		"message":     "获取蜘蛛统计成功",
		"stats":       stats,
		"detail_data": detailData,
	})
}

// CacheTemplate 缓存模板
// @Summary 缓存模板
// @Description 加密缓存模板内容
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param request body TemplateCacheRequest true "模板缓存信息"
// @Success 200 {object} Response
// @Router /api/encryption/template/cache [post]
func (eh *EncryptionHandler) CacheTemplate(c *gin.Context) {
	var req TemplateCacheRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认TTL
	ttl := time.Duration(req.TTLSeconds) * time.Second
	if ttl == 0 {
		ttl = 24 * time.Hour // 默认24小时
	}
	
	cacheKey, err := eh.templateCache.CacheTemplate(
		req.TemplateID,
		req.TemplateContent,
		req.DynamicData,
		ttl,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "模板缓存成功",
		"cache_key": cacheKey,
	})
}

// GetCachedTemplate 获取缓存模板
// @Summary 获取缓存模板
// @Description 获取解密的缓存模板内容
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param cache_key path string true "缓存键"
// @Success 200 {object} Response
// @Router /api/encryption/template/cache/{cache_key} [get]
func (eh *EncryptionHandler) GetCachedTemplate(c *gin.Context) {
	cacheKey := c.Param("cache_key")
	
	content, err := eh.templateCache.GetCachedTemplate(cacheKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	if content == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "缓存不存在或已过期"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取缓存模板成功",
		"content": content,
	})
}

// GenerateDynamicContent 生成动态内容
// @Summary 生成动态内容
// @Description 生成加密的动态内容
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param request body DynamicContentRequest true "动态内容生成信息"
// @Success 200 {object} Response
// @Router /api/encryption/content/generate [post]
func (eh *EncryptionHandler) GenerateDynamicContent(c *gin.Context) {
	var req DynamicContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	content, err := eh.templateCache.GenerateEncryptedContent(
		req.TemplateContent,
		req.Data,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "动态内容生成成功",
		"content": content,
	})
}

// CompressOutput 压缩页面输出
// @Summary 压缩页面输出
// @Description 压缩并加密页面输出内容
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param request body CompressOutputRequest true "压缩输出信息"
// @Success 200 {object} Response
// @Router /api/encryption/output/compress [post]
func (eh *EncryptionHandler) CompressOutput(c *gin.Context) {
	var req CompressOutputRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认加密方法
	if req.Method == "" {
		req.Method = "xxtea"
	}
	
	compressedOutput, err := eh.templateCache.CompressAndEncryptOutput(
		req.Content,
		req.Method,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":           "页面输出压缩成功",
		"compressed_output": compressedOutput,
		"method":            req.Method,
		"original_size":     len(req.Content),
		"compressed_size":   len(compressedOutput),
	})
}

// DecompressOutput 解压缩页面输出
// @Summary 解压缩页面输出
// @Description 解压缩并解密页面输出内容
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param request body DecompressOutputRequest true "解压缩输出信息"
// @Success 200 {object} Response
// @Router /api/encryption/output/decompress [post]
func (eh *EncryptionHandler) DecompressOutput(c *gin.Context) {
	var req DecompressOutputRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	decompressedOutput, err := eh.templateCache.DecompressAndDecryptOutput(
		req.CompressedContent,
		req.Method,
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":             "页面输出解压缩成功",
		"decompressed_output": decompressedOutput,
		"method":              req.Method,
	})
}

// EncryptData 通用数据加密
// @Summary 通用数据加密
// @Description 使用指定方法加密数据
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param request body EncryptDataRequest true "加密数据信息"
// @Success 200 {object} Response
// @Router /api/encryption/data/encrypt [post]
func (eh *EncryptionHandler) EncryptData(c *gin.Context) {
	var req EncryptDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	dataBytes := []byte(req.Data)
	encryptedData, err := utils.EncryptSpiderData(dataBytes, req.Method)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 转换为Base64便于传输
	encryptedBase64 := eh.crypto.Base64Encrypt(encryptedData)

	c.JSON(http.StatusOK, gin.H{
		"message":        "数据加密成功",
		"encrypted_data": encryptedBase64,
		"method":         req.Method,
		"original_size":  len(req.Data),
		"encrypted_size": len(encryptedBase64),
	})
}

// DecryptData 通用数据解密
// @Summary 通用数据解密
// @Description 使用指定方法解密数据
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param request body DecryptDataRequest true "解密数据信息"
// @Success 200 {object} Response
// @Router /api/encryption/data/decrypt [post]
func (eh *EncryptionHandler) DecryptData(c *gin.Context) {
	var req DecryptDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 先Base64解码
	encryptedData, err := eh.crypto.Base64Decrypt(req.EncryptedData)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Base64解码失败: " + err.Error()})
		return
	}

	decryptedData, err := utils.DecryptSpiderData(encryptedData, req.Method)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "数据解密成功",
		"decrypted_data": string(decryptedData),
		"method":         req.Method,
	})
}

// GetCacheStats 获取缓存统计
// @Summary 获取缓存统计
// @Description 获取模板缓存统计信息
// @Tags 加密功能
// @Accept json
// @Produce json
// @Success 200 {object} Response
// @Router /api/encryption/cache/stats [get]
func (eh *EncryptionHandler) GetCacheStats(c *gin.Context) {
	stats, err := eh.templateCache.GetCacheStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取缓存统计成功",
		"stats":   stats,
	})
}

// CleanExpiredCache 清理过期缓存
// @Summary 清理过期缓存
// @Description 清理过期的模板缓存
// @Tags 加密功能
// @Accept json
// @Produce json
// @Success 200 {object} Response
// @Router /api/encryption/cache/clean [post]
func (eh *EncryptionHandler) CleanExpiredCache(c *gin.Context) {
	cleanedCount, err := eh.templateCache.CleanExpiredCache()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "清理过期缓存成功",
		"cleaned_count": cleanedCount,
	})
}

// CleanOldSpiderLogs 清理旧蜘蛛日志
// @Summary 清理旧蜘蛛日志
// @Description 清理指定天数前的蜘蛛日志
// @Tags 加密功能
// @Accept json
// @Produce json
// @Param days query int false "保留天数" default(30)
// @Success 200 {object} Response
// @Router /api/encryption/spider/clean [post]
func (eh *EncryptionHandler) CleanOldSpiderLogs(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
	
	cleanedCount, err := eh.spiderRecorder.CleanOldLogs(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "清理旧蜘蛛日志成功",
		"cleaned_count": cleanedCount,
		"days":          days,
	})
}

// 请求结构体定义

type SpiderVisitRequest struct {
	UserAgent    string `json:"user_agent" binding:"required"`
	IP           string `json:"ip" binding:"required"`
	URL          string `json:"url" binding:"required"`
	Referer      string `json:"referer"`
	Domain       string `json:"domain" binding:"required"`
	Method       string `json:"method" binding:"required"`
	StatusCode   int    `json:"status_code"`
	ResponseTime int64  `json:"response_time"`
}

type TemplateCacheRequest struct {
	TemplateID      uint                   `json:"template_id" binding:"required"`
	TemplateContent string                 `json:"template_content" binding:"required"`
	DynamicData     map[string]interface{} `json:"dynamic_data"`
	TTLSeconds      int                    `json:"ttl_seconds"`
}

type DynamicContentRequest struct {
	TemplateContent string                 `json:"template_content" binding:"required"`
	Data            map[string]interface{} `json:"data"`
}

type CompressOutputRequest struct {
	Content string `json:"content" binding:"required"`
	Method  string `json:"method"` // xor, des, xxtea, base64
}

type DecompressOutputRequest struct {
	CompressedContent string `json:"compressed_content" binding:"required"`
	Method            string `json:"method" binding:"required"`
}

type EncryptDataRequest struct {
	Data   string `json:"data" binding:"required"`
	Method string `json:"method" binding:"required"` // xor, des, xxtea
}

type DecryptDataRequest struct {
	EncryptedData string `json:"encrypted_data" binding:"required"`
	Method        string `json:"method" binding:"required"` // xor, des, xxtea
}

type Response struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}