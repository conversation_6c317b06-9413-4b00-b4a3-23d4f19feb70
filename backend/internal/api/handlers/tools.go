package handlers

import (
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ToolsHandler struct {
	db *gorm.DB
}

func NewToolsHandler(db *gorm.DB) *ToolsHandler {
	return &ToolsHandler{
		db: db,
	}
}

// SpiderSimulator 蜘蛛模拟器
func (h *<PERSON>lsHandler) SpiderSimulator(c *gin.Context) {
	var req struct {
		URL    string `json:"url" binding:"required"`
		Spider string `json:"spider" binding:"required"`
		Mode   string `json:"mode"`
	}

	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取对应蜘蛛的User-Agent
	userAgent := getSpiderUserAgent(req.<PERSON>)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建请求
	request, err := http.NewRequest("GET", req.URL, nil)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的URL",
		})
		return
	}

	// 设置请求头
	request.Header.Set("User-Agent", userAgent)
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	request.Header.Set("Accept-Language", "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3")
	request.Header.Set("Accept-Encoding", "gzip, deflate")
	request.Header.Set("Connection", "keep-alive")

	// 发送请求
	start := time.Now()
	response, err := client.Do(request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "请求失败",
			"error":   err.Error(),
		})
		return
	}
	defer response.Body.Close()

	responseTime := time.Since(start).Milliseconds()

	// 读取响应内容
	body, err := io.ReadAll(response.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "读取响应失败",
		})
		return
	}

	// 构建响应头字符串
	headersStr := ""
	for key, values := range response.Header {
		headersStr += fmt.Sprintf("%s: %s\n", key, strings.Join(values, ", "))
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "模拟成功",
		"data": gin.H{
			"statusCode":    response.StatusCode,
			"statusText":    response.Status,
			"targetUrl":     req.URL,
			"userAgent":     userAgent,
			"responseTime":  responseTime,
			"contentLength": len(body),
			"server":        response.Header.Get("Server"),
			"headers":       headersStr,
			"content":       string(body),
		},
	})
}

// HijackDetector 404劫持检测器
func (h *ToolsHandler) HijackDetector(c *gin.Context) {
	var req struct {
		URL    string `json:"url" binding:"required"`
		Engine string `json:"engine" binding:"required"`
		Device string `json:"device"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 获取搜索引擎Referer
	referer := getEngineReferer(req.Engine)
	userAgent := getDeviceUserAgent(req.Device)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建请求
	request, err := http.NewRequest("GET", req.URL, nil)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的URL",
		})
		return
	}

	// 设置请求头
	request.Header.Set("User-Agent", userAgent)
	request.Header.Set("Referer", referer)
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")

	// 发送请求
	start := time.Now()
	response, err := client.Do(request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "请求失败",
		})
		return
	}
	defer response.Body.Close()

	responseTime := time.Since(start).Milliseconds()

	// 读取响应内容
	body, err := io.ReadAll(response.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "读取响应失败",
		})
		return
	}

	// 构建响应头字符串
	headersStr := ""
	for key, values := range response.Header {
		headersStr += fmt.Sprintf("%s: %s\n", key, strings.Join(values, ", "))
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "检测完成",
		"data": gin.H{
			"statusCode":   response.StatusCode,
			"statusText":   response.Status,
			"targetUrl":    req.URL,
			"referer":      referer,
			"userAgent":    userAgent,
			"responseTime": responseTime,
			"headers":      headersStr,
			"content":      string(body),
		},
	})
}

// HtaccessGenerator htaccess生成器
func (h *ToolsHandler) HtaccessGenerator(c *gin.Context) {
	var req struct {
		Domains []string `json:"domains" binding:"required"`
		Options map[string]interface{} `json:"options"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 生成.htaccess代码
	code := generateHtaccessCode(req.Domains, req.Options)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "生成成功",
		"data": gin.H{
			"code": code,
		},
	})
}

// IIS6Generator IIS6生成器
func (h *ToolsHandler) IIS6Generator(c *gin.Context) {
	var req struct {
		Domains []string `json:"domains" binding:"required"`
		Options map[string]interface{} `json:"options"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 生成IIS6重写规则
	code := generateIIS6Code(req.Domains, req.Options)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "生成成功",
		"data": gin.H{
			"code": code,
		},
	})
}

// FileIndexEncrypt 文件索引加密
func (h *ToolsHandler) FileIndexEncrypt(c *gin.Context) {
	var req struct {
		EncryptMode     string   `json:"encrypt_mode" binding:"required"`
		ProtectionLevel string   `json:"protection_level"`
		ProtectedDirs   []string `json:"protected_dirs"`
		Options         map[string]interface{} `json:"options"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 生成文件保护配置
	code := generateFileProtectionCode(req.EncryptMode, req.ProtectionLevel, req.ProtectedDirs, req.Options)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "生成成功",
		"data": gin.H{
			"code": code,
		},
	})
}

// JSEncrypt JS加密
func (h *ToolsHandler) JSEncrypt(c *gin.Context) {
	var req struct {
		SourceCode string `json:"source_code" binding:"required"`
		Method     string `json:"method"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 简单的Base64编码作为加密
	encryptedCode := base64.StdEncoding.EncodeToString([]byte(req.SourceCode))

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "加密成功",
		"data": gin.H{
			"encrypted_code": encryptedCode,
			"method":         "base64",
		},
	})
}

// JSDecrypt JS解密
func (h *ToolsHandler) JSDecrypt(c *gin.Context) {
	var req struct {
		EncryptedCode string `json:"encrypted_code" binding:"required"`
		Method        string `json:"method"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 尝试Base64解码
	decryptedBytes, err := base64.StdEncoding.DecodeString(req.EncryptedCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "解密失败，无效的编码",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "解密成功",
		"data": gin.H{
			"decrypted_code": string(decryptedBytes),
			"method":         "base64",
		},
	})
}

// IISDomainExport IIS域名导出
func (h *ToolsHandler) IISDomainExport(c *gin.Context) {
	var req struct {
		SiteIDs []string `json:"site_ids"`
		Format  string   `json:"format"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 模拟IIS站点数据
	sites := []map[string]interface{}{
		{
			"site_id":   "1",
			"site_name": "Default Web Site",
			"bindings":  []string{"*:80:", "*:443:"},
			"path":      "C:\\inetpub\\wwwroot",
			"status":    "Started",
		},
		{
			"site_id":   "2",
			"site_name": "Example Site",
			"bindings":  []string{"*:80:example.com", "*:443:example.com"},
			"path":      "C:\\inetpub\\example",
			"status":    "Started",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "导出成功",
		"data": gin.H{
			"sites": sites,
		},
	})
}

// GetIISSites 获取IIS站点列表
func (h *ToolsHandler) GetIISSites(c *gin.Context) {
	// 模拟IIS站点列表
	sites := []map[string]interface{}{
		{
			"id":     "1",
			"name":   "Default Web Site",
			"status": "Started",
			"port":   "80",
		},
		{
			"id":     "2",
			"name":   "Example Site",
			"status": "Started",
			"port":   "80",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    sites,
	})
}

// 辅助函数

func getSpiderUserAgent(spider string) string {
	spiderMap := map[string]string{
		"360pc":       "Mozilla/5.0 (compatible; 360Spider; +http://webscan.360.cn)",
		"360mobile":   "Mozilla/5.0 (iPhone; CPU iPhone OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A465 Safari/9537.53 (compatible; 360Spider; +http://webscan.360.cn)",
		"baidupc":     "Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)",
		"baidumobile": "Mozilla/5.0 (Linux; u; Android 4.2.2; zh-cn; ) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile Safari/10600.6.3 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)",
		"sogoupc":     "Sogou web spider/4.0(+http://www.sogou.com/docs/help/webmasters.htm#07)",
		"sogoumobile": "Mozilla/5.0 (iPhone; CPU iPhone OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9A334 (compatible; Sogou web spider/4.0;+http://www.sogou.com/docs/help/webmasters.htm#07)",
		"shenma":      "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 (compatible; YisouSpider; +http://www.yisou.com/help/spider.html)",
		"normal":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
	}

	if ua, exists := spiderMap[spider]; exists {
		return ua
	}
	return spiderMap["normal"]
}

func getEngineReferer(engine string) string {
	engineMap := map[string]string{
		"sogou": "https://www.sogou.com/web?query=test",
		"baidu": "https://www.baidu.com/s?wd=test",
		"shenma": "https://m.sm.cn/s?q=test",
		"360": "https://www.so.com/s?q=test",
	}

	if referer, exists := engineMap[engine]; exists {
		return referer
	}
	return engineMap["sogou"]
}

func getDeviceUserAgent(device string) string {
	deviceMap := map[string]string{
		"desktop": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"mobile":  "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
		"uc":      "Mozilla/5.0 (Linux; U; Android 8.1.0; zh-CN; EML-AL00 Build/HUAWEIEML-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/11.9.4.974 UWS/********* Mobile Safari/537.36",
	}

	if ua, exists := deviceMap[device]; exists {
		return ua
	}
	return deviceMap["desktop"]
}

func generateHtaccessCode(domains []string, options map[string]interface{}) string {
	code := "# Generated .htaccess file for site hijacking\n"
	code += "# Generated by SEO Platform\n\n"
	code += "RewriteEngine On\n\n"

	for _, domain := range domains {
		if domain != "" {
			code += fmt.Sprintf("# Redirect rule for %s\n", domain)
			code += fmt.Sprintf("RewriteCond %%{HTTP_HOST} ^(www\\.)?%s$ [NC]\n", strings.ReplaceAll(domain, ".", "\\."))
			code += "RewriteCond %{REQUEST_URI} !^/admin\n"
			code += "RewriteRule ^(.*)$ http://target-site.com/$1 [R=301,L]\n\n"
		}
	}

	return code
}

func generateIIS6Code(domains []string, options map[string]interface{}) string {
	code := "# IIS 6 ISAPI Rewrite2 Configuration\n"
	code += "# Generated by SEO Platform\n\n"

	for _, domain := range domains {
		if domain != "" {
			code += fmt.Sprintf("# Redirect rule for %s\n", domain)
			code += fmt.Sprintf("RewriteCond Host: ^(www\\.)?%s$\n", strings.ReplaceAll(domain, ".", "\\."))
			code += "RewriteRule (.*) http://target-site.com/$1 [I,RP]\n\n"
		}
	}

	return code
}

func generateFileProtectionCode(encryptMode, protectionLevel string, protectedDirs []string, options map[string]interface{}) string {
	switch encryptMode {
	case "htaccess":
		return generateHtaccessProtection(protectionLevel, protectedDirs, options)
	case "webconfig":
		return generateWebConfigProtection(protectionLevel, protectedDirs, options)
	case "nginx":
		return generateNginxProtection(protectionLevel, protectedDirs, options)
	case "php":
		return generatePHPProtection(protectionLevel, protectedDirs, options)
	default:
		return generateHtaccessProtection(protectionLevel, protectedDirs, options)
	}
}

func generateHtaccessProtection(level string, dirs []string, options map[string]interface{}) string {
	code := "# File protection configuration\n"
	code += "# Generated by SEO Platform\n\n"

	// 禁止目录浏览
	code += "Options -Indexes\n\n"

	// 保护特定目录
	for _, dir := range dirs {
		if dir != "" {
			code += fmt.Sprintf("<Directory \"%s\">\n", dir)
			code += "    Require all denied\n"
			code += "</Directory>\n"
		}
	}

	return code
}

func generateWebConfigProtection(level string, dirs []string, options map[string]interface{}) string {
	code := "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
	code += "<configuration>\n"
	code += "  <system.webServer>\n"
	code += "    <directoryBrowse enabled=\"false\" />\n"
	code += "  </system.webServer>\n"
	code += "</configuration>\n"

	return code
}

func generateNginxProtection(level string, dirs []string, options map[string]interface{}) string {
	code := "# Nginx file protection configuration\n"
	code += "# Generated by SEO Platform\n\n"

	// 禁止访问特定目录
	for _, dir := range dirs {
		if dir != "" {
			code += fmt.Sprintf("location %s {\n", dir)
			code += "    deny all;\n"
			code += "}\n\n"
		}
	}

	return code
}

func generatePHPProtection(level string, dirs []string, options map[string]interface{}) string {
	code := "<?php\n"
	code += "// File protection script\n"
	code += "// Generated by SEO Platform\n\n"
	code += "function checkFileAccess() {\n"
	code += "    $requestURI = $_SERVER['REQUEST_URI'];\n\n"

	// 检查保护目录
	for _, dir := range dirs {
		if dir != "" {
			code += fmt.Sprintf("    if (strpos($requestURI, '%s') === 0) {\n", dir)
			code += "        http_response_code(403);\n"
			code += "        die('Access Denied');\n"
			code += "    }\n\n"
		}
	}

	code += "    return true;\n"
	code += "}\n\n"
	code += "checkFileAccess();\n"
	code += "?>\n"

	return code
}
