package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"seo-platform/internal/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type SEOHandler struct {
	db *gorm.DB
}

func NewSEOHandler(db *gorm.DB) *SEOHandler {
	return &SEOHandler{
		db: db,
	}
}

// GetContent SEO内容输出
func (h *SEOHandler) GetContent(c *gin.Context) {
	domain := c.Query("domain")
	url := c.Query("url")
	contentType := c.DefaultQuery("type", "article")

	// 检测是否为搜索引擎蜘蛛
	userAgent := c.<PERSON>eader("User-Agent")
	spiderType := h.detectSpider(userAgent)

	// 记录蜘蛛访问
	if spiderType > 0 {
		go h.logSpiderVisit(spiderType, userAgent, c.ClientIP(), url, c.<PERSON>eader("Referer"), domain, c.Request.Method)
	}

	// 从缓存或数据库获取内容
	content, err := h.getContentFromCache(domain, url, contentType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取内容失败",
		})
		return
	}

	// 如果是蜘蛛访问，返回SEO优化的内容
	if spiderType > 0 {
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(http.StatusOK, content.Content)
		return
	}

	// 普通用户访问，返回JSON格式
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    content,
	})
}

// LogSpider 记录蜘蛛访问
func (h *SEOHandler) LogSpider(c *gin.Context) {
	var req struct {
		UserAgent  string `json:"user_agent" binding:"required"`
		IP         string `json:"ip" binding:"required"`
		URL        string `json:"url" binding:"required"`
		Referer    string `json:"referer"`
		Domain     string `json:"domain"`
		Method     string `json:"method"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	spiderType := h.detectSpider(req.UserAgent)
	if spiderType == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "非搜索引擎蜘蛛",
		})
		return
	}

	h.logSpiderVisit(spiderType, req.UserAgent, req.IP, req.URL, req.Referer, req.Domain, req.Method)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "记录成功",
	})
}

// GetSitemap 获取站点地图
func (h *SEOHandler) GetSitemap(c *gin.Context) {
	domain := c.Query("domain")
	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "域名参数必填",
		})
		return
	}

	// 生成sitemap XML
	sitemap := h.generateSitemap(domain)

	c.Header("Content-Type", "application/xml; charset=utf-8")
	c.String(http.StatusOK, sitemap)
}

// detectSpider 检测搜索引擎蜘蛛
func (h *SEOHandler) detectSpider(userAgent string) int {
	userAgent = strings.ToLower(userAgent)

	if strings.Contains(userAgent, "baiduspider") {
		return 1 // 百度
	} else if strings.Contains(userAgent, "sogou") {
		return 2 // 搜狗
	} else if strings.Contains(userAgent, "360spider") {
		return 3 // 360
	} else if strings.Contains(userAgent, "bingbot") {
		return 4 // 必应
	} else if strings.Contains(userAgent, "googlebot") {
		return 5 // 谷歌
	} else if strings.Contains(userAgent, "yisouspider") {
		return 6 // 神马
	} else if strings.Contains(userAgent, "yandexbot") {
		return 7 // Yandex
	} else if strings.Contains(userAgent, "coccocbot") {
		return 8 // Coccoc
	} else if strings.Contains(userAgent, "naverbot") {
		return 9 // Naver
	}

	return 0 // 非蜘蛛
}

// logSpiderVisit 记录蜘蛛访问
func (h *SEOHandler) logSpiderVisit(spiderType int, userAgent, ip, url, referer, domain, method string) {
	spiderLog := models.SpiderLog{
		SpiderType:   spiderType,
		UserAgent:    userAgent,
		IP:           ip,
		URL:          url,
		Referer:      referer,
		Domain:       domain,
		Method:       method,
		StatusCode:   200,
		ResponseTime: 0, // 实际响应时间需要通过监控获取
		VisitTime:    time.Now().Unix(),
	}

	h.db.Create(&spiderLog)
}

// getContentFromCache 从缓存获取内容
func (h *SEOHandler) getContentFromCache(domain, url, contentType string) (*models.Content, error) {
	var content models.Content

	// 先从缓存表查找
	cacheKey := domain + ":" + url
	var cache models.Cache
	if err := h.db.Where("key = ? AND (expire_at IS NULL OR expire_at > ?)", cacheKey, time.Now()).First(&cache).Error; err == nil {
		// 缓存命中，解析内容
		content = models.Content{
			Type:    contentType,
			Title:   "缓存内容",
			Content: cache.Value,
			Domain:  domain,
			URL:     url,
		}
		return &content, nil
	}

	// 缓存未命中，生成新内容
	content = models.Content{
		Type:    contentType,
		Title:   h.generateTitle(domain, url),
		Content: h.generateContent(domain, url, contentType),
		Domain:  domain,
		URL:     url,
	}

	// 保存到内容表
	h.db.Create(&content)

	// 保存到缓存表
	cache = models.Cache{
		Key:      cacheKey,
		Value:    content.Content,
		ExpireAt: &[]time.Time{time.Now().Add(24 * time.Hour)}[0], // 24小时过期
	}
	h.db.Create(&cache)

	return &content, nil
}

// generateTitle 生成标题
func (h *SEOHandler) generateTitle(domain, url string) string {
	return "SEO优化页面 - " + domain + url
}

// generateContent 生成内容
func (h *SEOHandler) generateContent(domain, url, contentType string) string {
	// 使用随机User-Agent生成内容
	randomUA := utils.GetRandomUserAgent()

	html := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>` + h.generateTitle(domain, url) + `</title>
    <meta name="description" content="这是一个SEO优化的页面">
    <meta name="keywords" content="SEO,优化,搜索引擎">
</head>
<body>
    <h1>` + h.generateTitle(domain, url) + `</h1>
    <p>这是动态生成的SEO内容，访问时间：` + time.Now().Format("2006-01-02 15:04:05") + `</p>
    <p>域名：` + domain + `</p>
    <p>路径：` + url + `</p>
    <p>内容类型：` + contentType + `</p>
    <p>随机User-Agent：` + randomUA + `</p>
    <!-- 更多SEO优化内容 -->
</body>
</html>`

	return html
}

// generateSitemap 生成站点地图
func (h *SEOHandler) generateSitemap(domain string) string {
	sitemap := `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>http://` + domain + `/</loc>
        <lastmod>` + time.Now().Format("2006-01-02") + `</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>http://` + domain + `/about</loc>
        <lastmod>` + time.Now().Format("2006-01-02") + `</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
</urlset>`

	return sitemap
}
