package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProjectHandler struct {
	db *gorm.DB
}

func NewProjectHandler(db *gorm.DB) *ProjectHandler {
	return &ProjectHandler{
		db: db,
	}
}

// List 获取项目列表
func (h *ProjectHandler) List(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10"))
	keyword := c.Query("keyword")
	status := c.Query("status")
	projectType := c.Query("type")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.Project{})
	if keyword != "" {
		query = query.Where("project_name LIKE ? OR project_url LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	if status != "" {
		query = query.Where("project_status = ?", status)
	}
	if projectType != "" {
		query = query.Where("project_type = ?", projectType)
	}

	var total int64
	query.Count(&total)

	var projects []models.Project
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&projects).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	// 为每个项目添加扩展信息
	var enrichedProjects []map[string]interface{}
	for _, project := range projects {
		// 获取关键词组数量
		var keywordGroupCount int64
		h.db.Model(&models.Keyword{}).Where("project_id = ?", project.ID).Count(&keywordGroupCount)

		// 获取蜘蛛访问数量
		var spiderCount int64
		h.db.Model(&models.SpiderLog{}).Where("domain = ?", project.ProjectURL).Count(&spiderCount)

		// 计算SEO评分（基于项目数据）
		seoScore := calculateSEOScore(project)

		enrichedProject := map[string]interface{}{
			"id":                project.ID,
			"site_id":           project.SiteID,
			"project_name":      project.ProjectName,
			"project_url":       project.ProjectURL,
			"project_type":      project.ProjectType,
			"project_status":    project.ProjectStatus,
			"created_at":        project.CreatedAt,
			"updated_at":        project.UpdatedAt,
			"keyword_groups":    keywordGroupCount,
			"url_rules_count":   0,
			"spider_count":      spiderCount,
			"seo_score":         seoScore,
			"template_name":     "",
			"ad_url":           "",
		}
		enrichedProjects = append(enrichedProjects, enrichedProject)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      enrichedProjects,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// Create 创建项目
func (h *ProjectHandler) Create(c *gin.Context) {
	var req struct {
		ProjectName    string `json:"project_name" binding:"required"`
		ProjectURL     string `json:"project_url" binding:"required"`
		ProjectType    int    `json:"project_type" binding:"required"`
		ProjectStatus  int    `json:"project_status"`
		MonitorStatus  int    `json:"monitor_status"`
		Keyword        string `json:"keyword"`
		SEOOption      string `json:"seo_option"`
		CacheOption    string `json:"cache_option"`
		ElinkOption    string `json:"elink_option"`
		PushOption     string `json:"push_option"`
		OtherOption    string `json:"other_option"`
		ProjectCharset int    `json:"project_charset"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 生成站点ID
	siteID := generateSiteID()

	project := models.Project{
		SiteID:         siteID,
		ProjectName:    req.ProjectName,
		ProjectURL:     req.ProjectURL,
		ProjectType:    req.ProjectType,
		ProjectStatus:  req.ProjectStatus,
		MonitorStatus:  req.MonitorStatus,
		Keyword:        req.Keyword,
		SEOOption:      req.SEOOption,
		CacheOption:    req.CacheOption,
		ElinkOption:    req.ElinkOption,
		PushOption:     req.PushOption,
		OtherOption:    req.OtherOption,
		ProjectCharset: req.ProjectCharset,
		AddTime:        time.Now().Unix(),
		UpdateTime:     time.Now().Unix(),
	}

	if err := h.db.Create(&project).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    project,
	})
}

// Get 获取项目详情
func (h *ProjectHandler) Get(c *gin.Context) {
	id := c.Param("id")

	var project models.Project
	if err := h.db.First(&project, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "项目不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    project,
	})
}

// Update 更新项目
func (h *ProjectHandler) Update(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		ProjectName    string `json:"project_name"`
		ProjectURL     string `json:"project_url"`
		ProjectType    int    `json:"project_type"`
		ProjectStatus  int    `json:"project_status"`
		MonitorStatus  int    `json:"monitor_status"`
		Keyword        string `json:"keyword"`
		SEOOption      string `json:"seo_option"`
		CacheOption    string `json:"cache_option"`
		ElinkOption    string `json:"elink_option"`
		PushOption     string `json:"push_option"`
		OtherOption    string `json:"other_option"`
		ProjectCharset int    `json:"project_charset"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	updates := map[string]interface{}{
		"project_name":    req.ProjectName,
		"project_url":     req.ProjectURL,
		"project_type":    req.ProjectType,
		"project_status":  req.ProjectStatus,
		"monitor_status":  req.MonitorStatus,
		"keyword":         req.Keyword,
		"seo_option":      req.SEOOption,
		"cache_option":    req.CacheOption,
		"elink_option":    req.ElinkOption,
		"push_option":     req.PushOption,
		"other_option":    req.OtherOption,
		"project_charset": req.ProjectCharset,
		"update_time":     time.Now().Unix(),
	}

	if err := h.db.Model(&models.Project{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// Delete 删除项目
func (h *ProjectHandler) Delete(c *gin.Context) {
	id := c.Param("id")

	if err := h.db.Delete(&models.Project{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// Sync 同步项目到控制端
func (h *ProjectHandler) Sync(c *gin.Context) {
	id := c.Param("id")

	var project models.Project
	if err := h.db.First(&project, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "项目不存在",
		})
		return
	}

	// 这里实现同步逻辑
	// 将项目配置同步到缓存表

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "同步成功",
	})
}

// GetStats 获取项目统计
func (h *ProjectHandler) GetStats(c *gin.Context) {
	id := c.Param("id")

	var project models.Project
	if err := h.db.First(&project, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "项目不存在",
		})
		return
	}

	// 获取蜘蛛访问统计
	var spiderStats []struct {
		SpiderType int   `json:"spider_type"`
		Count      int64 `json:"count"`
	}

	h.db.Model(&models.SpiderLog{}).
		Select("spider_type, COUNT(*) as count").
		Where("domain = ?", project.ProjectURL).
		Group("spider_type").
		Find(&spiderStats)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"project":      project,
			"spider_stats": spiderStats,
		},
	})
}

// BatchDelete 批量删除项目
func (h *ProjectHandler) BatchDelete(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	if len(req.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请选择要删除的项目",
		})
		return
	}

	// 执行批量删除
	if err := h.db.Where("id IN ?", req.IDs).Delete(&models.Project{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量删除成功",
	})
}

// BatchUpdateStatus 批量更新状态
func (h *ProjectHandler) BatchUpdateStatus(c *gin.Context) {
	var req struct {
		IDs    []uint `json:"ids" binding:"required"`
		Status int    `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	if len(req.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请选择要更新的项目",
		})
		return
	}

	// 执行批量状态更新
	if err := h.db.Model(&models.Project{}).Where("id IN ?", req.IDs).Update("project_status", req.Status).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量更新成功",
	})
}

// GetExtendedInfo 获取项目扩展信息
func (h *ProjectHandler) GetExtendedInfo(c *gin.Context) {
	id := c.Param("id")

	var project models.Project
	if err := h.db.First(&project, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "项目不存在",
		})
		return
	}

	// 获取关联的关键词组数量
	var keywordGroupCount int64
	h.db.Model(&models.Keyword{}).Where("project_id = ?", id).Count(&keywordGroupCount)

	// 获取URL规则数量
	var urlRulesCount int64
	// 这里假设有URL规则表，如果没有可以设为0
	// h.db.Model(&models.URLRule{}).Where("project_id = ?", id).Count(&urlRulesCount)
	urlRulesCount = 0

	// 获取蜘蛛访问数量
	var spiderCount int64
	h.db.Model(&models.SpiderLog{}).Where("domain = ?", project.ProjectURL).Count(&spiderCount)

	// 计算SEO评分（基于项目数据）
	seoScore := calculateSEOScore(project)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"keyword_groups":   keywordGroupCount,
			"url_rules_count":  urlRulesCount,
			"spider_count":     spiderCount,
			"seo_score":        seoScore,
			"template_name":    "",
			"ad_url":          "",
		},
	})
}

// generateSiteID 生成站点ID
func generateSiteID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}

// calculateSEOScore 计算SEO评分
func calculateSEOScore(project models.Project) int {
	// 基于项目实际数据计算SEO评分
	score := 50 // 基础分
	
	// 根据项目状态调整分数
	if project.ProjectStatus == 1 {
		score += 20
	}
	
	// 根据项目类型调整分数
	if project.ProjectType == 1 {
		score += 15
	}
	
	// 确保分数在合理范围内
	if score > 100 {
		score = 100
	}
	if score < 0 {
		score = 0
	}
	
	return score
}
