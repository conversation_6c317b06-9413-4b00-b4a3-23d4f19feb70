package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MonitorHandler struct {
	db *gorm.DB
}

func NewMonitorHandler(db *gorm.DB) *MonitorHandler {
	return &MonitorHandler{
		db: db,
	}
}

// ListMonitors 获取监控列表
func (h *MonitorHandler) ListMonitors(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("page_size", "10"))
	keyword := c.Query("keyword")
	siteID := c.Query("site_id")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.Monitor{})
	if keyword != "" {
		query = query.Where("name LIKE ? OR url LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	if siteID != "" {
		query = query.Where("site_id = ?", siteID)
	}

	var total int64
	query.Count(&total)

	var monitors []models.Monitor
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&monitors).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      monitors,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// CreateMonitor 创建监控
func (h *MonitorHandler) CreateMonitor(c *gin.Context) {
	var req struct {
		Name             string `json:"name" binding:"required"`
		URL              string `json:"url" binding:"required"`
		SiteID           string `json:"site_id" binding:"required"`
		JumpException    int    `json:"jump_exception"`
		SpiderException  int    `json:"spider_exception"`
		WebsiteException int    `json:"website_exception"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 检查URL是否已存在
	var count int64
	h.db.Model(&models.Monitor{}).Where("url = ?", req.URL).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "监控URL已存在",
		})
		return
	}

	monitor := models.Monitor{
		Name:             req.Name,
		URL:              req.URL,
		SiteID:           req.SiteID,
		JumpException:    req.JumpException,
		SpiderException:  req.SpiderException,
		WebsiteException: req.WebsiteException,
		AddTime:          time.Now().Unix(),
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if err := h.db.Create(&monitor).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    monitor,
	})
}

// GetMonitor 获取监控详情
func (h *MonitorHandler) GetMonitor(c *gin.Context) {
	id := c.Param("id")

	var monitor models.Monitor
	if err := h.db.First(&monitor, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "监控不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    monitor,
	})
}

// UpdateMonitor 更新监控
func (h *MonitorHandler) UpdateMonitor(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		Name             string `json:"name"`
		URL              string `json:"url"`
		SiteID           string `json:"site_id"`
		JumpException    int    `json:"jump_exception"`
		SpiderException  int    `json:"spider_exception"`
		WebsiteException int    `json:"website_exception"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 如果更新URL，检查是否已存在
	if req.URL != "" {
		var count int64
		h.db.Model(&models.Monitor{}).Where("url = ? AND id != ?", req.URL, id).Count(&count)
		if count > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "监控URL已存在",
			})
			return
		}
	}

	req_map := map[string]interface{}{
		"updated_at": time.Now(),
	}
	if req.Name != "" {
		req_map["name"] = req.Name
	}
	if req.URL != "" {
		req_map["url"] = req.URL
	}
	if req.SiteID != "" {
		req_map["site_id"] = req.SiteID
	}
	req_map["jump_exception"] = req.JumpException
	req_map["spider_exception"] = req.SpiderException
	req_map["website_exception"] = req.WebsiteException

	if err := h.db.Model(&models.Monitor{}).Where("id = ?", id).Updates(req_map).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteMonitor 删除监控
func (h *MonitorHandler) DeleteMonitor(c *gin.Context) {
	id := c.Param("id")

	if err := h.db.Delete(&models.Monitor{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// BatchDeleteMonitors 批量删除监控
func (h *MonitorHandler) BatchDeleteMonitors(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := h.db.Where("id IN ?", req.IDs).Delete(&models.Monitor{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量删除成功",
	})
}

// GetMonitorStats 获取监控统计信息
func (h *MonitorHandler) GetMonitorStats(c *gin.Context) {
	var stats struct {
		TotalMonitors    int64 `json:"total_monitors"`
		JumpExceptions   int64 `json:"jump_exceptions"`
		SpiderExceptions int64 `json:"spider_exceptions"`
		WebsiteExceptions int64 `json:"website_exceptions"`
	}

	// 总监控数
	h.db.Model(&models.Monitor{}).Count(&stats.TotalMonitors)
	
	// 跳转异常数
	h.db.Model(&models.Monitor{}).Where("jump_exception = 1").Count(&stats.JumpExceptions)
	
	// 蜘蛛异常数
	h.db.Model(&models.Monitor{}).Where("spider_exception = 1").Count(&stats.SpiderExceptions)
	
	// 网站异常数
	h.db.Model(&models.Monitor{}).Where("website_exception = 1").Count(&stats.WebsiteExceptions)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    stats,
	})
}

// CheckMonitorHealth 检查监控健康状态
func (h *MonitorHandler) CheckMonitorHealth(c *gin.Context) {
	id := c.Param("id")

	var monitor models.Monitor
	if err := h.db.First(&monitor, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "监控不存在",
		})
		return
	}

	// 这里可以实现实际的健康检查逻辑
	// 比如发送HTTP请求检查URL是否可访问
	health := gin.H{
		"monitor_id":        monitor.ID,
		"url":               monitor.URL,
		"status":            "unknown", // 需要实现真实监控
		"response_time":     "0ms",     // 需要实现真实监控
		"last_check_time":   time.Now(),
		"jump_exception":    monitor.JumpException == 1,
		"spider_exception":  monitor.SpiderException == 1,
		"website_exception": monitor.WebsiteException == 1,
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "检查完成",
		"data":    health,
	})
}

// GetMonitorHistory 获取监控历史记录
func (h *MonitorHandler) GetMonitorHistory(c *gin.Context) {
	id := c.Param("id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	// 这里可以从监控历史表中查询数据
	// TODO: 实现真实的监控数据获取
	history := []gin.H{
		{
			"id":            1,
			"monitor_id":    id,
			"check_time":    time.Now().Add(-time.Hour),
			"status":        "success",
			"response_time": "98ms",
			"error_message": "",
		},
		{
			"id":            2,
			"monitor_id":    id,
			"check_time":    time.Now().Add(-2 * time.Hour),
			"status":        "failed",
			"response_time": "0ms",
			"error_message": "连接超时",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      history,
			"total":     2,
			"page":      page,
			"page_size": pageSize,
		},
	})
}