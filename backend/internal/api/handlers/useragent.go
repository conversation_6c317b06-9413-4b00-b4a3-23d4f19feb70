package handlers

import (
	"net/http"
	"seo-platform/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserAgentHandler struct {
	db *gorm.DB
}

func NewUserAgentHandler(db *gorm.DB) *UserAgentHandler {
	return &UserAgentHandler{
		db: db,
	}
}

// GetRandomUserAgent 获取随机User-Agent
func (h *UserAgentHandler) GetRandomUserAgent(c *gin.Context) {
	keyword := c.Query("keyword")
	
	var userAgent string
	if keyword != "" {
		userAgent = utils.GetRandomUserAgentByKeyword(keyword)
	} else {
		userAgent = utils.GetRandomUserAgent()
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"user_agent": userAgent,
		},
	})
}

// GetUserAgentList 获取User-Agent列表
func (h *UserAgentHandler) GetUserAgentList(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON><PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	keyword := c.Query("keyword")

	manager := utils.GetUserAgentManager()
	
	var userAgents []string
	if keyword != "" {
		userAgents = manager.FilterByKeyword(keyword)
	} else {
		userAgents = manager.GetAllUserAgents()
	}

	total := len(userAgents)
	
	// 分页处理
	start := (page - 1) * pageSize
	end := start + pageSize
	
	if start >= total {
		userAgents = []string{}
	} else {
		if end > total {
			end = total
		}
		userAgents = userAgents[start:end]
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      userAgents,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetUserAgentStats 获取User-Agent统计信息
func (h *UserAgentHandler) GetUserAgentStats(c *gin.Context) {
	manager := utils.GetUserAgentManager()
	
	// 统计不同浏览器的数量
	stats := make(map[string]int)
	allUserAgents := manager.GetAllUserAgents()
	
	for _, ua := range allUserAgents {
		if contains(ua, "Chrome") {
			stats["Chrome"]++
		} else if contains(ua, "Firefox") {
			stats["Firefox"]++
		} else if contains(ua, "Safari") && !contains(ua, "Chrome") {
			stats["Safari"]++
		} else if contains(ua, "Edge") {
			stats["Edge"]++
		} else if contains(ua, "MSIE") || contains(ua, "Trident") {
			stats["Internet Explorer"]++
		} else if contains(ua, "Opera") {
			stats["Opera"]++
		} else {
			stats["Other"]++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"total":       manager.GetCount(),
			"loaded":      manager.IsLoaded(),
			"browser_stats": stats,
		},
	})
}

// ReloadUserAgents 重新加载User-Agent列表
func (h *UserAgentHandler) ReloadUserAgents(c *gin.Context) {
	manager := utils.GetUserAgentManager()
	
	err := manager.LoadFromFile("static/data/user-agents.txt")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "重新加载失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "重新加载成功",
		"data": gin.H{
			"count": manager.GetCount(),
		},
	})
}

// GetUserAgentByIndex 根据索引获取User-Agent
func (h *UserAgentHandler) GetUserAgentByIndex(c *gin.Context) {
	indexStr := c.Param("index")
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的索引",
		})
		return
	}

	manager := utils.GetUserAgentManager()
	userAgent := manager.GetUserAgentByIndex(index)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"index":      index,
			"user_agent": userAgent,
		},
	})
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
			(s[:len(substr)] == substr || 
			 s[len(s)-len(substr):] == substr || 
			 containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
