package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"seo-platform/internal/services"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TemplateHandler struct {
	db            *gorm.DB
	templateCache *services.TemplateCacheService
}

func NewTemplateHandler(db *gorm.DB) *TemplateHandler {
	templateCache := services.NewTemplateCacheService(db)
	// 自动迁移加密缓存表结构
	templateCache.AutoMigrate()
	
	return &TemplateHandler{
		db:            db,
		templateCache: templateCache,
	}
}

// List 获取模板列表
func (h *TemplateHandler) List(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("page_size", "10"))
	keyword := c.Query("keyword")
	templateType := c.Query("type")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.Template{})
	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	if templateType != "" {
		query = query.Where("type = ?", templateType)
	}

	var total int64
	query.Count(&total)

	var templates []models.Template
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&templates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      templates,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// Create 创建模板
func (h *TemplateHandler) Create(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Type        string `json:"type" binding:"required"`
		Source      string `json:"source" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	template := models.Template{
		Name:        req.Name,
		Type:        req.Type,
		Source:      req.Source,
		Description: req.Description,
		Status:      1,
	}

	if err := h.db.Create(&template).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    template,
	})
}

// Get 获取模板详情
func (h *TemplateHandler) Get(c *gin.Context) {
	id := c.Param("id")

	var template models.Template
	if err := h.db.First(&template, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "模板不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    template,
	})
}

// Update 更新模板
func (h *TemplateHandler) Update(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		Name        string `json:"name"`
		Type        string `json:"type"`
		Source      string `json:"source"`
		Description string `json:"description"`
		Status      int    `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	if err := h.db.Model(&models.Template{}).Where("id = ?", id).Updates(req).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// Delete 删除模板
func (h *TemplateHandler) Delete(c *gin.Context) {
	id := c.Param("id")

	if err := h.db.Delete(&models.Template{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// Preview 预览模板（使用加密缓存）
func (h *TemplateHandler) Preview(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))

	var template models.Template
	if err := h.db.First(&template, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "模板不存在",
		})
		return
	}

	// 尝试从加密缓存中获取
	dynamicData := map[string]interface{}{
		"title":       "预览标题",
		"description": "预览描述",
		"keywords":    "预览关键词",
		"timestamp":   time.Now().Unix(),
	}
	
	// 生成加密的动态内容
	content, err := h.templateCache.GenerateEncryptedContent(template.Source, dynamicData)
	if err != nil {
		// 如果加密处理失败，返回原始内容
		c.JSON(http.StatusOK, gin.H{
			"code":    200,
			"message": "预览成功（原始模式）",
			"data": gin.H{
				"content":   template.Source,
				"encrypted": false,
			},
		})
		return
	}
	
	// 自动缓存模板（24小时TTL）
	go func() {
		h.templateCache.CacheTemplate(
			uint(template.ID),
			template.Source,
			dynamicData,
			24*time.Hour,
		)
	}()

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "预览成功（加密模式）",
		"data": gin.H{
			"content":   content,
			"encrypted": true,
		},
	})
}
