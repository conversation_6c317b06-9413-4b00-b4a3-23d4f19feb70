package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DashboardHandler struct {
	db *gorm.DB
}

func NewDashboardHandler(db *gorm.DB) *DashboardHandler {
	return &DashboardHandler{
		db: db,
	}
}

// GetStats 获取仪表盘统计
func (h *DashboardHandler) GetStats(c *gin.Context) {
	// 获取项目总数
	var projectCount int64
	h.db.Model(&models.Project{}).Count(&projectCount)

	// 获取关键词库总数
	var keywordCount int64
	h.db.Model(&models.Keyword{}).Count(&keywordCount)

	// 获取模板总数
	var templateCount int64
	h.db.Model(&models.Template{}).Count(&templateCount)

	// 获取今日蜘蛛访问数
	today := time.Now().Format("2006-01-02")
	var todaySpiderCount int64
	h.db.Model(&models.SpiderLog{}).
		Where("DATE(created_at) = ?", today).
		Count(&todaySpiderCount)

	// 获取用户总数
	var userCount int64
	h.db.Model(&models.User{}).Count(&userCount)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"project_count":       projectCount,
			"keyword_count":       keywordCount,
			"template_count":      templateCount,
			"today_spider_count":  todaySpiderCount,
			"user_count":          userCount,
		},
	})
}

// GetSpiderStats 获取蜘蛛统计
func (h *DashboardHandler) GetSpiderStats(c *gin.Context) {
	// 获取最近7天的蜘蛛访问统计
	var chartData []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		var count int64
		h.db.Model(&models.SpiderLog{}).
			Where("DATE(created_at) = ?", date).
			Count(&count)

		chartData = append(chartData, struct {
			Date  string `json:"date"`
			Count int64  `json:"count"`
		}{
			Date:  date,
			Count: count,
		})
	}

	// 获取各类蜘蛛的分布
	var spiderTypes []struct {
		SpiderType int    `json:"spider_type"`
		Name       string `json:"name"`
		Count      int64  `json:"count"`
	}

	spiderNames := map[int]string{
		1: "百度", 2: "搜狗", 3: "360", 4: "必应", 5: "谷歌",
		6: "神马", 7: "Yandex", 8: "Coccoc", 9: "Naver",
	}

	rows, err := h.db.Model(&models.SpiderLog{}).
		Select("spider_type, COUNT(*) as count").
		Group("spider_type").
		Order("count DESC").
		Rows()
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var spiderType int
			var count int64
			rows.Scan(&spiderType, &count)
			spiderTypes = append(spiderTypes, struct {
				SpiderType int    `json:"spider_type"`
				Name       string `json:"name"`
				Count      int64  `json:"count"`
			}{
				SpiderType: spiderType,
				Name:       spiderNames[spiderType],
				Count:      count,
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"chart_data":    chartData,
			"spider_types":  spiderTypes,
		},
	})
}

// GetRecentLogs 获取最近日志
func (h *DashboardHandler) GetRecentLogs(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	var logs []models.SpiderLog
	if err := h.db.Order("created_at DESC").Limit(limit).Find(&logs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    logs,
	})
}
