package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type WebsiteHandler struct {
	db *gorm.DB
}

func NewWebsiteHandler(db *gorm.DB) *WebsiteHandler {
	return &WebsiteHandler{
		db: db,
	}
}

// ListWebsites 获取网站列表
func (h *WebsiteHandler) ListWebsites(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10"))
	keyword := c.Query("keyword")
	status := c.Query("status")
	projectID := c.Query("project_id")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.WebsiteList{})
	if keyword != "" {
		query = query.Where("domain LIKE ? OR title LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if projectID != "" {
		query = query.Where("project_id = ?", projectID)
	}

	var total int64
	query.Count(&total)

	var websites []models.WebsiteList
	if err := query.Preload("Project").Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&websites).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      websites,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// CreateWebsite 创建网站
func (h *WebsiteHandler) CreateWebsite(c *gin.Context) {
	var req struct {
		ProjectID   uint   `json:"project_id" binding:"required"`
		Domain      string `json:"domain" binding:"required"`
		Title       string `json:"title" binding:"required"`
		Description string `json:"description"`
		Keywords    string `json:"keywords"`
		Status      int    `json:"status"`
		SortOrder   int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 检查域名是否已存在
	var count int64
	h.db.Model(&models.WebsiteList{}).Where("domain = ?", req.Domain).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "域名已存在",
		})
		return
	}

	website := models.WebsiteList{
		CategoryID:    int(req.ProjectID),
		DisplayName:   req.Title,
		BindDomains:   req.Domain,
		ProfileID:     1, // 默认配置ID
		LastediteTime: time.Now().Unix(),
		SEOOptions:    req.Description + "|" + req.Keywords, // 将描述和关键词存储在SEO选项中
		ClsID:         "",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := h.db.Create(&website).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    website,
	})
}

// GetWebsite 获取网站详情
func (h *WebsiteHandler) GetWebsite(c *gin.Context) {
	id := c.Param("id")

	var website models.WebsiteList
	if err := h.db.Preload("Project").First(&website, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "网站不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    website,
	})
}

// UpdateWebsite 更新网站
func (h *WebsiteHandler) UpdateWebsite(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		ProjectID   uint   `json:"project_id"`
		Domain      string `json:"domain"`
		Title       string `json:"title"`
		Description string `json:"description"`
		Keywords    string `json:"keywords"`
		Status      int    `json:"status"`
		SortOrder   int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	// 如果更新域名，检查是否已存在
	if req.Domain != "" {
		var count int64
		h.db.Model(&models.WebsiteList{}).Where("domain = ? AND id != ?", req.Domain, id).Count(&count)
		if count > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "域名已存在",
			})
			return
		}
	}

	req_map := map[string]interface{}{
		"updated_at": time.Now(),
	}
	if req.ProjectID != 0 {
		req_map["project_id"] = req.ProjectID
	}
	if req.Domain != "" {
		req_map["domain"] = req.Domain
	}
	if req.Title != "" {
		req_map["title"] = req.Title
	}
	if req.Description != "" {
		req_map["description"] = req.Description
	}
	if req.Keywords != "" {
		req_map["keywords"] = req.Keywords
	}
	req_map["status"] = req.Status
	req_map["sort_order"] = req.SortOrder

	if err := h.db.Model(&models.WebsiteList{}).Where("id = ?", id).Updates(req_map).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteWebsite 删除网站
func (h *WebsiteHandler) DeleteWebsite(c *gin.Context) {
	id := c.Param("id")

	// 检查是否有关联的网站配置
	var count int64
	h.db.Model(&models.WebsiteProfile{}).Where("website_id = ?", id).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "该网站下还有配置信息，无法删除",
		})
		return
	}

	if err := h.db.Delete(&models.WebsiteList{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// BatchDeleteWebsites 批量删除网站
func (h *WebsiteHandler) BatchDeleteWebsites(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 检查是否有关联的网站配置
	var count int64
	h.db.Model(&models.WebsiteProfile{}).Where("website_id IN ?", req.IDs).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "部分网站下还有配置信息，无法删除",
		})
		return
	}

	if err := h.db.Where("id IN ?", req.IDs).Delete(&models.WebsiteList{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量删除成功",
	})
}

// BatchUpdateWebsiteStatus 批量更新网站状态
func (h *WebsiteHandler) BatchUpdateWebsiteStatus(c *gin.Context) {
	var req struct {
		IDs    []uint `json:"ids" binding:"required"`
		Status int    `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := h.db.Model(&models.WebsiteList{}).Where("id IN ?", req.IDs).Update("status", req.Status).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量更新成功",
	})
}

// ListWebsiteProfiles 获取网站配置列表
func (h *WebsiteHandler) ListWebsiteProfiles(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	websiteID := c.Query("website_id")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.WebsiteProfile{})
	if websiteID != "" {
		query = query.Where("website_id = ?", websiteID)
	}

	var total int64
	query.Count(&total)

	var profiles []models.WebsiteProfile
	if err := query.Preload("Website").Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&profiles).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      profiles,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// CreateWebsiteProfile 创建网站配置
func (h *WebsiteHandler) CreateWebsiteProfile(c *gin.Context) {
	var req struct {
		WebsiteID   uint   `json:"website_id" binding:"required"`
		ConfigKey   string `json:"config_key" binding:"required"`
		ConfigValue string `json:"config_value" binding:"required"`
		Description string `json:"description"`
		Status      int    `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 检查配置键是否已存在
	var count int64
	h.db.Model(&models.WebsiteProfile{}).Where("website_id = ? AND config_key = ?", req.WebsiteID, req.ConfigKey).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "配置键已存在",
		})
		return
	}

	profile := models.WebsiteProfile{
		DisplayName:      req.ConfigKey,
		Category:         int(req.WebsiteID),
		CategoryKeyword:  req.ConfigValue,
		CategoryContent:  req.Description,
		CategoryTemplate: "",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if err := h.db.Create(&profile).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    profile,
	})
}

// GetWebsiteProfile 获取网站配置详情
func (h *WebsiteHandler) GetWebsiteProfile(c *gin.Context) {
	id := c.Param("id")

	var profile models.WebsiteProfile
	if err := h.db.Preload("Website").First(&profile, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "配置不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    profile,
	})
}

// UpdateWebsiteProfile 更新网站配置
func (h *WebsiteHandler) UpdateWebsiteProfile(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		ConfigKey   string `json:"config_key"`
		ConfigValue string `json:"config_value"`
		Description string `json:"description"`
		Status      int    `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	req_map := map[string]interface{}{
		"updated_at": time.Now(),
	}
	if req.ConfigKey != "" {
		req_map["config_key"] = req.ConfigKey
	}
	if req.ConfigValue != "" {
		req_map["config_value"] = req.ConfigValue
	}
	if req.Description != "" {
		req_map["description"] = req.Description
	}
	req_map["status"] = req.Status

	if err := h.db.Model(&models.WebsiteProfile{}).Where("id = ?", id).Updates(req_map).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteWebsiteProfile 删除网站配置
func (h *WebsiteHandler) DeleteWebsiteProfile(c *gin.Context) {
	id := c.Param("id")

	if err := h.db.Delete(&models.WebsiteProfile{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetWebsiteStats 获取网站统计信息
func (h *WebsiteHandler) GetWebsiteStats(c *gin.Context) {
	var stats struct {
		TotalWebsites int64 `json:"total_websites"`
		ActiveWebsites int64 `json:"active_websites"`
		InactiveWebsites int64 `json:"inactive_websites"`
		TotalProfiles int64 `json:"total_profiles"`
	}

	// 总网站数
	h.db.Model(&models.WebsiteList{}).Count(&stats.TotalWebsites)
	
	// 活跃网站数
	h.db.Model(&models.WebsiteList{}).Where("status = 1").Count(&stats.ActiveWebsites)
	
	// 非活跃网站数
	h.db.Model(&models.WebsiteList{}).Where("status = 0").Count(&stats.InactiveWebsites)
	
	// 总配置数
	h.db.Model(&models.WebsiteProfile{}).Count(&stats.TotalProfiles)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    stats,
	})
}