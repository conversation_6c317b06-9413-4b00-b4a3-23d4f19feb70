package handlers

import (
	"net/http"
	"seo-platform/internal/models"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PermissionHandler struct {
	db *gorm.DB
}

func NewPermissionHandler(db *gorm.DB) *PermissionHandler {
	return &PermissionHandler{
		db: db,
	}
}

// ListRoles 获取角色列表
func (h *PermissionHandler) ListRoles(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page_size", "10"))
	keyword := c.Query("keyword")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.AuthGroup{})
	if keyword != "" {
		query = query.Where("title LIKE ?", "%"+keyword+"%")
	}

	var total int64
	query.Count(&total)

	var roles []models.AuthGroup
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&roles).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      roles,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// CreateRole 创建角色
func (h *PermissionHandler) CreateRole(c *gin.Context) {
	var req struct {
		Title  string `json:"title" binding:"required"`
		Status int    `json:"status"`
		Rules  string `json:"rules"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	role := models.AuthGroup{
		Title:  req.Title,
		Status: req.Status,
		Rules:  req.Rules,
	}

	if err := h.db.Create(&role).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    role,
	})
}

// GetRole 获取角色详情
func (h *PermissionHandler) GetRole(c *gin.Context) {
	id := c.Param("id")

	var role models.AuthGroup
	if err := h.db.First(&role, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "角色不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    role,
	})
}

// UpdateRole 更新角色
func (h *PermissionHandler) UpdateRole(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		Title  string `json:"title"`
		Status int    `json:"status"`
		Rules  string `json:"rules"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	if err := h.db.Model(&models.AuthGroup{}).Where("id = ?", id).Updates(req).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteRole 删除角色
func (h *PermissionHandler) DeleteRole(c *gin.Context) {
	id := c.Param("id")

	// 检查是否有用户关联此角色
	var count int64
	h.db.Model(&models.AuthGroupAccess{}).Where("group_id = ?", id).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "该角色下还有用户，无法删除",
		})
		return
	}

	if err := h.db.Delete(&models.AuthGroup{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ListPermissions 获取权限列表
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	keyword := c.Query("keyword")

	offset := (page - 1) * pageSize

	query := h.db.Model(&models.AuthRule{})
	if keyword != "" {
		query = query.Where("title LIKE ? OR name LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	var total int64
	query.Count(&total)

	var permissions []models.AuthRule
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&permissions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      permissions,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// CreatePermission 创建权限
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req struct {
		PID       uint   `json:"pid"`
		Name      string `json:"name" binding:"required"`
		Title     string `json:"title" binding:"required"`
		Status    int    `json:"status"`
		Type      int    `json:"type"`
		Condition string `json:"condition"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	permission := models.AuthRule{
		PID:       req.PID,
		Name:      req.Name,
		Title:     req.Title,
		Status:    req.Status,
		Type:      req.Type,
		Condition: req.Condition,
	}

	if err := h.db.Create(&permission).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    permission,
	})
}

// GetPermission 获取权限详情
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	id := c.Param("id")

	var permission models.AuthRule
	if err := h.db.First(&permission, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "权限不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    permission,
	})
}

// UpdatePermission 更新权限
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		PID       uint   `json:"pid"`
		Name      string `json:"name"`
		Title     string `json:"title"`
		Status    int    `json:"status"`
		Type      int    `json:"type"`
		Condition string `json:"condition"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
		})
		return
	}

	if err := h.db.Model(&models.AuthRule{}).Where("id = ?", id).Updates(req).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeletePermission 删除权限
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	id := c.Param("id")

	if err := h.db.Delete(&models.AuthRule{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// AssignRoleToUser 为用户分配角色
func (h *PermissionHandler) AssignRoleToUser(c *gin.Context) {
	var req struct {
		UserID  uint `json:"user_id" binding:"required"`
		GroupID uint `json:"group_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 检查是否已存在
	var count int64
	h.db.Model(&models.AuthGroupAccess{}).Where("uid = ? AND group_id = ?", req.UserID, req.GroupID).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户已拥有该角色",
		})
		return
	}

	access := models.AuthGroupAccess{
		UID:     req.UserID,
		GroupID: req.GroupID,
	}

	if err := h.db.Create(&access).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "分配失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "分配成功",
	})
}

// RemoveRoleFromUser 移除用户角色
func (h *PermissionHandler) RemoveRoleFromUser(c *gin.Context) {
	var req struct {
		UserID  uint `json:"user_id" binding:"required"`
		GroupID uint `json:"group_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := h.db.Where("uid = ? AND group_id = ?", req.UserID, req.GroupID).Delete(&models.AuthGroupAccess{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "移除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "移除成功",
	})
}

// GetUserRoles 获取用户角色
func (h *PermissionHandler) GetUserRoles(c *gin.Context) {
	userID := c.Param("user_id")

	var roles []models.AuthGroup
	if err := h.db.Table("auth_groups").
		Joins("JOIN auth_group_access ON auth_groups.id = auth_group_access.group_id").
		Where("auth_group_access.uid = ?", userID).
		Find(&roles).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    roles,
	})
}