package api

import (
	"seo-platform/internal/api/handlers"
	"seo-platform/internal/config"
	"seo-platform/internal/middleware"
	"seo-platform/static"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRouter(db *gorm.DB, cfg *config.Config) *gin.Engine {
	router := gin.Default()

	// 中间件
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	
	// 蜘蛛访问跟踪中间件（自动加密记录）
	spiderTracker := middleware.NewSpiderTracker(db)
	router.Use(spiderTracker.TrackSpiderVisit())

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(db, cfg)
	projectHandler := handlers.NewProjectHandler(db)
	keywordHandler := handlers.NewKeywordHandler(db)
	templateHandler := handlers.NewTemplateHandler(db)
	spiderHandler := handlers.NewSpiderHandler(db)
	dashboardHandler := handlers.NewDashboardHandler(db)
	userAgentHandler := handlers.NewUserAgentHandler(db)
	permissionHandler := handlers.NewPermissionHandler(db)
	websiteHandler := handlers.NewWebsiteHandler(db)
	monitorHandler := handlers.NewMonitorHandler(db)
	toolsHandler := handlers.NewToolsHandler(db)


	// API路由组
	api := router.Group("/api")
	{
		// 认证相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/logout", authHandler.Logout)
			auth.GET("/profile", middleware.AuthRequired(), authHandler.GetProfile)
			auth.PUT("/profile", middleware.AuthRequired(), authHandler.UpdateProfile)

			// 安全相关路由
			auth.GET("/security/status", middleware.AuthRequired(), authHandler.GetSecurityStatus)
			auth.POST("/security/unlock/:id", middleware.AuthRequired(), authHandler.UnlockAccount)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthRequired())
		{
			// 仪表盘
			dashboard := protected.Group("/dashboard")
			{
				dashboard.GET("/stats", dashboardHandler.GetStats)
				dashboard.GET("/spider-stats", dashboardHandler.GetSpiderStats)
				dashboard.GET("/recent-logs", dashboardHandler.GetRecentLogs)
			}

			// 项目管理
			projects := protected.Group("/projects")
			{
				projects.GET("", projectHandler.List)
				projects.POST("", projectHandler.Create)
				projects.GET("/:id", projectHandler.Get)
				projects.PUT("/:id", projectHandler.Update)
				projects.DELETE("/:id", projectHandler.Delete)
				projects.POST("/:id/sync", projectHandler.Sync)
				projects.GET("/:id/stats", projectHandler.GetStats)
				projects.GET("/:id/extended", projectHandler.GetExtendedInfo)

				// 批量操作
				projects.POST("/batch/delete", projectHandler.BatchDelete)
				projects.POST("/batch/status", projectHandler.BatchUpdateStatus)
			}

			// 关键词管理
			keywords := protected.Group("/keywords")
			{
				keywords.GET("", keywordHandler.List)
				keywords.POST("", keywordHandler.Create)
				keywords.GET("/:id", keywordHandler.Get)
				keywords.PUT("/:id", keywordHandler.Update)
				keywords.DELETE("/:id", keywordHandler.Delete)
				keywords.POST("/import", keywordHandler.Import)
				keywords.GET("/export", keywordHandler.Export)
			}

			// 模板管理
			templates := protected.Group("/templates")
			{
				templates.GET("", templateHandler.List)
				templates.POST("", templateHandler.Create)
				templates.GET("/:id", templateHandler.Get)
				templates.PUT("/:id", templateHandler.Update)
				templates.DELETE("/:id", templateHandler.Delete)
				templates.GET("/:id/preview", templateHandler.Preview)
			}

			// 蜘蛛统计
			spider := protected.Group("/spider")
			{
				spider.GET("/logs", spiderHandler.GetLogs)
				spider.GET("/stats", spiderHandler.GetStats)
				spider.GET("/stats/today", spiderHandler.GetTodayStats)
				spider.GET("/stats/comparison", spiderHandler.GetComparisonStats)
				spider.GET("/stats/hourly", spiderHandler.GetHourlyStats)
				spider.GET("/stats/chart", spiderHandler.GetChartData)
				spider.POST("/logs/clear", spiderHandler.ClearLogs)
			}



			// 用户管理
			users := protected.Group("/users")
			{
				users.GET("", authHandler.ListUsers)
				users.POST("", authHandler.CreateUser)
				users.GET("/:id", authHandler.GetUser)
				users.PUT("/:id", authHandler.UpdateUser)
				users.DELETE("/:id", authHandler.DeleteUser)
			}

			// User-Agent管理
			userAgents := protected.Group("/user-agents")
			{
				userAgents.GET("", userAgentHandler.GetUserAgentList)
				userAgents.GET("/random", userAgentHandler.GetRandomUserAgent)
				userAgents.GET("/stats", userAgentHandler.GetUserAgentStats)
				userAgents.POST("/reload", userAgentHandler.ReloadUserAgents)
				userAgents.GET("/:index", userAgentHandler.GetUserAgentByIndex)
			}

			// 权限管理
			permissions := protected.Group("/permissions")
			{
				// 角色管理
				roles := permissions.Group("/roles")
				{
					roles.GET("", permissionHandler.ListRoles)
					roles.POST("", permissionHandler.CreateRole)
					roles.GET("/:id", permissionHandler.GetRole)
					roles.PUT("/:id", permissionHandler.UpdateRole)
					roles.DELETE("/:id", permissionHandler.DeleteRole)
				}
				// 权限管理
				perms := permissions.Group("/rules")
				{
					perms.GET("", permissionHandler.ListPermissions)
					perms.POST("", permissionHandler.CreatePermission)
					perms.GET("/:id", permissionHandler.GetPermission)
					perms.PUT("/:id", permissionHandler.UpdatePermission)
					perms.DELETE("/:id", permissionHandler.DeletePermission)
				}
				// 用户角色分配
				permissions.POST("/assign", permissionHandler.AssignRoleToUser)
				permissions.POST("/remove", permissionHandler.RemoveRoleFromUser)
				permissions.GET("/users/:user_id/roles", permissionHandler.GetUserRoles)
			}

			// 站群管理
			websites := protected.Group("/websites")
			{
				websites.GET("", websiteHandler.ListWebsites)
				websites.POST("", websiteHandler.CreateWebsite)
				websites.GET("/:id", websiteHandler.GetWebsite)
				websites.PUT("/:id", websiteHandler.UpdateWebsite)
				websites.DELETE("/:id", websiteHandler.DeleteWebsite)
				websites.POST("/batch/delete", websiteHandler.BatchDeleteWebsites)
				websites.POST("/batch/status", websiteHandler.BatchUpdateWebsiteStatus)
				websites.GET("/stats", websiteHandler.GetWebsiteStats)
				// 网站配置管理
				profiles := websites.Group("/profiles")
				{
					profiles.GET("", websiteHandler.ListWebsiteProfiles)
					profiles.POST("", websiteHandler.CreateWebsiteProfile)
					profiles.GET("/:id", websiteHandler.GetWebsiteProfile)
					profiles.PUT("/:id", websiteHandler.UpdateWebsiteProfile)
					profiles.DELETE("/:id", websiteHandler.DeleteWebsiteProfile)
				}
			}

			// 监控管理
			monitor := protected.Group("/monitor")
			{
				monitor.GET("", monitorHandler.ListMonitors)
				monitor.POST("", monitorHandler.CreateMonitor)
				monitor.GET("/:id", monitorHandler.GetMonitor)
				monitor.PUT("/:id", monitorHandler.UpdateMonitor)
				monitor.DELETE("/:id", monitorHandler.DeleteMonitor)
				monitor.POST("/batch/delete", monitorHandler.BatchDeleteMonitors)
				monitor.GET("/stats", monitorHandler.GetMonitorStats)
				monitor.GET("/:id/health", monitorHandler.CheckMonitorHealth)
				monitor.GET("/:id/history", monitorHandler.GetMonitorHistory)
			}

			// 工具管理
			tools := protected.Group("/tools")
			{
				tools.POST("/spider-simulator", toolsHandler.SpiderSimulator)
				tools.POST("/404-hijack-detector", toolsHandler.HijackDetector)
				tools.POST("/htaccess-generator", toolsHandler.HtaccessGenerator)
				tools.POST("/iis6-generator", toolsHandler.IIS6Generator)
				tools.POST("/file-index-encrypt", toolsHandler.FileIndexEncrypt)
				tools.POST("/js-encrypt", toolsHandler.JSEncrypt)
				tools.POST("/js-decrypt", toolsHandler.JSDecrypt)
				tools.POST("/iis-domain-export", toolsHandler.IISDomainExport)
				tools.GET("/iis-sites", toolsHandler.GetIISSites)
			}

		}
	}

	// SEO控制端API（用于内容输出）
	seo := router.Group("/seo")
	{
		seoHandler := handlers.NewSEOHandler(db)
		seo.GET("/content", seoHandler.GetContent)
		seo.POST("/spider-log", seoHandler.LogSpider)
		seo.GET("/sitemap", seoHandler.GetSitemap)
	}

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// 静态文件服务 - 使用嵌入的文件（放在最后，作为fallback）
	router.NoRoute(static.StaticFileHandler())

	return router
}
