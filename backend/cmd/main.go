package main

import (
	"log"
	"seo-platform/internal/api"
	"seo-platform/internal/config"
	"seo-platform/internal/database"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize MySQL database with auto-setup
	log.Printf("🚀 Starting SEO Platform...")
	log.Printf("🔗 Connecting to MySQL at %s:%s", cfg.Database.MySQL.Host, cfg.Database.MySQL.Port)

	db, err := database.InitMySQL(cfg.Database.MySQL)
	if err != nil {
		log.Fatalf("❌ Failed to initialize MySQL database: %v", err)
	}

	log.Printf("✅ Database initialization completed successfully")

	// 设置 Gin 模式
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := api.SetupRouter(db, cfg)

	// Start the server
	log.Printf("🌐 Server starting on port %s", cfg.Server.Port)
	log.Printf("🔗 Access the application at: http://localhost:%s", cfg.Server.Port)
	log.Printf("📖 API documentation available at: http://localhost:%s/api/docs", cfg.Server.Port)

	if err := router.Run(":" + cfg.Server.Port); err != nil {
		log.Fatalf("❌ Failed to start server: %v", err)
	}
}
