<template>
  <div class="spider-simulator page-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb page-header">
      <el-icon class="breadcrumb-icon"><Setting /></el-icon>
      <span class="breadcrumb-text">辅助工具</span>
      <span class="breadcrumb-separator">></span>
      <span class="breadcrumb-current">在线蜘蛛模拟</span>
    </div>

    <div class="simulator-container page-content">
      <!-- 左侧蜘蛛类型列表 -->
      <div class="spider-list enhanced-card">
        <div class="spider-list-header">
          <el-icon class="spider-list-icon"><DataAnalysis /></el-icon>
          <span class="spider-list-title">蜘蛛类型</span>
        </div>
        <div
          v-for="spider in spiderTypes"
          :key="spider.value"
          class="spider-item"
          :class="{ active: selectedSpider === spider.value }"
          @click="selectSpider(spider.value)"
        >
          <el-icon class="spider-item-icon"><Monitor /></el-icon>
          <span class="spider-item-text">{{ spider.label }}</span>
        </div>
      </div>

      <!-- 右侧操作区域 -->
      <div class="operation-area">
        <div class="input-section enhanced-card">
          <div class="input-header">
            <el-icon class="input-icon"><Link /></el-icon>
            <span class="input-title">URL 配置</span>
          </div>
          <el-input
            v-model="targetUrl"
            placeholder="请输入待抓取的URL..."
            class="url-input input-enhanced"
            size="large"
          >
            <template #prefix>
              <el-icon><Link /></el-icon>
            </template>
          </el-input>

          <div class="controls">
            <el-select
              v-model="spiderMode"
              placeholder="蜘蛛模式"
              class="mode-select input-enhanced"
              size="large"
            >
              <el-option label="标准模式" value="standard" />
              <el-option label="快速模式" value="fast" />
              <el-option label="详细模式" value="detailed" />
            </el-select>

            <el-button
              @click="handleSimulate"
              :loading="loading"
              class="simulate-btn btn-gradient"
              size="large"
            >
              <el-icon><Search /></el-icon>
              模拟抓取
            </el-button>
          </div>
        </div>

        <!-- 结果展示区域 -->
        <div v-if="simulationResult" class="result-area enhanced-card fade-in-up">
          <div class="result-header">
            <div class="result-title">
              <el-icon class="result-icon"><DocumentChecked /></el-icon>
              <h3>抓取结果</h3>
            </div>
            <div class="result-status">
              <el-tag :type="getStatusType(simulationResult.statusCode)" size="large" class="status-tag">
                {{ simulationResult.statusCode }} {{ simulationResult.statusText }}
              </el-tag>
            </div>
          </div>

          <div class="result-content">
            <el-tabs v-model="activeResultTab" class="result-tabs">
              <el-tab-pane label="响应信息" name="response">
                <div class="response-info">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <div class="info-item">
                        <span class="info-label">响应时间:</span>
                        <span class="info-value">{{ simulationResult.responseTime }}ms</span>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="info-item">
                        <span class="info-label">内容大小:</span>
                        <span class="info-value">{{ formatSize(simulationResult.contentLength) }}</span>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="info-item">
                        <span class="info-label">内容类型:</span>
                        <span class="info-value">{{ simulationResult.contentType }}</span>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="info-item">
                        <span class="info-label">服务器:</span>
                        <span class="info-value">{{ simulationResult.server }}</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>

              <el-tab-pane label="HTTP头" name="headers">
                <div class="headers-content">
                  <pre>{{ simulationResult.headers }}</pre>
                </div>
              </el-tab-pane>

              <el-tab-pane label="页面内容" name="content">
                <div class="page-content-area">
                  <el-input
                    v-model="simulationResult.content"
                    type="textarea"
                    :rows="20"
                    readonly
                    class="content-textarea"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, DataAnalysis, Monitor, Link, Search, DocumentChecked } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedSpider = ref('360pc')
const targetUrl = ref('')
const spiderMode = ref('standard')
const activeResultTab = ref('response')
const simulationResult = ref(null)

// 蜘蛛类型列表
const spiderTypes = [
  { label: '360PC蜘蛛模拟', value: '360pc' },
  { label: '360移动蜘蛛模拟', value: '360mobile' },
  { label: '百度PC蜘蛛模拟', value: 'baidupc' },
  { label: '百度移动蜘蛛模拟', value: 'baidumobile' },
  { label: '搜狗PC蜘蛛模拟', value: 'sogoupc' },
  { label: '搜狗移动蜘蛛模拟', value: 'sogoumobile' },
  { label: '神马移动蜘蛛', value: 'shenma' },
  { label: '正常访问', value: 'normal' }
]

// 方法
const selectSpider = (spiderValue) => {
  selectedSpider.value = spiderValue
}

const handleSimulate = async () => {
  if (!targetUrl.value.trim()) {
    ElMessage.warning('请输入要抓取的URL')
    return
  }

  loading.value = true
  try {
    const response = await fetch('/api/tools/spider-simulator', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: targetUrl.value,
        spider: selectedSpider.value
      })
    })

    if (response.ok) {
      simulationResult.value = await response.json()
      ElMessage.success('蜘蛛模拟完成')
    } else {
       throw new Error('模拟失败')
     }
  } catch (error) {
    ElMessage.error('模拟抓取失败，请检查URL是否正确')
  } finally {
    loading.value = false
  }
}

const getSpiderInfo = (spiderType) => {
  const spiderMap = {
    '360pc': {
      name: '360PC蜘蛛',
      userAgent: 'Mozilla/5.0 (compatible; 360Spider; +http://webscan.360.cn)'
    },
    '360mobile': {
      name: '360移动蜘蛛',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A465 Safari/9537.53 (compatible; 360Spider; +http://webscan.360.cn)'
    },
    'baidupc': {
      name: '百度PC蜘蛛',
      userAgent: 'Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)'
    },
    'baidumobile': {
      name: '百度移动蜘蛛',
      userAgent: 'Mozilla/5.0 (Linux; u; Android 4.2.2; zh-cn; ) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile Safari/10600.6.3 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)'
    },
    'sogoupc': {
      name: '搜狗PC蜘蛛',
      userAgent: 'Sogou web spider/4.0(+http://www.sogou.com/docs/help/webmasters.htm#07)'
    },
    'sogoumobile': {
      name: '搜狗移动蜘蛛',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9A334 (compatible; Sogou web spider/4.0;+http://www.sogou.com/docs/help/webmasters.htm#07)'
    },
    'shenma': {
      name: '神马移动蜘蛛',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 (compatible; YisouSpider; +http://www.yisou.com/help/spider.html)'
    },
    'normal': {
      name: '正常访问',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
  }

  return spiderMap[spiderType] || spiderMap['360pc']
}

const getStatusType = (statusCode) => {
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'warning'
  if (statusCode >= 400) return 'danger'
  return 'info'
}

const formatSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.spider-simulator {
  animation: fadeInUp 0.8s ease-out;
}

.breadcrumb {
  display: flex;
  align-items: center;
}

.breadcrumb-icon {
  font-size: 20px;
  color: var(--primary-color);
  margin-right: 12px;
  transition: all 0.3s ease;
}

.breadcrumb-text {
  color: var(--text-regular);
  font-size: 15px;
  font-weight: 500;
}

.breadcrumb-separator {
  margin: 0 12px;
  color: var(--text-placeholder);
  font-weight: 600;
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 15px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simulator-container {
  display: flex;
  gap: 32px;
  min-height: calc(100vh - 200px);
}

.spider-list {
  width: 280px;
  padding: 24px;
  height: fit-content;
  transition: all 0.3s ease;
}

.spider-list:hover {
  transform: translateY(-2px);
}

.spider-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--border-light);
}

.spider-list-icon {
  font-size: 20px;
  color: var(--primary-color);
  margin-right: 8px;
}

.spider-list-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.spider-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-regular);
  font-size: 14px;
  font-weight: 500;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.spider-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.spider-item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.05));
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateX(4px) scale(1.02);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.spider-item:hover::before {
  left: 100%;
}

.spider-item.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: 600;
  border-color: var(--primary-light);
  transform: translateX(6px) scale(1.05);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.spider-item-icon {
  font-size: 16px;
  margin-right: 10px;
  transition: all 0.3s ease;
}

.spider-item:hover .spider-item-icon,
.spider-item.active .spider-item-icon {
  transform: scale(1.1) rotate(5deg);
}

.spider-item-text {
  flex: 1;
}

.spider-item:last-child {
  border-bottom: none;
}

.operation-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.input-section {
  padding: 32px;
  transition: all 0.3s ease;
}

.input-section:hover {
  transform: translateY(-2px);
}

.input-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--border-light);
}

.input-icon {
  font-size: 20px;
  color: var(--primary-color);
  margin-right: 8px;
}

.input-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.url-input {
  width: 100%;
  margin-bottom: 24px;
}

.url-input :deep(.el-input__wrapper) {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.url-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
}

.controls {
  display: flex;
  gap: 20px;
  align-items: center;
}

.mode-select {
  width: 180px;
}

.mode-select :deep(.el-input__wrapper) {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.mode-select :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
}

.simulate-btn {
  background: #409eff;
  border-color: #409eff;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.simulate-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.result-area {
  margin-top: 32px;
  padding: 32px;
  transition: all 0.3s ease;
}

.result-area:hover {
  transform: translateY(-2px);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--border-light);
}

.result-title {
  display: flex;
  align-items: center;
}

.result-icon {
  font-size: 20px;
  color: var(--success-color);
  margin-right: 8px;
  transition: all 0.3s ease;
}

.result-area:hover .result-icon {
  transform: scale(1.1) rotate(5deg);
}

.result-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(135deg, var(--success-color), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.response-info {
  padding: 24px;
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.05), rgba(102, 126, 234, 0.03));
  border-radius: var(--radius-large);
  border: 1px solid rgba(103, 194, 58, 0.1);
  backdrop-filter: blur(10px);
}

.info-item {
  display: flex;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: var(--radius-base);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-label {
  font-weight: 600;
  color: var(--text-regular);
  width: 100px;
  font-size: 14px;
}

.info-value {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.headers-content {
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(233, 236, 239, 0.8));
  padding: 20px;
  border-radius: var(--radius-base);
  border: 1px solid var(--border-light);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.headers-content:hover {
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.95), rgba(233, 236, 239, 0.9));
  border-color: var(--primary-color);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.headers-content pre {
  margin: 0;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: var(--text-primary);
  white-space: pre-wrap;
  background: rgba(255, 255, 255, 0.5);
  padding: 16px;
  border-radius: var(--radius-small);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.content-textarea {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-size: 13px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-base);
  transition: all 0.3s ease;
}

.content-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-input__wrapper) {
  border-radius: var(--radius-base);
  transition: all 0.3s ease;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: var(--radius-base);
}

:deep(.el-button) {
  border-radius: var(--radius-base);
}

:deep(.el-tabs__item) {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-regular);
  transition: all 0.3s ease;
}

:deep(.el-tabs__item.is-active) {
  color: var(--primary-color);
  font-weight: 600;
}

:deep(.el-tabs__active-bar) {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  height: 3px;
}

:deep(.el-tabs__nav-wrap::after) {
  background: var(--border-light);
}

:deep(.el-textarea__inner) {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-base);
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .simulator-container {
    flex-direction: column;
    gap: 24px;
  }
  
  .spider-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .spider-item {
    flex: 1;
    min-width: 150px;
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .mode-select {
    width: 100%;
  }
  
  .simulate-btn {
    width: 100%;
    justify-content: center;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .response-info .el-row {
    flex-direction: column;
  }
  
  .response-info .el-col {
    width: 100%;
    margin-bottom: 8px;
  }
}
</style>
