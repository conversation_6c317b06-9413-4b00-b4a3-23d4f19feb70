# SEO Platform 部署指南

## 环境要求

### 系统要求
- Linux/macOS/Windows
- 内存: 最低 2GB，推荐 4GB+
- 磁盘: 最低 10GB 可用空间

### 软件依赖
- **Go 1.21+**: 后端运行环境
- **Node.js 18+**: 前端构建环境
- **MySQL 8.0+**: 数据库

## 快速部署

### 1. 下载项目
```bash
git clone <repository-url>
cd seo-platform-go-vue
```

### 2. 安装依赖
```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 运行构建脚本
./scripts/build.sh
```

### 3. 配置数据库

#### MySQL 配置
```sql
-- 创建数据库
CREATE DATABASE seo_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'seouser'@'localhost' IDENTIFIED BY 'seopassword';
GRANT ALL PRIVILEGES ON seo_platform.* TO 'seouser'@'localhost';
FLUSH PRIVILEGES;

-- 导入初始数据
mysql -u seouser -p seo_platform < backend/migrations/001_init_schema.sql
```



### 4. 配置应用
```bash
# 复制配置文件
cp backend/config/config.example.yaml backend/config/config.yaml

# 编辑配置文件
vim backend/config/config.yaml
```

配置示例：
```yaml
server:
  port: "8080"
  mode: "release"

database:
  mysql:
    host: "localhost"
    port: "3306"
    username: "seouser"
    password: "your_secure_password"
    database: "seo_platform"
    charset: "utf8mb4"

jwt:
  secret: "your-production-secret-key"
  expire_time: 86400

seo:
  default_user_agent: "Mozilla/5.0 (compatible; SEO-Platform/1.0)"
  allowed_domains:
    - "example.com"
    - "*.example.com"
  cache_expire: 3600
```

### 5. 启动服务
```bash
# 生产环境启动
./bin/seo-platform

# 或后台运行
nohup ./bin/seo-platform > logs/app.log 2>&1 &
```

### 6. 访问应用
- 管理后台: http://localhost:8080
- API接口: http://localhost:8080/api
- 健康检查: http://localhost:8080/health

默认管理员账号：
- 用户名: admin
- 密码: 请手动创建管理员用户

## 开发环境

### 启动开发环境
```bash
# 启动开发服务
./scripts/dev.sh

# 前端: http://localhost:3000
# 后端: http://localhost:8080
```

### 停止开发环境
```bash
./scripts/stop.sh
```

## 生产环境优化

### 1. 使用进程管理器
推荐使用 systemd 或 supervisor 管理进程：

#### systemd 配置
```ini
# /etc/systemd/system/seo-platform.service
[Unit]
Description=SEO Platform
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/seo-platform-go-vue
ExecStart=/path/to/seo-platform-go-vue/bin/seo-platform
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable seo-platform
sudo systemctl start seo-platform
sudo systemctl status seo-platform
```

### 2. 反向代理配置

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 3. 数据库优化
- 配置 MySQL 连接池
- 设置 MySQL 索引优化
- 配置 MySQL 缓存

### 4. 监控和日志
- 配置日志轮转
- 设置监控告警
- 性能指标收集

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
lsof -i :8080
# 或
netstat -tulpn | grep 8080
```

2. **数据库连接失败**
- 检查数据库服务状态
- 验证连接配置
- 检查防火墙设置

3. **前端页面无法访问**
- 检查静态文件是否正确构建
- 验证路由配置
- 查看浏览器控制台错误

4. **API 请求失败**
- 检查后端服务状态
- 验证 CORS 配置
- 查看网络请求日志

### 日志查看
```bash
# 应用日志
tail -f logs/app.log

# 系统日志
journalctl -u seo-platform -f

# 数据库日志
tail -f /var/log/mysql/error.log
```

## 安全建议

1. **更改默认密码**
2. **配置 HTTPS**
3. **设置防火墙规则**
4. **定期更新依赖**
5. **备份数据库**
6. **监控异常访问**

## 备份和恢复

### 数据备份
```bash
# MySQL 备份
mysqldump -u seouser -p seo_platform > backup_$(date +%Y%m%d).sql

# 压缩备份文件
gzip backup_$(date +%Y%m%d).sql
```

### 数据恢复
```bash
# 解压备份文件
gunzip backup_20240101.sql.gz

# MySQL 恢复
mysql -u seouser -p seo_platform < backup_20240101.sql
```
