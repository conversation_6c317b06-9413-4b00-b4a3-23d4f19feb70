# Development Console Filtering Guide

## Browser Extension Error Filtering

When developing the SEO Platform, you may see console errors from browser extensions. Here's how to filter them out:

### Chrome DevTools Console Filtering

1. **Open Chrome DevTools** (F12)
2. **Go to Console tab**
3. **Click the Filter icon** (funnel icon)
4. **Add these negative filters** to hide extension errors:

```
-runtime.lastError
-"Could not establish connection"
-"The message port closed"
-background.js
-content_script
-extension
-chrome-extension://
```

### Alternative: Use Incognito Mode

For clean development without extension interference:

1. **Open Chrome Incognito Window** (Ctrl+Shift+N / Cmd+Shift+N)
2. **Navigate to** `http://localhost:8081`
3. **Develop without extension noise**

### Create Development Profile

1. **Chrome Settings** → **Manage Profiles**
2. **Add New Profile** → "Development"
3. **Don't install extensions** in this profile
4. **Use for development work**

## Application-Specific Console Monitoring

To monitor only SEO Platform application logs:

### Filter for Application Messages
```
seo-platform
api/
/api/
localhost:8081
```

### Monitor Network Tab
- **Network tab** shows actual API calls
- **Filter by XHR/Fetch** for API requests
- **Check Response codes** for real issues

## Common Extension Error Patterns to Ignore

```javascript
// These are safe to ignore - they're from extensions:
runtime.lastError: The message port closed before a response was received
runtime.lastError: Could not establish connection. Receiving end does not exist
[LM] Lock monitor stopped, without clearing alarm
Could not get default saving location, resetting location
Unchecked runtime.lastError
chrome-extension:// errors
```

## Real Application Errors to Watch For

```javascript
// These indicate actual application issues:
Failed to fetch
404 Not Found
500 Internal Server Error
Uncaught TypeError
Uncaught ReferenceError
CORS errors from localhost:8081
```
